defmodule Repobot.Repo.Migrations.AddSyncModeToRepositories do
  use Ecto.Migration

  def change do
    execute "CREATE TYPE repository_sync_mode AS ENUM ('direct', 'pr')"

    alter table(:repositories) do
      add :sync_mode, :repository_sync_mode, default: "direct", null: false
    end
  end

  def down do
    alter table(:repositories) do
      remove :sync_mode
    end

    execute "DROP TYPE repository_sync_mode"
  end
end
