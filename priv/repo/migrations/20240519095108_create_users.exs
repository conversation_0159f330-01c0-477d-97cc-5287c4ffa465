defmodule Repobot.Repo.Migrations.CreateUsers do
  use Ecto.Migration

  def change do
    create table(:users, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :login, :string, null: false
      add :email, :string
      add :token, :string
      add :refresh_token, :string
      add :expires_at, :integer
      add :info, :jsonb

      timestamps(type: :utc_datetime)
    end

    create unique_index(:users, [:login])
  end
end
