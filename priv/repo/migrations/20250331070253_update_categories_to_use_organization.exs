defmodule Repobot.Repo.Migrations.UpdateCategoriesToUseOrganization do
  use Ecto.Migration

  def up do
    # First add the new organization_id column
    alter table(:categories) do
      add :organization_id, references(:organizations, type: :binary_id, on_delete: :delete_all)
    end

    # Create a function to get organization_id from user_id
    execute """
    CREATE OR REPLACE FUNCTION get_user_default_organization_id(user_id uuid)
    RETURNS uuid AS $$
    BEGIN
      RETURN (SELECT default_organization_id FROM users WHERE id = user_id);
    END;
    $$ LANGUAGE plpgsql;
    """

    # Update the organization_id based on user's default organization
    execute """
    UPDATE categories
    SET organization_id = get_user_default_organization_id(user_id)
    WHERE organization_id IS NULL;
    """

    # Drop the function as it's no longer needed
    execute "DROP FUNCTION get_user_default_organization_id;"

    # Drop the old user_id column and index
    drop index(:categories, [:user_id, :name])

    alter table(:categories) do
      remove :user_id
    end

    # Create new unique index for organization_id and name
    create unique_index(:categories, [:organization_id, :name],
             name: :categories_organization_id_name_index
           )
  end

  def down do
    # Add back the user_id column
    alter table(:categories) do
      add :user_id, references(:users, type: :binary_id, on_delete: :delete_all)
    end

    # Create a function to get user_id from organization_id
    execute """
    CREATE OR REPLACE FUNCTION get_organization_owner_id(org_id uuid)
    RETURNS uuid AS $$
    BEGIN
      RETURN (SELECT id FROM users WHERE default_organization_id = org_id LIMIT 1);
    END;
    $$ LANGUAGE plpgsql;
    """

    # Update the user_id based on organization's owner
    execute """
    UPDATE categories
    SET user_id = get_organization_owner_id(organization_id)
    WHERE user_id IS NULL;
    """

    # Drop the function as it's no longer needed
    execute "DROP FUNCTION get_organization_owner_id;"

    # Drop the new organization index
    drop index(:categories, [:organization_id, :name])

    # Drop the organization_id column
    alter table(:categories) do
      remove :organization_id
    end

    # Recreate the old index
    create unique_index(:categories, [:user_id, :name], name: :categories_user_id_name_index)
  end
end
