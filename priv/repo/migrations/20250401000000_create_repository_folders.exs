defmodule Repobot.Repo.Migrations.CreateRepositoryFolders do
  use Ecto.Migration

  def up do
    create table(:repository_folders, primary_key: false) do
      add :id, :binary_id, primary_key: true

      add :repository_id, references(:repositories, type: :binary_id, on_delete: :delete_all),
        null: false

      add :folder_id, references(:folders, type: :binary_id, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:repository_folders, [:repository_id, :folder_id])

    # Create a function to validate that only template repositories can be added
    execute """
    CREATE OR REPLACE FUNCTION check_template_repository()
    RETURNS TRIGGER AS $$
    BEGIN
      IF NOT EXISTS (
        SELECT 1 FROM repositories
        WHERE id = NEW.repository_id AND template = true
      ) THEN
        RAISE EXCEPTION 'Only template repositories can be added to multiple folders';
      END IF;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
    """

    # Create a trigger that runs the validation function before insert
    execute """
    CREATE TRIGGER ensure_template_repository
    BEFORE INSERT ON repository_folders
    FOR EACH ROW
    EXECUTE FUNCTION check_template_repository();
    """
  end

  def down do
    execute "DROP TRIGGER IF EXISTS ensure_template_repository ON repository_folders;"
    execute "DROP FUNCTION IF EXISTS check_template_repository;"
    drop table(:repository_folders)
  end
end
