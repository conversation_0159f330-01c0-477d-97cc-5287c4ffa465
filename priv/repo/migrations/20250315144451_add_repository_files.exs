defmodule Repobot.Repo.Migrations.AddRepositoryFiles do
  use Ecto.Migration

  def change do
    create table(:repository_files, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :path, :string, null: false
      add :name, :string, null: false
      add :type, :string, null: false
      add :size, :integer
      add :sha, :string
      add :content, :text
      add :content_updated_at, :utc_datetime

      add :repository_id, references(:repositories, on_delete: :delete_all, type: :binary_id),
        null: false

      timestamps(type: :utc_datetime)
    end

    create index(:repository_files, [:repository_id])

    create unique_index(:repository_files, [:repository_id, :path],
             name: :repository_files_repository_id_path_index
           )
  end
end
