defmodule Repobot.Repo.Migrations.CreateUsersOrganizations do
  use Ecto.Migration

  def change do
    create table(:users_organizations, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :user_id, references(:users, type: :binary_id, on_delete: :delete_all), null: false

      add :organization_id, references(:organizations, type: :binary_id, on_delete: :delete_all),
        null: false

      add :role, :string, null: false, default: "member"

      timestamps(type: :utc_datetime)
    end

    create index(:users_organizations, [:user_id])
    create index(:users_organizations, [:organization_id])

    create unique_index(:users_organizations, [:user_id, :organization_id],
             name: :user_id_organization_id_unique_index
           )
  end
end
