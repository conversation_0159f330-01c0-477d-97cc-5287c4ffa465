defmodule Repobot.Repo.Migrations.RemoveUserIdFromFolders do
  use Ecto.Migration

  def up do
    # First, update duplicate folder names for the specified user
    execute """
    UPDATE folders
    SET name = name || '_' || id
    WHERE user_id = '336331ff-098c-4ae6-9870-f68e58a046c9'
    AND name IN (
      SELECT name
      FROM folders
      GROUP BY name, organization_id
      HAVING COUNT(*) > 1
    )
    """

    # Then proceed with removing user_id
    drop_if_exists index(:folders, [:user_id, :starred])
    drop_if_exists index(:folders, [:user_id, :name, :organization_id])
    drop_if_exists index(:folders, [:user_id])

    alter table(:folders) do
      remove :user_id
    end

    create unique_index(:folders, [:name, :organization_id])
  end

  def down do
    drop_if_exists index(:folders, [:name, :organization_id])

    alter table(:folders) do
      add :user_id, references(:users, on_delete: :delete_all), null: true
    end

    create index(:folders, [:user_id])
    create index(:folders, [:user_id, :name, :organization_id])
    create index(:folders, [:user_id, :starred])
  end
end
