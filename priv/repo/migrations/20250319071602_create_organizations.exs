defmodule Repobot.Repo.Migrations.CreateOrganizations do
  use Ecto.Migration

  def change do
    create table(:organizations, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :settings, :map, default: %{}

      timestamps(type: :utc_datetime)
    end

    create unique_index(:organizations, [:name])

    # Create default organizations for existing users
    execute """
    INSERT INTO organizations (id, name, settings, inserted_at, updated_at)
    SELECT gen_random_uuid(), login, '{}', NOW(), NOW()
    FROM users;
    """

    alter table(:users) do
      add :default_organization_id, references(:organizations, type: :binary_id)
    end

    # Update users to reference their default organizations
    execute """
    UPDATE users u
    SET default_organization_id = o.id
    FROM organizations o
    WHERE u.login = o.name;
    """

    # Add organization_id to source_files table
    alter table(:source_files) do
      add :organization_id, references(:organizations, type: :binary_id)
    end

    # Update source_files to use the user's default organization
    execute """
    UPDATE source_files sf
    SET organization_id = u.default_organization_id
    FROM users u
    WHERE sf.user_id = u.id;
    """

    # Now make organization_id non-null
    execute """
    ALTER TABLE source_files ALTER COLUMN organization_id SET NOT NULL;
    """

    # Add organization_id to repositories table
    alter table(:repositories) do
      add :organization_id, references(:organizations, type: :binary_id)
    end

    # Update repositories to use the user's default organization (based on owner)
    execute """
    UPDATE repositories r
    SET organization_id = u.default_organization_id
    FROM users u
    WHERE r.owner = u.login;
    """

    # Now make organization_id non-null
    execute """
    ALTER TABLE repositories ALTER COLUMN organization_id SET NOT NULL;
    """

    # Add organization_id to folders table
    alter table(:folders) do
      add :organization_id, references(:organizations, type: :binary_id)
    end

    # Update folders to use the user's default organization
    execute """
    UPDATE folders f
    SET organization_id = u.default_organization_id
    FROM users u
    WHERE f.user_id = u.id;
    """

    # Now make organization_id non-null
    execute """
    ALTER TABLE folders ALTER COLUMN organization_id SET NOT NULL;
    """

    # Add organization_id to tags table
    alter table(:tags) do
      add :organization_id, references(:organizations, type: :binary_id)
    end

    # Update tags to use the user's default organization
    execute """
    UPDATE tags t
    SET organization_id = u.default_organization_id
    FROM users u
    WHERE t.user_id = u.id;
    """

    # Now make organization_id non-null
    execute """
    ALTER TABLE tags ALTER COLUMN organization_id SET NOT NULL;
    """
  end
end
