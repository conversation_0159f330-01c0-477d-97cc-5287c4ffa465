defmodule Repobot.Repo.Migrations.CreateTagsAndSourceFileTags do
  use Ecto.Migration

  def change do
    create table(:tags, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :color, :string, null: false
      add :user_id, references(:users, type: :binary_id, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:tags, [:name, :user_id])

    create table(:source_file_tags, primary_key: false) do
      add :source_file_id, references(:source_files, type: :binary_id, on_delete: :delete_all),
        null: false

      add :tag_id, references(:tags, type: :binary_id, on_delete: :delete_all), null: false
    end

    create unique_index(:source_file_tags, [:source_file_id, :tag_id])
  end
end
