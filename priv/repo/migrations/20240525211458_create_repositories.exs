defmodule Repobot.Repo.Migrations.CreateRepositories do
  use Ecto.Migration

  def change do
    create table(:repositories, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :owner, :string, null: false
      add :name, :string, null: false
      add :full_name, :string, null: false
      add :language, :string
      add :data, :jsonb

      timestamps(type: :utc_datetime)
    end
  end
end
