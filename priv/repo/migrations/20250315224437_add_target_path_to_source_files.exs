defmodule Repobot.Repo.Migrations.AddTargetPathToSourceFiles do
  use Ecto.Migration

  def change do
    alter table(:source_files) do
      add :target_path, :string
    end

    # Backfill existing records to have target_path same as name
    execute "UPDATE source_files SET target_path = name WHERE target_path IS NULL"

    # Make target_path required after backfill
    alter table(:source_files) do
      modify :target_path, :string, null: false
    end
  end
end
