defmodule Repobot.Repo.Migrations.CreateOrganizationSettings do
  use Ecto.Migration

  def up do
    create table(:organization_settings, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :anthropic_api_key, :binary
      add :openai_api_key, :binary
      add :avatar_url, :string
      add :html_url, :string
      add :organization_id, references(:organizations, type: :binary_id), null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:organization_settings, [:organization_id])

    alter table(:organizations) do
      remove :settings
    end
  end

  def down do
    alter table(:organizations) do
      add :settings, :map, default: %{}
    end

    drop table(:organization_settings)
  end
end
