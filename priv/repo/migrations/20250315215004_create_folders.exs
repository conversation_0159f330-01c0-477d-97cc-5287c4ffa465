defmodule Repobot.Repo.Migrations.CreateFolders do
  use Ecto.Migration

  def change do
    create table(:folders, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :user_id, references(:users, type: :binary_id), null: false
      add :source_files, {:array, :string}, default: [], null: false

      timestamps(type: :utc_datetime)
    end

    create index(:folders, [:user_id])
    create unique_index(:folders, [:user_id, :name])

    alter table(:repositories) do
      add :folder_id, references(:folders, type: :binary_id)
    end

    create index(:repositories, [:folder_id])
  end
end
