defmodule Repobot.Repo.Migrations.CreatePullRequests do
  use Ecto.Migration

  def change do
    create table(:pull_requests, primary_key: false) do
      add :id, :binary_id, primary_key: true

      add :source_file_id, references(:source_files, type: :binary_id, on_delete: :delete_all),
        null: false

      add :repository, :string, null: false
      add :branch_name, :string, null: false
      add :pull_request_number, :integer, null: false
      add :pull_request_url, :string, null: false
      add :status, :string, null: false, default: "open"

      timestamps(type: :utc_datetime)
    end

    create index(:pull_requests, [:source_file_id])
    create index(:pull_requests, [:repository])
    create unique_index(:pull_requests, [:repository, :pull_request_number])
  end
end
