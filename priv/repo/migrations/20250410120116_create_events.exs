defmodule Repobot.Repo.Migrations.CreateEvents do
  use Ecto.Migration

  def change do
    create table(:events, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :type, :string, null: false
      add :payload, :map, null: false
      add :status, :string

      add :organization_id, references(:organizations, type: :binary_id, on_delete: :delete_all),
        null: false

      add :repository_id, references(:repositories, type: :binary_id, on_delete: :nilify_all)

      add :user_id, references(:users, type: :binary_id, on_delete: :nilify_all)

      timestamps(type: :utc_datetime)
    end

    create index(:events, [:user_id])
    create index(:events, [:organization_id])
    create index(:events, [:repository_id])
    create index(:events, [:type])
    create index(:events, [:status])
    create index(:events, [:inserted_at])
  end
end
