defmodule Repobot.Repo.Migrations.AddInstallationIdToOrganizations do
  use Ecto.Migration

  def up do
    alter table(:organizations) do
      add :installation_id, :integer
    end

    # Migrate existing installation_id from settings
    execute """
    UPDATE organizations
    SET installation_id = (settings->>'installation_id')::integer
    WHERE settings->>'installation_id' IS NOT NULL
    """

    # Create an index for faster lookups
    create index(:organizations, [:installation_id])
  end

  def down do
    # Move installation_id back to settings before removing the column
    execute """
    UPDATE organizations
    SET settings = settings || jsonb_build_object('installation_id', installation_id)
    WHERE installation_id IS NOT NULL
    """

    alter table(:organizations) do
      remove :installation_id
    end
  end
end
