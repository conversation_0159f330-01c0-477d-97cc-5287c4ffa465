defmodule Repobot.Repo.Migrations.CreateRepositorySourceFiles do
  use Ecto.Migration

  def change do
    create table(:repository_source_files, primary_key: false) do
      add :id, :binary_id, primary_key: true

      add :repository_id, references(:repositories, type: :binary_id, on_delete: :delete_all),
        null: false

      add :source_file_id, references(:source_files, type: :binary_id, on_delete: :delete_all),
        null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:repository_source_files, [:repository_id, :source_file_id])
  end
end
