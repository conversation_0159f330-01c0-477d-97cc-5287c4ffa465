defmodule Repobot.Repo.Migrations.CreateSourceFileFolders do
  use Ecto.Migration

  def change do
    # Create the join table
    create table(:source_file_folders, primary_key: false) do
      add :id, :binary_id, primary_key: true

      add :source_file_id, references(:source_files, type: :binary_id, on_delete: :delete_all),
        null: false

      add :folder_id, references(:folders, type: :binary_id, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime)
    end

    # Create indexes
    create unique_index(:source_file_folders, [:source_file_id, :folder_id])
    create index(:source_file_folders, [:folder_id])

    # Migrate existing data
    execute(
      """
      INSERT INTO source_file_folders (id, source_file_id, folder_id, inserted_at, updated_at)
      SELECT gen_random_uuid(), id, folder_id, NOW(), NOW()
      FROM source_files
      WHERE folder_id IS NOT NULL
      """,
      ""
    )

    # Remove the folder_id column from source_files
    alter table(:source_files) do
      remove :folder_id
    end
  end
end
