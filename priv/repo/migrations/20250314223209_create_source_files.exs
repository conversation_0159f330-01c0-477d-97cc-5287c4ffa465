defmodule Repobot.Repo.Migrations.CreateSourceFiles do
  use Ecto.Migration

  def change do
    create table(:source_files, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :user_id, references(:users, type: :binary_id), null: false
      add :name, :string, null: false
      add :content, :text

      timestamps(type: :utc_datetime)
    end

    create index(:source_files, [:user_id])
    create unique_index(:source_files, [:user_id, :name])
  end
end
