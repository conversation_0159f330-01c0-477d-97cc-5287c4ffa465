#!/bin/bash
set -e

# Update system packages
sudo apt-get update -y

# Install required system dependencies
sudo apt-get install -y \
    build-essential \
    git \
    curl \
    wget \
    gnupg \
    lsb-release \
    ca-certificates \
    postgresql \
    postgresql-contrib \
    imagemagick \
    unzip \
    autoconf \
    libssl-dev \
    libncurses5-dev \
    libwxgtk3.0-gtk3-dev \
    libgl1-mesa-dev \
    libglu1-mesa-dev \
    libpng-dev \
    libssh-dev \
    xsltproc \
    fop \
    libxml2-utils

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install asdf version manager
git clone https://github.com/asdf-vm/asdf.git ~/.asdf --branch v0.14.0
echo '. "$HOME/.asdf/asdf.sh"' >> ~/.profile
echo '. "$HOME/.asdf/completions/asdf.bash"' >> ~/.profile

# Source asdf
. "$HOME/.asdf/asdf.sh"

# Add asdf plugins
asdf plugin add erlang https://github.com/asdf-vm/asdf-erlang.git
asdf plugin add elixir https://github.com/asdf-vm/asdf-elixir.git

# Install Erlang and Elixir
asdf install erlang 27.2.2
asdf install elixir 1.18.2-otp-27

# Set global versions
asdf global erlang 27.2.2
asdf global elixir 1.18.2-otp-27

# Verify installations
elixir --version
node --version
npm --version

# Install Hex and Rebar (Elixir package managers)
mix local.hex --force
mix local.rebar --force

# Start PostgreSQL service
sudo service postgresql start

# Configure PostgreSQL
sudo -u postgres createuser -s postgres || true
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'postgres';" || true

# Set environment variables for tests
export DATABASE_HOST=localhost
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=postgres
export POSTGRES_DB=repobot_test

source .env.test

# Install project dependencies
mix deps.get

# Install and setup assets
mix assets.setup

# Compile the project
mix compile

# Setup database and run migrations for test environment
mix ecto.setup
