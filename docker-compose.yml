services:
  base: &base
    build:
      context: .
      dockerfile: Dockerfile.dev
    working_dir: /workspace/repobot
    depends_on: [postgres]
    links: [postgres]
    entrypoint: bin/docker-entrypoint
    environment:
      DATABASE_HOST: postgres
    volumes:
      - .:/workspace/repobot

  dev:
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: dev
    env_file: [ .env.dev ]

  test:
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: test
    env_file: [ .env.test ]

  postgres:
    image: postgres:latest
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DATABASE: postgres
      POSTGRES_USERNAME: postgres
    ports:
      - 5432:5432
    volumes:
      - repobot_pgdata:/var/lib/postgresql/data
    command: [ "postgres", "-c", "log_statement=all" ]

volumes:
  repobot_pgdata:
