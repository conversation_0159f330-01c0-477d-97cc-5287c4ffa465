import cytoscape from 'cytoscape';

const SyncMap = {
  mounted() {
    this.cy = null;
    this.el.innerHTML = ""; // Clear loading message

    this.handleEvent("render_sync_map", ({ nodes, edges }) => {
      if (this.cy) {
        this.cy.destroy();
      }

      this.cy = cytoscape({
        container: this.el,
        elements: nodes.concat(edges), // Combine nodes and edges

        style: [
          {
            selector: 'node',
            style: {
              'label': 'data(label)',
              'text-valign': 'center',
              'text-halign': 'center',
              'text-wrap': 'wrap',        // Wrap text if needed
              'text-max-width': '80px',   // Max width before wrapping
              'background-color': '#ccc', // Default background
              'border-color': '#666',
              'border-width': 1,
              'padding': '10px',           // Padding inside node
              'width': 'label',            // Size node width to label
              'height': 'label',           // Size node height to label
              'font-size': '10px'
            }
          },
          {
            selector: 'edge',
            style: {
              'width': 1,
              'line-color': '#ccc',
              'target-arrow-color': '#ccc',
              'target-arrow-shape': 'triangle',
              'curve-style': 'bezier' // Use bezier curves for smoother edges
            }
          },
          // Group-specific styles
          {
            selector: '.template_repo',
            style: {
              'background-color': '#97C2FC',
              'border-color': '#2B7CE9',
              'shape': 'round-rectangle' // Changed shape
            }
          },
          {
            selector: '.target_repo',
            style: {
              'background-color': '#FFFFE0',
              'border-color': '#FFA500',
              'shape': 'ellipse'
            }
          },
          {
            selector: '.source_file',
            style: {
              'background-color': '#E0FFE0',
              'border-color': '#228B22',
              'shape': 'diamond',
              'font-size': '9px' // Slightly smaller for source files
            }
          }
        ],

        layout: {
          name: 'cose', // cose (Compound Spring Embedder) often handles compound nodes well
          animate: false,
          padding: 30,
          nodeOverlap: 10,
          idealEdgeLength: 100,
          nodeRepulsion: 400000,
          fit: true // Fit the graph to the viewport
        }
      });

      // Basic Tooltip Handling (optional)
      this.cy.on('mouseover', 'node', (event) => {
        const node = event.target;
        // console.log('Hover Node:', node.data('title'));
        // You could implement a custom tooltip div here
      });
      this.cy.on('mouseout', 'node', (event) => {
        // Hide tooltip
      });

    });
  },

  destroyed() {
    if (this.cy) {
      this.cy.destroy();
      this.cy = null;
    }
  }
};

export default SyncMap;
