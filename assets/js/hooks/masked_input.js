const MaskedInput = {
  mounted() {
    this.handleInput = this.handleInput.bind(this);
    this.el.addEventListener('focus', () => {
      this.el.value = this.el.value || '';  // Show real value when focused
    });
    this.el.addEventListener('blur', () => {
      const displayedValue = this.el.dataset.displayedValue;
      if (displayedValue) {
        this.el.value = displayedValue;  // Show masked value when blurred
      }
    });

    // Initially show masked value
    if (this.el.dataset.displayedValue) {
      this.el.value = this.el.dataset.displayedValue;
    }
  },

  destroyed() {
    this.el.removeEventListener('focus', this.handleInput);
    this.el.removeEventListener('blur', this.handleInput);
  }
}

export default MaskedInput;
