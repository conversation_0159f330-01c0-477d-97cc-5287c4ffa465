import hljs from 'highlight.js'

const SyntaxHighlight = {
  mounted() {
    this.highlightCode()
  },

  updated() {
    this.highlightCode()
  },

  highlightCode() {
    // Get the language from data attribute or detect from file extension
    const language = this.el.dataset.language
    const fileExtension = this.el.dataset.fileExtension
    
    // Find the code element within this hook's element
    const codeElement = this.el.querySelector('code')
    if (!codeElement) return

    // Clear any existing highlighting
    codeElement.className = ''
    
    // Determine language for highlighting
    let detectedLanguage = null
    
    if (language) {
      detectedLanguage = language
    } else if (fileExtension) {
      detectedLanguage = this.getLanguageFromExtension(fileExtension)
    }

    try {
      if (detectedLanguage && hljs.getLanguage(detectedLanguage)) {
        // Use specific language
        const result = hljs.highlight(codeElement.textContent, { language: detectedLanguage })
        codeElement.innerHTML = result.value
        codeElement.className = `hljs language-${detectedLanguage}`
      } else {
        // Auto-detect language
        const result = hljs.highlightAuto(codeElement.textContent)
        codeElement.innerHTML = result.value
        codeElement.className = `hljs language-${result.language || 'plaintext'}`
      }
    } catch (error) {
      console.warn('Syntax highlighting failed:', error)
      // Fallback: just add basic hljs class for styling
      codeElement.className = 'hljs language-plaintext'
    }
  },

  getLanguageFromExtension(extension) {
    // Remove leading dot if present
    const ext = extension.startsWith('.') ? extension.slice(1) : extension
    
    // Map file extensions to highlight.js language identifiers
    const extensionMap = {
      // Web technologies
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'html': 'html',
      'htm': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'less': 'less',
      'json': 'json',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      
      // Programming languages
      'py': 'python',
      'rb': 'ruby',
      'php': 'php',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'cc': 'cpp',
      'cxx': 'cpp',
      'h': 'c',
      'hpp': 'cpp',
      'cs': 'csharp',
      'go': 'go',
      'rs': 'rust',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'clj': 'clojure',
      'ex': 'elixir',
      'exs': 'elixir',
      'erl': 'erlang',
      'hrl': 'erlang',
      
      // Shell and config
      'sh': 'bash',
      'bash': 'bash',
      'zsh': 'bash',
      'fish': 'bash',
      'ps1': 'powershell',
      'bat': 'batch',
      'cmd': 'batch',
      
      // Data and config
      'toml': 'toml',
      'ini': 'ini',
      'cfg': 'ini',
      'conf': 'ini',
      'env': 'bash',
      
      // Documentation
      'md': 'markdown',
      'markdown': 'markdown',
      'rst': 'rst',
      'tex': 'latex',
      
      // Database
      'sql': 'sql',
      
      // Docker and infrastructure
      'dockerfile': 'dockerfile',
      'dockerignore': 'ignore',
      'gitignore': 'ignore',
      
      // Template files
      'liquid': 'liquid',
      'hbs': 'handlebars',
      'mustache': 'handlebars',
      'twig': 'twig',
      
      // Other
      'r': 'r',
      'R': 'r',
      'matlab': 'matlab',
      'm': 'matlab',
      'pl': 'perl',
      'lua': 'lua',
      'vim': 'vim'
    }
    
    return extensionMap[ext.toLowerCase()] || null
  }
}

export default SyntaxHighlight
