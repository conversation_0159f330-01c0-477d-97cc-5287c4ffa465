export const RepositoryList = {
  mounted() {
    this.el.addEventListener('click', e => {
      const repoEl = e.target.closest('[data-repository]');
      if (!repoEl) return;

      const sourceFilesEl = repoEl.querySelector('[data-source-files]');
      if (!sourceFilesEl) return;

      const isExpanded = repoEl.getAttribute('aria-expanded') === 'true';
      repoEl.setAttribute('aria-expanded', !isExpanded);

      sourceFilesEl.classList.toggle('hidden');

      const chevronEl = repoEl.querySelector('[data-chevron]');
      if (chevronEl) {
        chevronEl.style.transform = isExpanded ? 'rotate(0deg)' : 'rotate(90deg)';
      }
    });
  }
}
