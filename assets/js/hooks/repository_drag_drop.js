import Sortable from 'sortablejs';

const RepositoryDragDrop = {
  mounted() {
    // Initialize sortable for folders (the container that holds all folder cards)
    this.initFoldersSortable();

    // Initialize sortable for repositories within each folder
    this.initRepositoriesSortable();
  },

  initFoldersSortable() {
    // We're not implementing folder reordering in this version
    // This is just a placeholder for future enhancement
  },

  initRepositoriesSortable() {
    // Get all repository lists within folders
    const folderContents = document.querySelectorAll('[data-folder-content]');

    // Store sortable instances to access them later if needed
    this.sortableInstances = [];

    folderContents.forEach(folderContent => {
      const folderId = folderContent.getAttribute('data-folder-id');

      // Create a sortable instance for each folder's repository list
      const sortable = new Sortable(folderContent, {
        group: 'repositories', // Repositories can be moved between folders with the same group name
        animation: 150, // Animation speed in ms
        ghostClass: 'bg-indigo-50', // Class applied to the ghost element (visual feedback)
        chosenClass: 'bg-indigo-100', // Class applied to the chosen item
        dragClass: 'opacity-70', // Class applied to the dragged item
        handle: '[data-drag-handle]', // Drag handle selector within the draggable element

        // Called when an item is dropped
        onEnd: (evt) => {
          // Only process if the item was actually moved to a different folder
          if (evt.from !== evt.to) {
            const repositoryId = evt.item.getAttribute('data-repository-id');
            const templateRepo = evt.item.getAttribute('data-template-repo') === 'true';
            const fromFolderId = evt.from.getAttribute('data-folder-id');
            const toFolderId = evt.to.getAttribute('data-folder-id');

            // If moving to the unorganized section (null folder)
            if (toFolderId === 'unorganized') {
              // For template repos, we need to remove from the specific folder
              if (templateRepo) {
                this.pushEventTo(`#folder-${fromFolderId}`, 'remove_from_folder', {
                  repository_id: repositoryId,
                  folder_id: fromFolderId
                });
              } else {
                // For regular repos, just remove from any folder
                this.pushEventTo(`#folder-${fromFolderId}`, 'remove_from_folder', {
                  repository_id: repositoryId
                });
              }
            } else {
              // Moving to another folder
              this.pushEventTo(`#folder-${toFolderId}`, 'move_to_folder', {
                repository_id: repositoryId,
                folder_id: toFolderId,
                template_repo: templateRepo
              });
            }
          }
        }
      });

      this.sortableInstances.push(sortable);
    });

    // Make the unorganized repositories section a valid drop target
    const unorganizedSection = document.querySelector('[data-unorganized-section]');
    if (unorganizedSection) {
      const unorganizedSortable = new Sortable(unorganizedSection, {
        group: 'repositories',
        animation: 150,
        ghostClass: 'bg-slate-50',
        chosenClass: 'bg-slate-100',
        dragClass: 'opacity-70',
        handle: '[data-drag-handle]',

        // Called when an item is dropped in the unorganized section
        onAdd: (evt) => {
          const repositoryId = evt.item.getAttribute('data-repository-id');
          const templateRepo = evt.item.getAttribute('data-template-repo') === 'true';
          const fromFolderId = evt.from.getAttribute('data-folder-id');

          // For template repos, we need to remove from the specific folder
          if (templateRepo) {
            this.pushEventTo(`#folder-${fromFolderId}`, 'remove_from_folder', {
              repository_id: repositoryId,
              folder_id: fromFolderId
            });
          } else {
            // For regular repos, just remove from any folder
            this.pushEventTo(`#folder-${fromFolderId}`, 'remove_from_folder', {
              repository_id: repositoryId
            });
          }
        }
      });

      this.sortableInstances.push(unorganizedSortable);
    }
  },

  // Helper method to push events to a specific element
  pushEventTo(selector, event, payload) {
    const target = document.querySelector(selector);
    if (target) {
      this.pushEvent(event, payload);
    } else {
      console.error(`Target element not found: ${selector}`);
    }
  }
};

export default RepositoryDragDrop;
