# fly.toml app configuration file generated for repobot on 2025-03-20T07:46:47Z
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'repobot'
primary_region = 'fra'
kill_signal = 'SIGTERM'

[build]

[deploy]
release_command = '/app/bin/migrate'

[env]
PHX_HOST = 'repobot.app'
PORT = '8080'

[http_service]
internal_port = 8080
force_https = true
auto_stop_machines = 'stop'
auto_start_machines = true
min_machines_running = 1
processes = ['app']

[http_service.concurrency]
type = 'connections'
hard_limit = 1000
soft_limit = 1000

[[vm]]
memory = '1gb'
cpu_kind = 'shared'
cpus = 1
