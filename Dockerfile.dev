ARG ELIXIR_VERSION=1.18.2
ARG OTP_VERSION=27.2.2
ARG DEBIAN_VERSION=bullseye-20250203-slim

ARG IMAGE="hexpm/elixir:${ELIXIR_VERSION}-erlang-${OTP_VERSION}-debian-${DEBIAN_VERSION}"

FROM ${IMAGE} AS base

RUN apt-get update -y && \
  apt-get install -y --no-install-recommends git bash curl less git gnupg inotify-tools postgresql-client ssh && \
  apt-get clean && \
  rm -f /var/lib/apt/lists/*_*

WORKDIR /workspace/repobot

COPY mix.exs mix.lock ./

RUN mix local.hex --force && \
  mix local.rebar --force

FROM base AS dev

ENV FLYCTL_INSTALL="/usr/local/src/fly"
ENV PATH="$FLYCTL_INSTALL/bin:$PATH"

RUN curl -L https://fly.io/install.sh | sh

COPY .env.dev .env.dev

RUN mix deps.get

FROM base AS test

ENV MIX_ENV="test"
