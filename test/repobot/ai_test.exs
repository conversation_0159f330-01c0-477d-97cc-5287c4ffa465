defmodule Repobot.AITest do
  use Repobot.DataCase

  alias <PERSON>obot.AI
  alias Repobot.Accounts.{Organization, OrganizationSettings}

  describe "backend/1 in test environment" do
    test "always returns the configured mock in test environment" do
      # In test environment, should always return the mock regardless of API keys
      assert AI.backend() == Repobot.Test.AIMock
    end

    test "returns mock even with organization API keys in test environment" do
      organization = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "org-anthropic-key",
          openai_api_key: "org-openai-key"
        }
      }

      # Should still return mock in test environment
      assert AI.backend(organization) == Repobot.Test.AIMock
    end
  end

  describe "backend selection logic (unit tests)" do
    # These tests verify the internal logic without actually calling backend/1
    # since that always returns the mock in test environment

    test "organization key detection works correctly" do
      organization_with_anthropic = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "org-anthropic-key",
          openai_api_key: nil
        }
      }

      organization_with_openai = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: nil,
          openai_api_key: "org-openai-key"
        }
      }

      organization_with_both = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "org-anthropic-key",
          openai_api_key: "org-openai-key"
        }
      }

      organization_with_empty = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "",
          openai_api_key: ""
        }
      }

      organization_with_nil = %Organization{settings: nil}

      assert AI.test_has_organization_anthropic_key?(organization_with_anthropic) == true
      assert AI.test_has_organization_openai_key?(organization_with_anthropic) == false

      assert AI.test_has_organization_anthropic_key?(organization_with_openai) == false
      assert AI.test_has_organization_openai_key?(organization_with_openai) == true

      assert AI.test_has_organization_anthropic_key?(organization_with_both) == true
      assert AI.test_has_organization_openai_key?(organization_with_both) == true

      assert AI.test_has_organization_anthropic_key?(organization_with_empty) == false
      assert AI.test_has_organization_openai_key?(organization_with_empty) == false

      assert AI.test_has_organization_anthropic_key?(organization_with_nil) == false
      assert AI.test_has_organization_openai_key?(organization_with_nil) == false
    end

    test "backend selection prioritizes correctly" do
      # Test with organization having Anthropic key
      org_with_anthropic = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "org-anthropic-key",
          openai_api_key: nil
        }
      }

      assert AI.test_select_backend_by_api_key_availability(org_with_anthropic) ==
               Repobot.AI.Anthropic

      # Test with organization having both keys (should prioritize Anthropic)
      org_with_both = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "org-anthropic-key",
          openai_api_key: "org-openai-key"
        }
      }

      assert AI.test_select_backend_by_api_key_availability(org_with_both) == Repobot.AI.Anthropic

      # Test with organization having only OpenAI key
      org_with_openai = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: nil,
          openai_api_key: "org-openai-key"
        }
      }

      assert AI.test_select_backend_by_api_key_availability(org_with_openai) == Repobot.AI.OpenAI

      # Test with no organization keys
      org_with_no_keys = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: nil,
          openai_api_key: nil
        }
      }

      assert AI.test_select_backend_by_api_key_availability(org_with_no_keys) == nil
    end
  end

  describe "backend/1 in production environment" do
    setup do
      # Temporarily override the environment setting to simulate production
      original_env = Application.get_env(:repobot, :env)
      Application.put_env(:repobot, :env, :prod)

      on_exit(fn ->
        Application.put_env(:repobot, :env, original_env)
      end)

      :ok
    end

    test "returns no-op backend when no API keys are available" do
      # Test with organization having no API keys
      org_with_no_keys = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: nil,
          openai_api_key: nil
        }
      }

      # Should return no-op backend when no API keys are available
      assert AI.backend(org_with_no_keys) == Repobot.AI.NoOp
    end

    test "returns correct backend based on organization API keys" do
      # Test with organization having Anthropic key
      org_with_anthropic = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: "org-anthropic-key",
          openai_api_key: nil
        }
      }

      assert AI.backend(org_with_anthropic) == Repobot.AI.Anthropic

      # Test with organization having OpenAI key
      org_with_openai = %Organization{
        settings: %OrganizationSettings{
          anthropic_api_key: nil,
          openai_api_key: "org-openai-key"
        }
      }

      assert AI.backend(org_with_openai) == Repobot.AI.OpenAI
    end

    test "returns no-op backend when organization is nil" do
      assert AI.backend(nil) == Repobot.AI.NoOp
    end
  end

  describe "static_backend/0" do
    test "returns the configured backend" do
      # In test environment, this should be AIMock
      assert AI.static_backend() == Repobot.Test.AIMock
    end
  end
end
