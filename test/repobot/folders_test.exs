defmodule Repobot.FoldersTest do
  use Repobot.DataCase

  alias Repobot.Folders
  alias Repobot.Test.Fixtures

  describe "folders" do
    test "list_folders/1 returns all folders for an organization" do
      user = Fixtures.create_user()
      other_org = Fixtures.organization_fixture()

      # Create folders in different organizations
      folder1 =
        Fixtures.create_folder(%{organization_id: user.default_organization_id, name: "Folder 1"})

      folder2 = Fixtures.create_folder(%{organization_id: other_org.id, name: "Folder 2"})

      # List folders for default organization
      folders = Folders.list_folders(user, nil, user.default_organization_id)
      assert length(folders) == 1
      assert hd(folders).id == folder1.id

      # List folders for other organization
      folders = Folders.list_folders(user, nil, other_org.id)
      assert length(folders) == 1
      assert hd(folders).id == folder2.id
    end

    test "list_folders/2 with search filters repositories by name" do
      user = Fixtures.create_user()

      folder =
        Fixtures.create_folder(%{
          organization_id: user.default_organization_id,
          name: "Test Folder"
        })

      repo1 =
        Fixtures.create_repository(%{
          folder_id: folder.id,
          owner: "user",
          name: "repo-abc",
          full_name: "user/repo-abc",
          organization_id: user.default_organization_id
        })

      _repo2 =
        Fixtures.create_repository(%{
          folder_id: folder.id,
          owner: "user",
          name: "repo-xyz",
          full_name: "user/repo-xyz",
          organization_id: user.default_organization_id
        })

      folders = Folders.list_folders(user, "abc", user.default_organization_id)
      assert length(folders) == 1
      [folder_with_repos] = folders
      assert length(folder_with_repos.repositories) == 1
      assert hd(folder_with_repos.repositories).id == repo1.id
    end

    test "users in same organization can see each other's folders" do
      # Create first user and organization
      user1 = Fixtures.create_user()
      org = user1.default_organization

      # Create second user in the same organization
      user2 = Fixtures.create_user(%{login: "other-user"})
      {:ok, _} = Repobot.Accounts.add_user_to_organization(user2, org, "member")

      # Create a folder in the organization
      folder = Fixtures.create_folder(%{organization_id: org.id, name: "Shared Folder"})

      # Both users should see the folder
      user1_folders = Folders.list_folders(user1, nil, org.id)
      user2_folders = Folders.list_folders(user2, nil, org.id)

      assert length(user1_folders) == 1
      assert length(user2_folders) == 1
      assert hd(user1_folders).id == folder.id
      assert hd(user2_folders).id == folder.id
    end

    test "get_folder!/1 returns the folder with given id" do
      user = Fixtures.create_user()

      folder =
        Fixtures.create_folder(%{
          organization_id: user.default_organization_id,
          name: "Test Folder"
        })

      found_folder = Folders.get_folder!(folder.id)
      assert found_folder.id == folder.id
      assert found_folder.name == "Test Folder"
    end

    test "create_folder/1 with valid data creates a folder" do
      user = Fixtures.create_user()

      valid_attrs = %{
        name: "Test Folder",
        organization_id: user.default_organization_id
      }

      assert {:ok, folder} = Folders.create_folder(valid_attrs)
      assert folder.name == "Test Folder"
    end

    test "update_folder/2 with valid data updates the folder" do
      user = Fixtures.create_user()

      folder =
        Fixtures.create_folder(%{
          organization_id: user.default_organization_id,
          name: "Test Folder"
        })

      update_attrs = %{name: "Updated Folder"}

      assert {:ok, folder} = Folders.update_folder(folder, update_attrs)
      assert folder.name == "Updated Folder"
    end

    test "add_repository_to_folder/2 adds the repository to the folder" do
      user = Fixtures.create_user()

      folder =
        Fixtures.create_folder(%{
          organization_id: user.default_organization_id,
          name: "Test Folder"
        })

      repository =
        Fixtures.create_repository(%{
          owner: "test-user",
          name: "test-repo",
          full_name: "test-user/test-repo",
          organization_id: user.default_organization_id
        })

      assert {:ok, updated_repo} = Folders.add_repository_to_folder(repository, folder)
      assert updated_repo.folder_id == folder.id
    end

    test "remove_repository_from_folder/1 removes the repository from its folder" do
      user = Fixtures.create_user()

      folder =
        Fixtures.create_folder(%{
          organization_id: user.default_organization_id,
          name: "Test Folder"
        })

      repository =
        Fixtures.create_repository(%{
          folder_id: folder.id,
          owner: "test-user",
          name: "test-repo",
          full_name: "test-user/test-repo",
          organization_id: user.default_organization_id
        })

      assert {:ok, updated_repo} = Folders.remove_repository_from_folder(repository)
      assert updated_repo.folder_id == nil
    end

    test "toggle_starred/1 toggles the starred status of a folder" do
      user = Fixtures.create_user()

      folder =
        Fixtures.create_folder(%{
          organization_id: user.default_organization_id,
          name: "Test Folder",
          starred: false
        })

      assert {:ok, updated_folder} = Folders.toggle_starred(folder)
      assert updated_folder.starred == true

      assert {:ok, toggled_back} = Folders.toggle_starred(updated_folder)
      assert toggled_back.starred == false
    end
  end
end
