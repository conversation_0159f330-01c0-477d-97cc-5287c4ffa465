defmodule Repobot.AccountsTest do
  use Repobot.DataCase

  import ExUnit.CaptureLog
  import Mox

  alias <PERSON>obot.Accounts
  alias Repobot.Accounts.{User, Organization, OrganizationSettings, UserOrganization}
  alias Repobot.Test.GitHubMock

  # Setup verification that all mocks were called
  setup :verify_on_exit!

  describe "user_from_auth/1" do
    test "creates a new user and associates with organizations they are a member of" do
      auth = %{
        info: %{
          name: "Test User",
          email: "<EMAIL>",
          nickname: "testuser",
          description: "Test description",
          image: "https://example.com/avatar.jpg",
          urls: %{"html_url" => "https://github.com/testuser"},
          location: "Test Location"
        },
        credentials: %{
          token: "test_token",
          refresh_token: "test_refresh_token",
          expires_at: 1_234_567_890
        }
      }

      installations = [
        %{
          "id" => 1,
          "account" => %{
            "login" => "org1",
            "type" => "Organization",
            "avatar_url" => "https://example.com/org1.jpg",
            "html_url" => "https://github.com/org1"
          }
          # Role removed, will be fetched via membership check
        },
        %{
          "id" => 2,
          "account" => %{
            "login" => "org2",
            "type" => "Organization",
            "avatar_url" => "https://example.com/org2.jpg",
            "html_url" => "https://github.com/org2"
          }
        },
        %{
          "id" => 3,
          "account" => %{
            "login" => "org3_not_member",
            "type" => "Organization",
            "avatar_url" => "https://example.com/org3.jpg",
            "html_url" => "https://github.com/org3"
          }
        },
        %{
          "id" => 4,
          "account" => %{
            # Should be skipped as type != Organization
            "login" => "user_account_skip",
            "type" => "User",
            "avatar_url" => "https://example.com/user.jpg",
            "html_url" => "https://github.com/user"
          }
        }
      ]

      # Mock GitHub API calls
      expect(GitHubMock, :client, fn _user -> "mock_client" end)

      expect(GitHubMock, :list_user_installations, fn _client ->
        {:ok, installations}
      end)

      # Note: get_organization_membership is NOT called for user_account_skip

      now = System.system_time(:second)
      assert {:ok, user} = Accounts.user_from_auth(auth)
      assert user.login == "testuser"
      assert user.token == "test_token"
      assert user.refresh_token == "test_refresh_token"
      assert user.expires_at == 1_234_567_890
      # Should be ~6 months from now
      assert_in_delta user.refresh_token_expires_at, now + 15_897_600, 1
      assert user.info.name == "Test User"
      assert user.info.email == "<EMAIL>"
      assert user.info.nickname == "testuser"
      assert user.info.description == "Test description"
      assert user.info.avatar_url == "https://example.com/avatar.jpg"
      assert user.info.html_url == "https://github.com/testuser"
      assert user.info.location == "Test Location"

      # Verify organizations and associations
      organizations = Repo.all(from o in Organization, preload: [:settings, :users])
      # Personal, org1, org2, org3_not_member are created now
      assert length(organizations) == 4

      # Find personal organization
      personal_org = Enum.find(organizations, &(&1.id == user.default_organization_id))
      assert personal_org.name == "testuser"
      assert personal_org.settings.avatar_url == "https://example.com/avatar.jpg"
      assert personal_org.settings.html_url == "https://github.com/testuser"
      # Check user association for personal org via UserOrganization
      user_org =
        Repo.get_by(UserOrganization,
          organization_id: personal_org.id,
          user_id: user.id
        )

      assert user_org != nil
      assert user_org.role == "admin"

      # Find org1
      org1 = Enum.find(organizations, &(&1.name == "org1"))
      assert org1.settings.avatar_url == "https://example.com/org1.jpg"
      # Verify user association and role for org1
      user_org1 =
        Repo.get_by!(UserOrganization, organization_id: org1.id, user_id: user.id)

      # Using the new implementation roles are always "member"
      assert user_org1.role == "member"

      # Find org2
      org2 = Enum.find(organizations, &(&1.name == "org2"))
      assert org2.settings.avatar_url == "https://example.com/org2.jpg"
      # Verify user association and role for org2
      user_org2 =
        Repo.get_by!(UserOrganization, organization_id: org2.id, user_id: user.id)

      assert user_org2.role == "member"

      # We now create the org3 as well
      org3 = Enum.find(organizations, &(&1.name == "org3_not_member"))
      assert org3 != nil
      assert org3.settings.avatar_url == "https://example.com/org3.jpg"
      # Verify user association and role for org3
      user_org3 =
        Repo.get_by!(UserOrganization, organization_id: org3.id, user_id: user.id)

      assert user_org3.role == "member"

      # Assert total number of associations for the user is 4 (personal org, org1, org2, and org3)
      query = from uo in UserOrganization, where: uo.user_id == ^user.id
      assert Repo.aggregate(query, :count, :id) == 4
    end

    test "updates existing user and their organization associations" do
      # Setup existing user and personal organization
      personal_org = organization_fixture(%{name: "testuser"})

      existing_user =
        Repo.insert!(%User{
          login: "testuser",
          token: "old_token",
          refresh_token: "old_refresh_token",
          expires_at: 1_111_111_111,
          default_organization_id: personal_org.id,
          info: %{
            name: "Old Name",
            email: "<EMAIL>",
            nickname: "testuser",
            description: "Old description",
            avatar_url: "https://example.com/old_avatar.jpg",
            html_url: "https://github.com/testuser",
            location: "Old Location"
          }
        })

      # Setup existing org1 and association (e.g., user was a member before)
      org1 = organization_fixture(%{name: "org1"})

      Repo.insert!(%UserOrganization{
        user_id: existing_user.id,
        organization_id: org1.id,
        role: "member"
      })

      auth = %{
        info: %{
          name: "Updated User",
          email: "<EMAIL>",
          nickname: "testuser",
          description: "Updated description",
          image: "https://example.com/new_avatar.jpg",
          urls: %{"html_url" => "https://github.com/testuser"},
          location: "Updated Location"
        },
        credentials: %{
          token: "new_token",
          refresh_token: "new_refresh_token",
          expires_at: 2_147_483_647
        }
      }

      installations = [
        %{
          "id" => 1,
          "account" => %{
            # Existing org
            "login" => "org1",
            "type" => "Organization",
            "avatar_url" => "https://example.com/org1_updated.jpg",
            "html_url" => "https://github.com/org1_updated"
          }
        },
        %{
          "id" => 2,
          "account" => %{
            # New org for this user
            "login" => "org_new",
            "type" => "Organization",
            "avatar_url" => "https://example.com/org_new.jpg",
            "html_url" => "https://github.com/org_new"
          }
        }
      ]

      # Mock GitHub API calls
      expect(GitHubMock, :client, fn _user -> "mock_client" end)

      expect(GitHubMock, :list_user_installations, fn _client ->
        {:ok, installations}
      end)

      # With our new implementation, we don't call get_organization_membership anymore
      # Remove these expects
      # expect(GitHubMock, :get_organization_membership, fn _client, "org1", "testuser" ->
      #   # Role changed
      #   {:ok, %{"state" => "active", "role" => "admin"}}
      # end)

      # expect(GitHubMock, :get_organization_membership, fn _client, "org_new", "testuser" ->
      #   {:ok, %{"state" => "active", "role" => "member"}}
      # end)

      now = System.system_time(:second)
      assert {:ok, updated_user} = Accounts.user_from_auth(auth)
      assert updated_user.id == existing_user.id
      assert updated_user.login == "testuser"
      assert updated_user.token == "new_token"
      assert updated_user.refresh_token == "new_refresh_token"
      assert updated_user.expires_at == 2_147_483_647
      # Should be ~6 months from now
      assert_in_delta updated_user.refresh_token_expires_at, now + 15_897_600, 1
      assert updated_user.info.name == "Updated User"
      assert updated_user.info.email == "<EMAIL>"
      assert updated_user.info.nickname == "testuser"
      assert updated_user.info.description == "Updated description"
      assert updated_user.info.avatar_url == "https://example.com/new_avatar.jpg"
      assert updated_user.info.html_url == "https://github.com/testuser"
      assert updated_user.info.location == "Updated Location"

      assert updated_user.default_organization_id == personal_org.id

      # Verify GitHub App organizations were created/updated and associated correctly
      organizations = Repo.all(from o in Organization, preload: [:settings, :users])
      # Personal, org1, org_new
      assert length(organizations) == 3

      # Verify org1 association updated
      org1_updated = Enum.find(organizations, &(&1.name == "org1"))
      assert org1_updated.settings.avatar_url == "https://example.com/org1_updated.jpg"

      user_org1 =
        Repo.get_by!(UserOrganization, organization_id: org1_updated.id, user_id: updated_user.id)

      # Role is always "member" with our new implementation
      assert user_org1.role == "member"

      # Verify org_new created and associated
      org_new = Enum.find(organizations, &(&1.name == "org_new"))
      assert org_new.settings.avatar_url == "https://example.com/org_new.jpg"

      user_org_new =
        Repo.get_by!(UserOrganization, organization_id: org_new.id, user_id: updated_user.id)

      assert user_org_new.role == "member"
    end

    test "handles GitHub API errors gracefully during installation fetch" do
      auth = %{
        info: %{
          name: "Test User",
          email: "<EMAIL>",
          nickname: "testuser",
          description: "Test description",
          image: "https://example.com/avatar.jpg",
          urls: %{"html_url" => "https://github.com/testuser"},
          location: "Test Location"
        },
        credentials: %{
          token: "test_token",
          refresh_token: "test_refresh_token",
          expires_at: 1_234_567_890
        }
      }

      # Mock GitHub API calls
      expect(GitHubMock, :client, fn _user -> "mock_client" end)

      # Simulate error fetching installations
      expect(GitHubMock, :list_user_installations, fn _client ->
        {:error, "API rate limit exceeded"}
      end)

      # Membership check should NOT be called if installation fetch fails
      # expect(GitHubMock, :get_organization_membership, 0) # Verify it's not called

      log =
        capture_log(fn ->
          assert {:ok, user} = Accounts.user_from_auth(auth)
          assert user.login == "testuser"

          # Verify personal organization was still created
          personal_org =
            Repo.get!(Organization, user.default_organization_id) |> Repo.preload(:settings)

          assert personal_org.name == "testuser"
          assert personal_org.settings.avatar_url == "https://example.com/avatar.jpg"
          assert personal_org.settings.html_url == "https://github.com/testuser"

          # Verify no other organizations or associations were created
          assert Repo.all(from o in Organization, where: o.id != ^personal_org.id) == []

          # Verify only the personal organization association exists
          user_orgs = Repo.all(UserOrganization)
          assert length(user_orgs) == 1
          assert hd(user_orgs).organization_id == personal_org.id
          assert hd(user_orgs).user_id == user.id
        end)

      # Fix the assertion to match the actual log message pattern
      assert log =~
               "Failed to fetch GitHub App installations for testuser: API rate limit exceeded"
    end

    test "handles GitHub API errors gracefully during membership check" do
      auth = %{
        info: %{
          name: "Test User",
          email: "<EMAIL>",
          nickname: "testuser",
          description: "Test description",
          image: "https://example.com/avatar.jpg",
          urls: %{"html_url" => "https://github.com/testuser"},
          location: "Test Location"
        },
        credentials: %{
          token: "test_token",
          refresh_token: "test_refresh_token",
          expires_at: 1_234_567_890
        }
      }

      installations = [
        %{
          "id" => 1,
          "account" => %{
            "login" => "org_error",
            "type" => "Organization",
            "avatar_url" => "https://example.com/org_error.jpg",
            "html_url" => "https://github.com/org_error"
          }
        }
      ]

      # Mock GitHub API calls
      expect(GitHubMock, :client, fn _user -> "mock_client" end)

      expect(GitHubMock, :list_user_installations, fn _client ->
        {:ok, installations}
      end)

      assert {:ok, user} = Accounts.user_from_auth(auth)
      assert user.login == "testuser"

      # Verify personal organization was still created
      personal_org =
        Repo.get!(Organization, user.default_organization_id) |> Repo.preload(:settings)

      assert personal_org.name == "testuser"

      # With the new implementation, the org_error IS created
      org_error = Repo.get_by(Organization, name: "org_error")
      assert org_error != nil

      # Verify both the personal organization and org_error associations exist
      user_orgs = Repo.all(UserOrganization)
      assert length(user_orgs) == 2

      # One association should be for personal org
      assert Enum.any?(user_orgs, fn uo ->
               uo.organization_id == personal_org.id && uo.user_id == user.id
             end)

      # One association should be for org_error
      assert Enum.any?(user_orgs, fn uo ->
               uo.organization_id == org_error.id && uo.user_id == user.id &&
                 uo.role == "member"
             end)
    end

    test "creates organization with valid settings during user sign-up" do
      auth = %{
        info: %{
          name: "Test User",
          email: "<EMAIL>",
          nickname: "test_settings_user",
          description: "Test description",
          image: "https://example.com/avatar.jpg",
          urls: %{"html_url" => "https://github.com/test_settings_user"},
          location: "Test Location"
        },
        credentials: %{
          token: "test_token",
          refresh_token: "test_refresh_token",
          expires_at: 1_234_567_890
        }
      }

      # Mock GitHub API calls - minimal needed for this test
      expect(GitHubMock, :client, fn _user -> "mock_client" end)
      expect(GitHubMock, :list_user_installations, fn _client -> {:ok, []} end)

      assert {:ok, user} = Accounts.user_from_auth(auth)
      assert user.login == "test_settings_user"

      # Verify the default organization was created
      organization = Repo.get(Organization, user.default_organization_id)
      assert organization.name == "test_settings_user"

      # Verify organization settings exist and are properly associated
      settings = Repo.get_by(OrganizationSettings, organization_id: organization.id)
      assert settings != nil
      assert settings.avatar_url == "https://example.com/avatar.jpg"
      assert settings.html_url == "https://github.com/test_settings_user"

      # Verify organization can be loaded with settings via preload
      organization_with_settings = Repo.preload(organization, :settings)
      assert organization_with_settings.settings.id == settings.id

      # Verify user-organization association was created
      user_org =
        Repo.get_by(UserOrganization,
          user_id: user.id,
          organization_id: organization.id
        )

      assert user_org != nil
      assert user_org.role == "admin"

      # Verify user can access organizations through association
      user_with_orgs = Repo.preload(user, :organizations)
      assert length(user_with_orgs.organizations) == 1
      assert hd(user_with_orgs.organizations).id == organization.id
    end

    test "sets installation_id for default organization when user installs GitHub app" do
      # Create a mock auth struct
      github_login = "testuser"
      installation_id = 12345

      auth = %{
        info: %{
          nickname: github_login,
          name: "Test User",
          email: "<EMAIL>",
          description: "Test description",
          image: "https://example.com/avatar.png",
          urls: %{
            "html_url" => "https://github.com/#{github_login}"
          },
          location: "Test Location"
        },
        credentials: %{
          token: "test_token",
          refresh_token: "test_refresh_token",
          expires_at: System.system_time(:second) + 3600
        }
      }

      # Mock the GitHub API response for list_user_installations
      # Include an installation for the user's personal account
      mock_installations = [
        %{
          "id" => installation_id,
          "account" => %{
            "login" => github_login,
            "type" => "User",
            "avatar_url" => "https://example.com/avatar.png",
            "html_url" => "https://github.com/#{github_login}"
          }
        }
      ]

      # Set up the mock to return our installations
      expect(GitHubMock, :client, fn _user -> "mock_client" end)
      expect(GitHubMock, :list_user_installations, fn _client -> {:ok, mock_installations} end)

      # Call user_from_auth
      {:ok, user} = Accounts.user_from_auth(auth)

      # Reload the user with the default organization
      user = Repo.preload(user, :default_organization)

      # Verify that the default organization has the correct installation_id
      assert user.default_organization.installation_id == installation_id
      assert user.default_organization.name == github_login
    end

    test "updates existing user while maintaining organization settings" do
      # First, create an organization with settings
      organization =
        organization_fixture(%{name: "existing_settings_user"})
        |> Repo.preload(:settings)

      # Update the settings
      {:ok, _settings} =
        Accounts.update_organization_settings(organization, %{
          avatar_url: "https://example.com/old_avatar.jpg",
          html_url: "https://github.com/existing_settings_user"
        })

      # Create a user with the organization as default
      user =
        user_fixture(%{
          login: "existing_settings_user",
          default_organization_id: organization.id
        })

      # Now simulate auth flow for existing user
      auth = %{
        info: %{
          name: "Updated User",
          email: "<EMAIL>",
          # Same login
          nickname: "existing_settings_user",
          description: "Updated description",
          image: "https://example.com/new_avatar.jpg",
          urls: %{"html_url" => "https://github.com/existing_settings_user"},
          location: "Updated Location"
        },
        credentials: %{
          token: "new_token",
          refresh_token: "new_refresh_token",
          # Max 32-bit signed integer
          expires_at: 2_147_483_647
        }
      }

      # Mock GitHub API calls
      expect(GitHubMock, :client, fn _user -> "mock_client" end)
      expect(GitHubMock, :list_user_installations, fn _client -> {:ok, []} end)

      # Process the auth
      assert {:ok, updated_user} = Accounts.user_from_auth(auth)
      assert updated_user.id == user.id
      assert updated_user.token == "new_token"

      # Verify organization settings are preserved
      organization =
        Repo.get(Organization, updated_user.default_organization_id)
        |> Repo.preload(:settings)

      assert organization.settings.avatar_url == "https://example.com/old_avatar.jpg"
      assert organization.settings.html_url == "https://github.com/existing_settings_user"
    end
  end

  describe "refresh_user_token/1" do
    setup do
      organization = organization_fixture(%{name: "testuser"})

      user =
        Repo.insert!(%User{
          login: "testuser",
          token: "old_token",
          refresh_token: "old_refresh_token",
          expires_at: System.system_time(:second) - 60,
          refresh_token_expires_at: System.system_time(:second) + 15_897_600,
          default_organization_id: organization.id,
          info: %{
            name: "Test User",
            email: "<EMAIL>",
            nickname: "testuser"
          }
        })

      %{user: user}
    end

    test "refreshes token successfully with valid GitHub response", %{user: user} do
      new_token = "****************************************"

      new_refresh_token =
        "********************************************************************************"

      expires_in = 28800
      refresh_token_expires_in = 15_897_600

      Req.Test.stub(Repobot.Accounts, fn conn ->
        Req.Test.json(conn, %{
          "access_token" => new_token,
          "refresh_token" => new_refresh_token,
          "expires_in" => expires_in,
          "refresh_token_expires_in" => refresh_token_expires_in,
          "scope" => "",
          "token_type" => "bearer"
        })
      end)

      now = System.system_time(:second)
      {:ok, updated_user} = Accounts.refresh_user_token(user)

      assert updated_user.token == new_token
      assert_in_delta updated_user.expires_at, now + expires_in, 1

      assert updated_user.refresh_token == new_refresh_token
      remaining_time = user.refresh_token_expires_at - now
      expected_refresh_expires_at = now + max(remaining_time, refresh_token_expires_in)
      assert_in_delta updated_user.refresh_token_expires_at, expected_refresh_expires_at, 1
    end

    test "returns error with unexpected response structure", %{user: user} do
      Req.Test.stub(Repobot.Accounts, fn conn ->
        Req.Test.json(conn, %{
          "access_token" => "new_token",
          "expires_in" => 3600
        })
      end)

      log =
        capture_log(fn ->
          assert {:error, "Failed to refresh token: unexpected response structure"} =
                   Accounts.refresh_user_token(user)
        end)

      assert log =~ "Failed to refresh GitHub OAuth token"
    end

    test "returns error when refresh token is not available", %{user: user} do
      user = %{user | refresh_token: nil}

      log =
        capture_log(fn ->
          assert {:error, "No refresh token available"} = Accounts.refresh_user_token(user)
        end)

      assert log =~ "Cannot refresh token for user"
    end
  end

  describe "update_organization_settings/2" do
    test "updates existing organization settings" do
      # Create organization with settings via fixture
      organization = organization_fixture(%{name: "test_org"})
      # Reload organization with settings
      organization = Repo.preload(organization, :settings)

      settings_params = %{
        avatar_url: "https://example.com/new_avatar.jpg",
        html_url: "https://github.com/new_org",
        anthropic_api_key: "new_key",
        openai_api_key: "new_openai_key"
      }

      assert {:ok, updated_settings} =
               Accounts.update_organization_settings(organization, settings_params)

      assert updated_settings.avatar_url == "https://example.com/new_avatar.jpg"
      assert updated_settings.html_url == "https://github.com/new_org"
      assert updated_settings.anthropic_api_key == "new_key"
      assert updated_settings.openai_api_key == "new_openai_key"
      assert updated_settings.organization_id == organization.id
    end

    test "creates settings for organization without existing settings" do
      # Create organization without settings
      {:ok, organization} =
        %Organization{}
        |> Organization.changeset(%{name: "test_org"})
        |> Repo.insert()

      # Preload the settings association
      organization = Repo.preload(organization, :settings)

      settings_params = %{
        avatar_url: "https://example.com/avatar.jpg",
        html_url: "https://github.com/org",
        anthropic_api_key: "test_key",
        openai_api_key: "openai_key"
      }

      assert {:ok, settings} =
               Accounts.update_organization_settings(organization, settings_params)

      assert settings.avatar_url == "https://example.com/avatar.jpg"
      assert settings.html_url == "https://github.com/org"
      assert settings.anthropic_api_key == "test_key"
      assert settings.openai_api_key == "openai_key"
      assert settings.organization_id == organization.id
    end

    test "handles validation errors" do
      # Create first organization with settings
      org1 = organization_fixture(%{name: "org1"})
      org1 = Repo.preload(org1, :settings)

      # Create second organization
      org2 = organization_fixture(%{name: "org2"})
      org2 = Repo.preload(org2, :settings)

      # Try to update org2's settings to use org1's organization_id
      settings_params = %{
        organization_id: org1.id
      }

      assert {:error, changeset} =
               Accounts.update_organization_settings(org2, settings_params)

      assert "has already been taken" in errors_on(changeset).organization_id
    end
  end
end
