defmodule Repobot.SourceFilesTest do
  use Repobot.DataCase
  use Repobot.Test.Fixtures

  alias Repobot.{SourceFiles, Repository}

  import Repobot.Test.Fixtures
  import ExUnit.CaptureLog
  import Mox

  # Setup verification for all tests
  setup :verify_on_exit!

  describe "render_template/2" do
    setup do
      user = create_user()

      # Read fixture files
      workflow_content = File.read!("test/fixtures/source_files/github_workflow.yml")
      readme_content = File.read!("test/fixtures/source_files/readme.md")

      # Create source files
      workflow_file =
        create_source_file(%{
          name: "github_workflow.yml",
          content: workflow_content,
          is_template: true,
          user_id: user.id
        })

      readme_file =
        create_source_file(%{
          name: "README.md",
          content: readme_content,
          is_template: true,
          user_id: user.id
        })

      non_template_file =
        create_source_file(%{
          name: "static.txt",
          content: "This is a static file",
          is_template: false,
          user_id: user.id
        })

      {:ok,
       %{
         user: user,
         workflow_file: workflow_file,
         readme_file: readme_file,
         non_template_file: non_template_file
       }}
    end

    test "renders a non-template file as-is", %{non_template_file: file} do
      assert {:ok, "This is a static file"} = SourceFiles.render_template(file, %{})
    end

    test "renders a README template with repository variables", %{readme_file: file} do
      vars = %{
        "name" => "awesome-project",
        "description" => "A really cool project",
        "owner_name" => "octocat",
        "language" => "Elixir",
        "topics" => ["elixir", "phoenix", "web"],
        "license" => "MIT"
      }

      {:ok, rendered} = SourceFiles.render_template(file, vars)

      assert rendered =~ "# awesome-project"
      assert rendered =~ "A really cool project"
      assert rendered =~ "maintained by octocat"
      assert rendered =~ "written primarily in Elixir"
      assert rendered =~ "- elixir"
      assert rendered =~ "- phoenix"
      assert rendered =~ "- web"
      assert rendered =~ "licensed under MIT"
    end

    test "renders a GitHub workflow template preserving GitHub Actions syntax", %{
      workflow_file: file
    } do
      vars = %{
        "name" => "awesome-project",
        "html_url" => "https://github.com/octocat/awesome-project",
        "default_branch" => "main"
      }

      {:ok, rendered} = SourceFiles.render_template(file, vars)

      # Check that our template variables are rendered
      assert rendered =~ "branches: [ main ]"
      assert rendered =~ "REPO_NAME: awesome-project"
      assert rendered =~ "REPO_URL: https://github.com/octocat/awesome-project"

      # Check that GitHub Actions syntax is preserved
      assert rendered =~ "${{ matrix.os }}"
      assert rendered =~ "${{ env.REPO_NAME }}"
      assert rendered =~ "${{ env.REPO_URL }}"
    end

    test "handles missing variables gracefully", %{readme_file: file} do
      vars = %{
        "name" => "awesome-project",
        # description is missing
        "owner_name" => "octocat",
        "language" => "Elixir",
        # topics is missing
        "license" => "MIT"
      }

      {:ok, rendered} = SourceFiles.render_template(file, vars)

      assert rendered =~ "# awesome-project"
      assert rendered =~ "maintained by octocat"
      assert rendered =~ "written primarily in Elixir"
      assert rendered =~ "licensed under MIT"
      # Missing description should be empty
      assert rendered =~ "\n\n"
      # Topics section should not appear
      refute rendered =~ "## Topics"
    end

    test "handles invalid template syntax", %{user: user} do
      file =
        create_source_file(%{
          name: "invalid.md",
          content: "{{ unclosed tag",
          is_template: true,
          user_id: user.id
        })

      log =
        capture_log(fn ->
          assert {:error, "Template parsing failed: " <> _} =
                   SourceFiles.render_template(file, %{})
        end)

      assert log =~ "Template parsing failed"
      assert log =~ "Tag or Object not properly terminated"
      assert log =~ "{{ unclosed tag"
    end

    test "handles empty content", %{user: user} do
      file =
        create_source_file(%{
          name: "empty.md",
          content: "",
          is_template: true,
          user_id: user.id
        })

      assert {:ok, ""} = SourceFiles.render_template(file, %{})
    end
  end

  describe "source files with tags" do
    test "updating a source file without specifying tags preserves existing tags" do
      user = create_user()

      # Create a source file with initial tags
      source_file =
        create_source_file(
          %{
            name: "config.yml",
            user_id: user.id
          },
          tags: ["config", "yaml"]
        )

      # Update the source file without mentioning tags
      {:ok, updated_file} = SourceFiles.update_source_file(source_file, %{name: "new_config.yml"})

      # Verify tags are preserved
      tag_names = Enum.map(updated_file.tags, & &1.name)
      assert "config" in tag_names
      assert "yaml" in tag_names
      assert length(tag_names) == 2
    end

    test "can explicitly clear all tags from a source file" do
      user = create_user()

      # Create a source file with initial tags
      source_file =
        create_source_file(
          %{
            name: "config.yml",
            user_id: user.id
          },
          tags: ["config", "yaml"]
        )

      # Update the source file with an empty tags list
      {:ok, updated_file} = SourceFiles.update_source_file(source_file, %{"tags" => []})

      # Verify tags are cleared
      assert Enum.empty?(updated_file.tags)
    end

    test "can delete multiple source files" do
      user = create_user()

      # Create multiple source files
      file1 = create_source_file(%{name: "file1.yml", user_id: user.id})
      file2 = create_source_file(%{name: "file2.yml", user_id: user.id})
      file3 = create_source_file(%{name: "file3.yml", user_id: user.id})

      # Get initial count
      initial_count = length(SourceFiles.list_source_files(user))

      # Delete two files
      {:ok, deleted_count} = SourceFiles.delete_source_files([file1.id, file2.id])

      # Verify correct number of files were deleted
      assert deleted_count == 2

      # Verify files are actually deleted
      remaining_files = SourceFiles.list_source_files(user)
      assert length(remaining_files) == initial_count - 2

      # Verify the correct files remain
      remaining_ids = Enum.map(remaining_files, & &1.id)
      assert file3.id in remaining_ids
      refute file1.id in remaining_ids
      refute file2.id in remaining_ids
    end

    test "delete_source_files with empty list returns zero count" do
      {:ok, deleted_count} = SourceFiles.delete_source_files([])
      assert deleted_count == 0
    end

    test "can add new tags to a source file" do
      user = create_user()

      # Create a source file with initial tags
      source_file =
        create_source_file(
          %{
            name: "config.yml",
            user_id: user.id
          },
          tags: ["config"]
        )

      # Add a new tag
      {:ok, updated_file} =
        SourceFiles.update_source_file(source_file, %{"tags" => ["config", "yaml"]})

      # Verify both tags are present
      tag_names = Enum.map(updated_file.tags, & &1.name)
      assert "config" in tag_names
      assert "yaml" in tag_names
      assert length(tag_names) == 2
    end

    test "can remove specific tags from a source file" do
      user = create_user()

      # Create a source file with initial tags
      source_file =
        create_source_file(
          %{
            name: "config.yml",
            user_id: user.id
          },
          tags: ["config", "yaml", "settings"]
        )

      # Remove one tag
      {:ok, updated_file} =
        SourceFiles.update_source_file(source_file, %{"tags" => ["config", "settings"]})

      # Verify correct tags remain
      tag_names = Enum.map(updated_file.tags, & &1.name)
      assert "config" in tag_names
      assert "settings" in tag_names
      refute "yaml" in tag_names
      assert length(tag_names) == 2
    end

    test "can update tags and other attributes simultaneously" do
      user = create_user()

      # Create a source file with initial tags
      source_file =
        create_source_file(
          %{
            name: "config.yml",
            user_id: user.id
          },
          tags: ["config"]
        )

      # Update both tags and name
      {:ok, updated_file} =
        SourceFiles.update_source_file(source_file, %{
          "tags" => ["config", "yaml"],
          "name" => "new_config.yml"
        })

      # Verify both tags and name are updated
      tag_names = Enum.map(updated_file.tags, & &1.name)
      assert "config" in tag_names
      assert "yaml" in tag_names
      assert length(tag_names) == 2
      assert updated_file.name == "new_config.yml"
    end
  end

  describe "find_by_repository_and_paths/3" do
    test "finds source files matching the repository ID and target paths" do
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      file1 =
        create_source_file(%{
          name: "file1.ex",
          target_path: "lib/file1.ex",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      file2 =
        create_source_file(%{
          name: "file2.ex",
          target_path: "lib/file2.ex",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      _other_file =
        create_source_file(%{
          name: "other.ex",
          target_path: "lib/other.ex",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate files with target repo for preloading test
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: file1.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: file2.id
      })

      # Find specific files
      found_files =
        SourceFiles.find_by_repository_and_paths(template_repo.id, [
          "lib/file1.ex",
          "lib/file2.ex"
        ])

      assert length(found_files) == 2
      found_ids = Enum.map(found_files, & &1.id)
      assert file1.id in found_ids
      assert file2.id in found_ids

      # Find with preloading
      found_files_preloaded =
        SourceFiles.find_by_repository_and_paths(template_repo.id, ["lib/file1.ex"],
          preload: [:repositories]
        )

      assert length(found_files_preloaded) == 1
      preloaded_file = hd(found_files_preloaded)
      assert preloaded_file.id == file1.id
      assert Enum.any?(preloaded_file.repositories, fn repo -> repo.id == target_repo.id end)
    end

    test "returns empty list if no files match" do
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      _file1 =
        create_source_file(%{
          name: "file1.ex",
          target_path: "lib/file1.ex",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      assert SourceFiles.find_by_repository_and_paths(template_repo.id, ["non/existent.ex"]) == []
    end
  end

  describe "find_by_repository_and_file_paths/3" do
    test "finds source files for both template and non-template files" do
      user = create_user()
      repository = create_repository(%{user_id: user.id, name: "test-repo"})

      # Create a non-template source file
      non_template_file =
        create_source_file(%{
          name: "README.md",
          target_path: "README.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: repository.id,
          is_template: false
        })

      # Create a template source file
      template_file =
        create_source_file(%{
          name: "settings.yml.liquid",
          target_path: "settings.yml",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: repository.id,
          is_template: true
        })

      # Search using repository file paths (how they appear in GitHub)
      found_files =
        SourceFiles.find_by_repository_and_file_paths(
          repository.id,
          ["README.md", "settings.yml.liquid"]
        )

      assert length(found_files) == 2
      found_ids = Enum.map(found_files, & &1.id) |> Enum.sort()
      expected_ids = [non_template_file.id, template_file.id] |> Enum.sort()
      assert found_ids == expected_ids
    end

    test "handles template files correctly by removing .liquid extension" do
      user = create_user()
      repository = create_repository(%{user_id: user.id, name: "test-repo"})

      # Create a template source file
      template_file =
        create_source_file(%{
          name: "config.yml.liquid",
          target_path: "config.yml",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: repository.id,
          is_template: true
        })

      # Search using the .liquid file path (as it appears in the repository)
      found_files =
        SourceFiles.find_by_repository_and_file_paths(
          repository.id,
          ["config.yml.liquid"]
        )

      assert length(found_files) == 1
      assert hd(found_files).id == template_file.id
      assert hd(found_files).target_path == "config.yml"
      assert hd(found_files).name == "config.yml.liquid"
    end

    test "returns empty list if no files match" do
      user = create_user()
      repository = create_repository(%{user_id: user.id, name: "test-repo"})

      found_files =
        SourceFiles.find_by_repository_and_file_paths(
          repository.id,
          ["nonexistent.md", "nonexistent.yml.liquid"]
        )

      assert found_files == []
    end
  end

  describe "update_source_file/2" do
    test "updates source file attributes" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "old_name.ex",
          content: "old content",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      {:ok, updated_file} =
        SourceFiles.update_source_file(source_file, %{name: "new_name.ex", content: "new content"})

      assert updated_file.id == source_file.id
      assert updated_file.name == "new_name.ex"
      assert updated_file.content == "new content"

      # Verify change is persisted
      db_file = Repo.get(Repobot.SourceFile, source_file.id)
      assert db_file.name == "new_name.ex"
      assert db_file.content == "new content"
    end

    test "returns error changeset on invalid attributes" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "old_name.ex",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # name is required
      assert {:error, changeset} = SourceFiles.update_source_file(source_file, %{name: nil})
      assert changeset.valid? == false
      assert [name: {"can't be blank", [validation: :required]}] == changeset.errors
    end
  end

  describe "import_file/2" do
    setup do
      user = create_user()

      repository =
        create_repository(%{
          name: "test-repo",
          owner: user.login,
          full_name: "#{user.login}/test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      {:ok, user: user, repository: repository}
    end

    test "raises ArgumentError when organization_id is missing", %{
      user: user,
      repository: repository
    } do
      attrs = %{
        name: "test.ex",
        content: "defmodule Test do\n  def hello, do: :world\nend\n",
        target_path: "lib/test.ex",
        user_id: user.id
      }

      assert_raise ArgumentError, "organization_id is required for importing a file", fn ->
        SourceFiles.import_file(attrs, [repository])
      end
    end

    test "raises ArgumentError when user_id is missing", %{user: user, repository: repository} do
      attrs = %{
        name: "test.ex",
        content: "defmodule Test do\n  def hello, do: :world\nend\n",
        target_path: "lib/test.ex",
        organization_id: user.default_organization_id
      }

      assert_raise ArgumentError, "user_id is required for importing a file", fn ->
        SourceFiles.import_file(attrs, [repository])
      end
    end

    test "successfully imports a file with tags", %{user: user, repository: repository} do
      # Mock AI tag inference
      Repobot.Test.AIMock
      |> expect(:infer_tags, fn source_file, org ->
        assert org.id == user.default_organization_id
        assert source_file.name == "test.ex"
        {:ok, ["elixir", "test"]}
      end)

      attrs = %{
        name: "test.ex",
        content: "defmodule Test do\n  def hello, do: :world\nend\n",
        target_path: "lib/test.ex",
        organization_id: user.default_organization_id,
        user_id: user.id
      }

      assert {:ok, source_file} = SourceFiles.import_file(attrs, [repository])

      # Verify source file was created with correct attributes
      assert source_file.name == "test.ex"
      assert source_file.content == "defmodule Test do\n  def hello, do: :world\nend\n"
      assert source_file.target_path == "lib/test.ex"
      assert source_file.user_id == user.id
      assert source_file.organization_id == user.default_organization_id

      # Verify repository association
      source_file = Repo.preload(source_file, :repositories)
      assert length(source_file.repositories) == 1
      assert hd(source_file.repositories).id == repository.id

      # Verify tags were created and associated
      source_file = Repo.preload(source_file, :tags)
      assert length(source_file.tags) == 2
      tag_names = Enum.map(source_file.tags, & &1.name)
      assert "elixir" in tag_names
      assert "test" in tag_names
    end

    test "succeeds even if tag inference fails", %{user: user, repository: repository} do
      # Mock AI tag inference to fail
      Repobot.Test.AIMock
      |> expect(:infer_tags, fn _source_file, _org ->
        {:error, :tags}
      end)

      attrs = %{
        name: "test.ex",
        content: "defmodule Test do\n  def hello, do: :world\nend\n",
        target_path: "lib/test.ex",
        organization_id: user.default_organization_id,
        user_id: user.id
      }

      assert {:ok, source_file} = SourceFiles.import_file(attrs, [repository])

      # Verify source file was created
      assert source_file.name == "test.ex"

      # Verify repository association still works
      source_file = Repo.preload(source_file, :repositories)
      assert length(source_file.repositories) == 1

      # Verify no tags were created
      source_file = Repo.preload(source_file, :tags)
      assert Enum.empty?(source_file.tags)
    end

    test "fails if repository association fails", %{user: user} do
      # Try to associate with a non-existent repository
      invalid_repo = %Repository{id: Ecto.UUID.generate()}

      attrs = %{
        name: "test.ex",
        content: "defmodule Test do\n  def hello, do: :world\nend\n",
        target_path: "lib/test.ex",
        organization_id: user.default_organization_id,
        user_id: user.id
      }

      assert {:error, "Failed to associate file with repositories"} =
               SourceFiles.import_file(attrs, [invalid_repo])
    end

    test "fails with invalid attributes", %{user: user, repository: repository} do
      # Missing required name
      attrs = %{
        content: "defmodule Test do\n  def hello, do: :world\nend\n",
        target_path: "lib/test.ex",
        organization_id: user.default_organization_id,
        user_id: user.id
      }

      assert {:error, message} = SourceFiles.import_file(attrs, [repository])
      assert message =~ "Failed to create source file"
    end

    test "imports file with multiple repositories", %{user: user, repository: repository} do
      # Create a second repository
      repository2 =
        create_repository(%{
          name: "test-repo-2",
          owner: user.login,
          full_name: "#{user.login}/test-repo-2",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Mock AI tag inference
      Repobot.Test.AIMock
      |> expect(:infer_tags, fn source_file, _org ->
        assert length(source_file.repositories) == 2
        {:ok, ["elixir", "test"]}
      end)

      attrs = %{
        name: "test.ex",
        content: "defmodule Test do\n  def hello, do: :world\nend\n",
        target_path: "lib/test.ex",
        organization_id: user.default_organization_id,
        user_id: user.id
      }

      assert {:ok, source_file} = SourceFiles.import_file(attrs, [repository, repository2])

      # Verify associations with both repositories
      source_file = Repo.preload(source_file, :repositories)
      assert length(source_file.repositories) == 2
      repository_ids = Enum.map(source_file.repositories, & &1.id)
      assert repository.id in repository_ids
      assert repository2.id in repository_ids
    end

    test "uses existing tags when available", %{user: user, repository: repository} do
      # Create some existing tags
      existing_tag = create_tag(user, %{name: "elixir"})

      # Mock AI tag inference to return mix of existing and new tags
      Repobot.Test.AIMock
      |> expect(:infer_tags, fn _source_file, _org ->
        {:ok, ["elixir", "new-tag"]}
      end)

      attrs = %{
        name: "test.ex",
        content: "defmodule Test do\n  def hello, do: :world\nend\n",
        target_path: "lib/test.ex",
        organization_id: user.default_organization_id,
        user_id: user.id
      }

      assert {:ok, source_file} = SourceFiles.import_file(attrs, [repository])

      # Verify tags were associated correctly
      source_file = Repo.preload(source_file, :tags)
      assert length(source_file.tags) == 2

      # Verify existing tag was reused
      tag_ids = Enum.map(source_file.tags, & &1.id)
      assert existing_tag.id in tag_ids

      # Verify new tag was created
      new_tag = Enum.find(source_file.tags, &(&1.name == "new-tag"))
      assert new_tag != nil
    end
  end

  describe "template conversion" do
    test "automatically renames file when converting to template" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "README.md",
          content: "# {{ name }}",
          is_template: false,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Convert to template
      {:ok, updated_file} = SourceFiles.update_source_file(source_file, %{is_template: true})

      # Verify file was renamed with .liquid extension
      assert updated_file.name == "README.md.liquid"
      # Verify target_path is set to original name
      assert updated_file.target_path == "README.md"
      assert updated_file.is_template == true
    end

    test "renames file in GitHub using Git Data API when converting to template" do
      user = create_user()

      # Create a source repository
      source_repository =
        create_repository(%{
          name: "test-repo",
          owner: user.login,
          full_name: "#{user.login}/test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file =
        create_source_file(%{
          name: "README.md",
          content: "# {{ name }}",
          is_template: false,
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: source_repository.id
        })

      # Mock GitHub API calls for file renaming using Git Data API
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        assert owner == user.login
        assert repo == "test-repo"
        test_client
      end)
      |> expect(:get_file_content, fn ^test_client, owner, repo, path ->
        assert owner == user.login
        assert repo == "test-repo"
        assert path == "README.md"
        {:ok, "# {{ name }}", %{"sha" => "test-sha"}}
      end)
      |> expect(:rename_file, fn ^test_client,
                                 owner,
                                 repo,
                                 old_path,
                                 new_path,
                                 content,
                                 message ->
        assert owner == user.login
        assert repo == "test-repo"
        assert old_path == "README.md"
        assert new_path == "README.md.liquid"
        assert content == "# {{ name }}"
        assert message =~ "Convert README.md to template"
        {:ok, "new-commit-sha"}
      end)

      # Convert to template
      {:ok, updated_file} = SourceFiles.update_source_file(source_file, %{is_template: true})

      # Verify file was renamed with .liquid extension
      assert updated_file.name == "README.md.liquid"
      # Verify target_path is set to original name
      assert updated_file.target_path == "README.md"
      assert updated_file.is_template == true
    end

    test "handles files without extension when converting to template" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "Dockerfile",
          content: "FROM {{ base_image }}",
          is_template: false,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Convert to template
      {:ok, updated_file} = SourceFiles.update_source_file(source_file, %{is_template: true})

      # Verify file was renamed with .liquid extension
      assert updated_file.name == "Dockerfile.liquid"
      # Verify target_path is set to original name
      assert updated_file.target_path == "Dockerfile"
      assert updated_file.is_template == true
    end

    test "does not rename file if already has .liquid extension" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "config.yml.liquid",
          content: "name: {{ name }}",
          is_template: false,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Convert to template
      {:ok, updated_file} = SourceFiles.update_source_file(source_file, %{is_template: true})

      # Verify file name remains the same
      assert updated_file.name == "config.yml.liquid"
      # Verify target_path is set to original name
      assert updated_file.target_path == "config.yml.liquid"
      assert updated_file.is_template == true
    end

    test "does not rename when converting from template to non-template" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "README.md.liquid",
          content: "# {{ name }}",
          is_template: true,
          target_path: "README.md",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Convert from template to non-template
      {:ok, updated_file} = SourceFiles.update_source_file(source_file, %{is_template: false})

      # Verify file name remains the same (no automatic renaming back)
      assert updated_file.name == "README.md.liquid"
      assert updated_file.target_path == "README.md"
      assert updated_file.is_template == false
    end

    test "reproduces bug: when user manually sets target_path with .liquid extension during template conversion" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          is_template: false,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Simulate the bug scenario: user manually sets target_path to include .liquid
      # and then converts to template
      {:ok, updated_file} =
        SourceFiles.update_source_file(source_file, %{
          name: "CONTRIBUTING.md",
          # User manually set this
          target_path: "CONTRIBUTING.md.liquid",
          is_template: true
        })

      # BUG: The target_path should be "CONTRIBUTING.md" but it's getting ".liquid" appended
      # because the changeset logic extracts the original name from the target_path
      assert updated_file.name == "CONTRIBUTING.md.liquid"
      # This should pass but might fail
      assert updated_file.target_path == "CONTRIBUTING.md"
      assert updated_file.is_template == true
    end

    test "reproduces LiveView bug: form submission overwrites target_path with name" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          is_template: false,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Simulate what happens when the checkbox is clicked (without source repository)
      # This should update the name to include .liquid and set target_path correctly
      {:ok, updated_file} =
        SourceFiles.update_source_file(source_file, %{
          name: "CONTRIBUTING.md.liquid",
          target_path: "CONTRIBUTING.md",
          is_template: true
        })

      # This should work correctly
      assert updated_file.name == "CONTRIBUTING.md.liquid"
      assert updated_file.target_path == "CONTRIBUTING.md"
      assert updated_file.is_template == true

      # Now simulate what happens when the form is submitted with the name field
      # containing .liquid but the target_path somehow gets overwritten
      # This simulates the LiveView form submission bug
      {:ok, updated_file2} =
        SourceFiles.update_source_file(updated_file, %{
          name: "CONTRIBUTING.md.liquid",
          # This is what the form might submit
          target_path: "CONTRIBUTING.md.liquid",
          is_template: true
        })

      # The backend should handle this correctly and extract the original name
      assert updated_file2.name == "CONTRIBUTING.md.liquid"
      # Should be corrected
      assert updated_file2.target_path == "CONTRIBUTING.md"
      assert updated_file2.is_template == true
    end
  end
end
