defmodule Repobot.CategoriesTest do
  use Repobot.DataCase

  alias Repobot.Categories
  alias Repobot.Category

  import Repobot.Test.Fixtures

  describe "categories" do
    test "list_categories/1 returns all categories for an organization" do
      # Create users with different logins to ensure different organizations
      user = create_user(%{login: "user1"})
      other_user = create_user(%{login: "user2"})
      organization = user.default_organization
      other_organization = other_user.default_organization

      # Create categories for our organization
      category1 = create_category(organization, %{name: "Category 1"})
      category2 = create_category(organization, %{name: "Category 2"})
      category3 = create_category(organization, %{name: "Category 3"})

      # Create a category for another organization (should not be returned)
      _other_category = create_category(other_organization, %{name: "Other Category"})

      categories =
        Categories.list_categories(organization)
        |> Enum.sort_by(& &1.name)

      expected_categories =
        [category1, category2, category3]
        |> Enum.sort_by(& &1.name)

      assert categories == expected_categories
    end

    test "list_categories/1 returns categories sorted by name" do
      user = create_user()
      organization = user.default_organization
      create_category(organization, %{name: "Workflows"})
      create_category(organization, %{name: "Config"})
      create_category(organization, %{name: "Documentation"})

      categories = Categories.list_categories(organization)
      names = Enum.map(categories, & &1.name)
      assert names == ["Config", "Documentation", "Workflows"]
    end

    test "get_category!/1 returns the category with given id" do
      user = create_user()
      organization = user.default_organization
      category = create_category(organization)
      assert Categories.get_category!(category.id) == category
    end

    test "get_category!/1 raises Ecto.NoResultsError for non-existent id" do
      assert_raise Ecto.NoResultsError, fn ->
        Categories.get_category!("00000000-0000-0000-0000-000000000000")
      end
    end

    test "create_category/1 with valid data creates a category" do
      user = create_user()
      organization = user.default_organization
      valid_attrs = %{name: "Test Category", organization_id: organization.id}

      assert {:ok, category} = Categories.create_category(valid_attrs)
      assert category.name == "Test Category"
      assert category.organization_id == organization.id
    end

    test "create_category/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Categories.create_category(%{name: nil})
    end

    test "create_category/1 enforces unique names per organization" do
      user = create_user()
      organization = user.default_organization
      attrs = %{name: "Config Files", organization_id: organization.id}

      assert {:ok, %Category{}} = Categories.create_category(attrs)
      assert {:error, changeset} = Categories.create_category(attrs)

      assert "has already been taken" in errors_on(changeset).organization_id
    end

    test "update_category/2 with valid data updates the category" do
      user = create_user()
      organization = user.default_organization
      category = create_category(organization)
      update_attrs = %{name: "Updated Name"}

      assert {:ok, %Category{} = category} = Categories.update_category(category, update_attrs)
      assert category.name == "Updated Name"
    end

    test "update_category/2 with invalid data returns error changeset" do
      user = create_user()
      organization = user.default_organization
      category = create_category(organization)
      assert {:error, %Ecto.Changeset{}} = Categories.update_category(category, %{name: nil})
      assert category == Categories.get_category!(category.id)
    end

    test "delete_category/1 deletes the category" do
      user = create_user()
      organization = user.default_organization
      category = create_category(organization)
      assert {:ok, %Category{}} = Categories.delete_category(category)
      assert_raise Ecto.NoResultsError, fn -> Categories.get_category!(category.id) end
    end

    test "change_category/1 returns a category changeset" do
      user = create_user()
      organization = user.default_organization
      category = create_category(organization)
      assert %Ecto.Changeset{} = Categories.change_category(category)
    end
  end
end
