defmodule Repobot.PullRequestsTest do
  use Repobot.DataCase
  use Repobot.Test.Fixtures

  alias Repobot.PullRequests
  alias Repobot.PullRequest

  describe "get_pull_request_by/1" do
    test "returns the pull request matching the options" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "file.ex",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      pull_request =
        create_pull_request(%{
          repository: "owner/repo",
          pull_request_number: 123,
          source_file_id: source_file.id
        })

      %{id: id_from_query} =
        PullRequests.get_pull_request_by(
          repository: "owner/repo",
          pull_request_number: 123
        )

      assert id_from_query == pull_request.id

      %{id: id_from_second_query} = PullRequests.get_pull_request_by(id: pull_request.id)
      assert id_from_second_query == pull_request.id
    end

    test "returns nil if no pull request matches the options" do
      assert PullRequests.get_pull_request_by(
               repository: "non/existent",
               pull_request_number: 456
             ) == nil
    end
  end

  describe "update_pull_request/2" do
    test "updates pull request attributes" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "file.ex",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      pull_request =
        create_pull_request(%{
          repository: "owner/repo",
          pull_request_number: 123,
          source_file_id: source_file.id,
          status: "open"
        })

      {:ok, updated_pr} = PullRequests.update_pull_request(pull_request, %{status: "merged"})

      assert updated_pr.id == pull_request.id
      assert updated_pr.status == "merged"

      # Verify change is persisted
      db_pr = Repo.get(PullRequest, pull_request.id)
      assert db_pr.status == "merged"
    end

    test "returns error changeset on invalid attributes" do
      user = create_user()

      source_file =
        create_source_file(%{
          name: "file.ex",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      pull_request =
        create_pull_request(%{
          repository: "owner/repo",
          pull_request_number: 123,
          source_file_id: source_file.id
        })

      assert {:error, changeset} =
               PullRequests.update_pull_request(pull_request, %{status: "invalid_status"})

      assert changeset.valid? == false

      assert [
               status:
                 {"is invalid", [validation: :inclusion, enum: ["open", "closed", "merged"]]}
             ] == changeset.errors
    end
  end
end
