defmodule Repobot.Workers.RepositoryLoaderWorkerTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  import Repobot.Test.Fixtures
  import Mox

  alias Repobot.Workers.RepositoryLoaderWorker
  alias Repobot.Repositories

  setup :verify_on_exit!

  describe "perform/1" do
    test "successfully loads repositories when cache is empty" do
      user = user_fixture()
      organization = organization_fixture(%{name: user.login})

      # Mock the GitHub API to return repositories
      expect(Repobot.Test.GitHubMock, :client, fn _user ->
        :test_client
      end)

      expect(Repobot.Test.GitHubMock, :user_repos, fn :test_client, _username ->
        {:ok,
         [
           %{
             "id" => 123,
             "name" => "test-repo",
             "full_name" => "#{user.login}/test-repo",
             "owner" => %{"login" => user.login},
             "language" => "Elixir",
             "fork" => false,
             "private" => false,
             "description" => "Test repository"
           }
         ]}
      end)

      job_args = %{
        "user_id" => user.id,
        "organization_id" => organization.id
      }

      assert :ok = perform_job(RepositoryLoaderWorker, job_args)

      # Verify repositories were created
      repositories = Repositories.user_repositories(user, false, organization.id)
      assert length(repositories) == 1
      assert hd(repositories).name == "test-repo"
    end

    test "returns cached repositories when available" do
      user = user_fixture()
      organization = organization_fixture(%{name: user.login})

      # Create a repository in the cache
      create_repository(%{
        name: "cached-repo",
        full_name: "#{user.login}/cached-repo",
        owner: user.login,
        organization_id: organization.id
      })

      job_args = %{
        "user_id" => user.id,
        "organization_id" => organization.id
      }

      assert :ok = perform_job(RepositoryLoaderWorker, job_args)

      # Verify cached repository is returned
      repositories = Repositories.user_repositories(user, false, organization.id)
      assert length(repositories) == 1
      assert hd(repositories).name == "cached-repo"
    end

    test "sends progress updates via Oban.Notifier" do
      user = user_fixture()
      organization = organization_fixture(%{name: user.login})

      # Mock the GitHub API
      expect(Repobot.Test.GitHubMock, :client, fn _user ->
        :test_client
      end)

      expect(Repobot.Test.GitHubMock, :user_repos, fn :test_client, _username ->
        {:ok, []}
      end)

      # Subscribe to Oban notifications for the default topic
      topic = "repository_loading:#{user.id}"
      topic_atom = String.to_atom(topic)
      Oban.Notifier.listen(Oban, topic_atom)

      job_args = %{
        "user_id" => user.id,
        "organization_id" => organization.id
      }

      assert :ok = perform_job(RepositoryLoaderWorker, job_args)

      # Verify Oban notifications were sent
      assert_receive {:notification, ^topic_atom,
                      %{"event" => "repository_loading_progress", "progress" => 10}},
                     1000

      assert_receive {:notification, ^topic_atom,
                      %{"event" => "repository_loading_progress", "progress" => 30}},
                     1000

      assert_receive {:notification, ^topic_atom, %{"event" => "repository_loading_complete"}},
                     1000
    end

    test "handles GitHub API errors gracefully" do
      user = user_fixture()
      organization = organization_fixture(%{name: user.login})

      # Mock the GitHub API to return an error
      expect(Repobot.Test.GitHubMock, :client, fn _user ->
        :test_client
      end)

      expect(Repobot.Test.GitHubMock, :user_repos, fn :test_client, _username ->
        {:error, "API rate limit exceeded"}
      end)

      job_args = %{
        "user_id" => user.id,
        "organization_id" => organization.id
      }

      # The worker should handle the error gracefully and return cached repositories
      # Since there are no cached repositories, it should still succeed but with empty list
      assert :ok = perform_job(RepositoryLoaderWorker, job_args)
    end

    test "fails with invalid job arguments" do
      job_args = %{"invalid" => "args"}

      assert {:error, reason} = perform_job(RepositoryLoaderWorker, job_args)
      assert reason =~ "missing required user_id or organization_id"
    end
  end

  describe "enqueue_repository_loading/3" do
    test "successfully enqueues a job" do
      user = user_fixture()
      organization = organization_fixture(%{name: user.login})

      assert {:ok, %Oban.Job{}} =
               RepositoryLoaderWorker.enqueue_repository_loading(user.id, organization.id)
    end

    test "enqueues job with topic" do
      user = user_fixture()
      organization = organization_fixture(%{name: user.login})

      assert {:ok, %Oban.Job{args: args}} =
               RepositoryLoaderWorker.enqueue_repository_loading(
                 user.id,
                 organization.id,
                 "custom_topic"
               )

      assert Map.has_key?(args, "topic")
      assert args["topic"] == "custom_topic"
    end
  end
end
