defmodule Repobot.Workers.ObanIntegrationTest do
  use Repobot.DataCase, async: false
  use Oban.Testing, repo: Repobot.Repo

  alias Repobot.Workers.ExampleWorker

  describe "Oban integration" do
    test "can enqueue and perform jobs" do
      # Test that we can enqueue jobs using Oban
      assert {:ok, job} = 
        ExampleWorker.new(%{"action" => "greet", "name" => "Bob"}) 
        |> Oban.insert()

      assert job.worker == "Repobot.Workers.ExampleWorker"
      assert job.args == %{"action" => "greet", "name" => "Bob"}

      # Perform the job using Oban.Testing
      assert :ok = perform_job(ExampleWorker, job.args)
    end

    test "handles job failures correctly" do
      assert {:ok, job} = 
        ExampleWorker.new(%{"action" => "fail"}) 
        |> Oban.insert()

      assert {:error, "Intentional failure for testing"} = perform_job(ExampleWorker, job.args)
    end

    test "processes progress jobs" do
      assert {:ok, job} = 
        ExampleWorker.new(%{"action" => "progress"}) 
        |> Oban.insert()

      # This should complete successfully despite taking time
      assert :ok = perform_job(ExampleWorker, job.args)
    end
  end
end
