defmodule Repobot.Workers.ExampleWorkerTest do
  use ExUnit.Case, async: true

  import ExUnit.CaptureLog

  alias Repobot.Workers.ExampleWorker

  describe "perform/1" do
    test "greet action with valid name" do
      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.ExampleWorker",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"action" => "greet", "name" => "Alice"}
      }

      log =
        capture_log(fn ->
          assert :ok = ExampleWorker.perform(job)
        end)

      assert log =~ "Job started"
      assert log =~ "Job completed successfully"
    end

    test "greet action with invalid name" do
      job = %Oban.Job{
        id: 2,
        worker: "Repobot.Workers.ExampleWorker",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"action" => "greet", "name" => ""}
      }

      log =
        capture_log(fn ->
          assert {:error, "Name must be a non-empty string"} = ExampleWorker.perform(job)
        end)

      assert log =~ "Job started"
      assert log =~ "Job failed"
    end

    test "fail action" do
      job = %Oban.Job{
        id: 3,
        worker: "Repobot.Workers.ExampleWorker",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"action" => "fail"}
      }

      log =
        capture_log(fn ->
          assert {:error, "Intentional failure for testing"} = ExampleWorker.perform(job)
        end)

      assert log =~ "Job started"
      assert log =~ "Job failed"
    end

    test "progress action" do
      job = %Oban.Job{
        id: 4,
        worker: "Repobot.Workers.ExampleWorker",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"action" => "progress"}
      }

      log =
        capture_log(fn ->
          assert :ok = ExampleWorker.perform(job)
        end)

      assert log =~ "Job started"
      assert log =~ "Job progress update"
      assert log =~ "Job completed successfully"
    end

    test "unknown action" do
      job = %Oban.Job{
        id: 5,
        worker: "Repobot.Workers.ExampleWorker",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"action" => "unknown"}
      }

      log =
        capture_log(fn ->
          assert {:error, "Unknown action in job arguments"} = ExampleWorker.perform(job)
        end)

      assert log =~ "Job started"
      assert log =~ "Job failed"
    end
  end

  describe "job configuration" do
    test "respects worker configuration" do
      job_changeset = ExampleWorker.new(%{"action" => "greet", "name" => "Charlie"})
      assert job_changeset.changes.worker == "Repobot.Workers.ExampleWorker"
      assert job_changeset.changes.max_attempts == 3
    end
  end
end
