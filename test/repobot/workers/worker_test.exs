defmodule Repobot.Workers.WorkerTest do
  use Repobot.DataCase, async: true

  import ExUnit.CaptureLog

  alias Repobot.Workers.Worker

  describe "log_job_start/1" do
    test "logs job start with structured metadata" do
      job = %Oban.Job{
        id: 123,
        worker: "TestWorker",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"test" => "value"}
      }

      log =
        capture_log(fn ->
          Worker.log_job_start(job)
        end)

      assert log =~ "Job started"
      # In test environment, structured metadata might not be visible in capture_log
      # but the function should execute without errors
    end

    test "sanitizes large arguments" do
      large_content = String.duplicate("x", 2000)

      job = %Oban.Job{
        id: 123,
        worker: "TestWorker",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"large_content" => large_content}
      }

      log =
        capture_log(fn ->
          Worker.log_job_start(job)
        end)

      assert log =~ "Job started"
      # The sanitization happens internally, we just verify no errors occur
    end
  end

  describe "log_job_success/2" do
    test "logs job success with metadata" do
      job = %Oban.Job{
        id: 123,
        worker: "TestWorker",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{}
      }

      log =
        capture_log(fn ->
          Worker.log_job_success(job, %{result: "completed"})
        end)

      assert log =~ "Job completed successfully"
    end
  end

  describe "log_job_error/3" do
    test "logs job error with reason" do
      job = %Oban.Job{
        id: 123,
        worker: "TestWorker",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{}
      }

      log =
        capture_log(fn ->
          Worker.log_job_error(job, "Something went wrong")
        end)

      assert log =~ "Job failed"
    end

    test "formats different error types" do
      job = %Oban.Job{
        id: 123,
        worker: "TestWorker",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{}
      }

      # Test atom error
      log =
        capture_log(fn ->
          Worker.log_job_error(job, :timeout)
        end)

      assert log =~ "Job failed"

      # Test map with message
      log =
        capture_log(fn ->
          Worker.log_job_error(job, %{message: "Custom error"})
        end)

      assert log =~ "Job failed"
    end
  end

  describe "safe_send/2" do
    test "sends message to PID" do
      parent = self()

      Worker.safe_send(parent, {:test, "message"})

      assert_receive {:test, "message"}
    end

    test "handles encoded PID" do
      parent = self()
      encoded_pid = Worker.encode_pid(parent)

      Worker.safe_send(encoded_pid, {:test, "encoded"})

      assert_receive {:test, "encoded"}
    end

    test "handles invalid encoded PID gracefully" do
      log =
        capture_log(fn ->
          result = Worker.safe_send("invalid_pid", {:test, "message"})
          assert result == :error
        end)

      assert log =~ "Failed to send message to receiver"
    end
  end

  describe "encode_pid/1" do
    test "encodes and decodes PID correctly" do
      original_pid = self()
      encoded = Worker.encode_pid(original_pid)

      assert is_binary(encoded)

      decoded_pid = :erlang.binary_to_term(Base.decode64!(encoded))
      assert decoded_pid == original_pid
    end
  end
end
