defmodule Repobot.UsersTest do
  use Repobot.DataCase

  alias Repobot.Users
  alias Repobot.Test.Fixtures

  describe "users" do
    test "by_login/1 returns the user with given login" do
      user = Fixtures.create_user(%{login: "test-login"})

      found_user = Users.by_login("test-login")
      assert found_user.id == user.id
      assert found_user.login == "test-login"
    end

    test "by_login/1 returns nil when user does not exist" do
      assert Users.by_login("nonexistent") == nil
    end
  end
end
