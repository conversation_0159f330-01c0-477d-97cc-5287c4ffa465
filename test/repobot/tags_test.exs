defmodule Repobot.TagsTest do
  use Repobot.DataCase
  use Repobot.Test.Fixtures

  alias Repobot.Tags
  alias Repobot.Tag

  describe "color management" do
    test "random_pastel_color/0 returns a valid hex color" do
      color = Tags.random_pastel_color()
      assert color =~ ~r/^#[0-9A-F]{6}$/i
    end

    test "next_available_color/1 returns unique colors for tags" do
      user = create_user()

      # Create multiple tags and collect their colors
      colors =
        1..10
        |> Enum.map(fn i ->
          {:ok, tag} =
            Tags.create_tag(%{
              name: "tag#{i}",
              user_id: user.id,
              organization_id: user.default_organization_id
            })

          tag.color
        end)
        |> Enum.uniq()

      # Assert that we got unique colors
      assert length(colors) == 10
      assert Enum.all?(colors, &(&1 =~ ~r/^#[0-9A-F]{6}$/i))
    end

    test "next_available_color/1 falls back to random color when all colors are used" do
      user = create_user()

      # Create more tags than available colors
      colors =
        1..50
        |> Enum.map(fn i ->
          {:ok, tag} =
            Tags.create_tag(%{
              name: "tag#{i}",
              user_id: user.id,
              organization_id: user.default_organization_id
            })

          tag.color
        end)

      # All colors should still be valid hex colors
      assert Enum.all?(colors, &(&1 =~ ~r/^#[0-9A-F]{6}$/i))
    end
  end

  describe "tags CRUD" do
    setup do
      user = create_user()
      %{user: user}
    end

    test "list_tags/1 returns all user's tags", %{user: user} do
      # Create some tags
      tag1 = create_tag(%{name: "tag1", user_id: user.id})
      tag2 = create_tag(%{name: "tag2", user_id: user.id})

      tags = Tags.list_tags(user)
      assert length(tags) == 2
      assert Enum.map(tags, & &1.id) |> Enum.sort() == [tag1.id, tag2.id] |> Enum.sort()
    end

    test "get_tag!/1 returns the tag with given id", %{user: user} do
      tag = create_tag(%{name: "tag1", user_id: user.id})
      assert Tags.get_tag!(tag.id) == tag
    end

    test "create_tag/1 with valid data creates a tag", %{user: user} do
      valid_attrs = %{
        name: "tag1",
        user_id: user.id,
        organization_id: user.default_organization_id
      }

      assert {:ok, %Tag{} = tag} = Tags.create_tag(valid_attrs)
      assert tag.name == "tag1"
      assert tag.color =~ ~r/^#[0-9A-F]{6}$/i
    end

    test "create_tag/1 with invalid data returns error changeset", %{user: user} do
      invalid_attrs = %{name: nil, user_id: user.id}
      assert {:error, %Ecto.Changeset{}} = Tags.create_tag(invalid_attrs)
    end

    test "update_tag/2 with valid data updates the tag", %{user: user} do
      tag = create_tag(%{name: "tag1", user_id: user.id})
      update_attrs = %{name: "updated tag"}

      assert {:ok, %Tag{} = updated_tag} = Tags.update_tag(tag, update_attrs)
      assert updated_tag.name == "updated tag"
      # Color should remain the same after update
      assert updated_tag.color == tag.color
    end

    test "delete_tag/1 deletes the tag", %{user: user} do
      tag = create_tag(%{name: "tag1", user_id: user.id})
      assert {:ok, %Tag{}} = Tags.delete_tag(tag)
      assert_raise Ecto.NoResultsError, fn -> Tags.get_tag!(tag.id) end
    end
  end

  describe "batch operations" do
    setup do
      user = create_user()
      %{user: user}
    end

    test "get_or_create_tags/2 creates new tags and returns existing ones", %{user: user} do
      # First create some tags
      existing_tag = create_tag(%{name: "existing", user_id: user.id})

      # Try to get or create tags including both new and existing ones
      tag_names = ["existing", "new1", "new2"]
      tags = Tags.get_or_create_tags(tag_names, user)

      assert length(tags) == 3
      assert Enum.find(tags, &(&1.id == existing_tag.id))
      assert Enum.count(tags, &(&1.name == "new1")) == 1
      assert Enum.count(tags, &(&1.name == "new2")) == 1
    end

    test "get_or_create_tags/2 with empty list returns empty list", %{user: user} do
      assert Tags.get_or_create_tags([], user) == []
    end

    test "get_or_create_tags/2 with nil returns empty list", %{user: user} do
      assert Tags.get_or_create_tags(nil, user) == []
    end
  end
end
