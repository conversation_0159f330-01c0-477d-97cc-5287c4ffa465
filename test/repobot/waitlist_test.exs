defmodule Repobot.WaitlistTest do
  use Repobot.DataCase

  alias <PERSON>obot.Waitlist
  alias Repobot.Waitlist.Entry

  describe "entries" do
    @valid_attrs %{
      email: "<EMAIL>",
      github_username: "validuser"
    }
    @invalid_attrs %{email: nil, github_username: nil}

    test "list_entries/0 returns all entries in descending order" do
      entry1 = entry_fixture(Map.put(@valid_attrs, :email, "<EMAIL>"))
      # Add a small delay to ensure different timestamps
      Process.sleep(1)
      entry2 = entry_fixture(Map.put(@valid_attrs, :email, "<EMAIL>"))

      entries = Waitlist.list_entries()
      assert length(entries) == 2
      # Verify descending order by inserted_at timestamp
      assert Enum.map(entries, & &1.inserted_at) == [entry2.inserted_at, entry1.inserted_at]
    end

    test "create_entry/1 with valid data creates an entry" do
      assert {:ok, %Entry{} = entry} = Waitlist.create_entry(@valid_attrs)
      assert entry.email == "<EMAIL>"
      assert entry.github_username == "validuser"
    end

    test "create_entry/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Waitlist.create_entry(@invalid_attrs)
    end

    test "create_entry/1 normalizes email to lowercase" do
      attrs = Map.put(@valid_attrs, :email, "<EMAIL>")
      assert {:ok, %Entry{} = entry} = Waitlist.create_entry(attrs)
      assert entry.email == "<EMAIL>"
    end

    test "create_entry/1 validates GitHub username format" do
      attrs = Map.put(@valid_attrs, :github_username, "invalid username")
      assert {:error, changeset} = Waitlist.create_entry(attrs)
      assert "must be a valid GitHub username" in errors_on(changeset).github_username
    end

    test "create_entry/1 rejects disposable email domains" do
      attrs = Map.put(@valid_attrs, :email, "<EMAIL>")
      assert {:error, changeset} = Waitlist.create_entry(attrs)
      assert "disposable email addresses are not allowed" in errors_on(changeset).email
    end

    test "get_entry!/1 returns the entry with given id" do
      entry = entry_fixture()
      assert Waitlist.get_entry!(entry.id) == entry
    end

    test "get_entry_by_invitation_code/1 returns entry for valid code" do
      entry = entry_fixture()
      {:ok, updated_entry} = Waitlist.generate_invitation_code(entry)
      assert Waitlist.get_entry_by_invitation_code(updated_entry.invitation_code) == updated_entry
    end

    test "get_entry_by_invitation_code/1 returns nil for invalid code" do
      assert Waitlist.get_entry_by_invitation_code("invalid") == nil
    end

    test "generate_invitation_code/1 generates unique codes" do
      entry1 = entry_fixture(Map.put(@valid_attrs, :email, "<EMAIL>"))
      entry2 = entry_fixture(Map.put(@valid_attrs, :email, "<EMAIL>"))

      {:ok, updated_entry1} = Waitlist.generate_invitation_code(entry1)
      {:ok, updated_entry2} = Waitlist.generate_invitation_code(entry2)

      assert updated_entry1.invitation_code != nil
      assert updated_entry2.invitation_code != nil
      assert updated_entry1.invitation_code != updated_entry2.invitation_code
    end

    test "get_invite_url/1 generates correct URL" do
      entry = entry_fixture()
      {:ok, updated_entry} = Waitlist.generate_invitation_code(entry)
      url = Waitlist.get_invite_url(updated_entry)

      assert url =~ "http"
      assert url =~ "/auth/github"
      assert url =~ "invitation_code=#{updated_entry.invitation_code}"
    end

    test "delete_entry/1 deletes the entry" do
      entry = entry_fixture()
      assert {:ok, %Entry{}} = Waitlist.delete_entry(entry)
      assert_raise Ecto.NoResultsError, fn -> Waitlist.get_entry!(entry.id) end
    end
  end

  defp entry_fixture(attrs \\ %{}) do
    {:ok, entry} =
      attrs
      |> Enum.into(@valid_attrs)
      |> Waitlist.create_entry()

    entry
  end
end
