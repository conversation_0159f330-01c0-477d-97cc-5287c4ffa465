defmodule Repobot.RepositoryFixtures do
  @moduledoc """
  This module defines test helpers for creating repository-related entities.
  """

  def create_repository_file(attrs \\ %{}) do
    {:ok, file} =
      attrs
      |> Enum.into(%{
        path: "lib/test.ex",
        name: "test.ex",
        type: "file",
        size: 100,
        sha: "abc123",
        content: "defmodule Test do\n  def hello, do: :world\nend\n",
        content_updated_at: DateTime.utc_now()
      })
      |> Repobot.RepositoryFiles.create_repository_file()

    file
  end
end
