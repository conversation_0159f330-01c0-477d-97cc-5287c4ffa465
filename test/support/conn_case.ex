defmodule RepobotWeb.ConnCase do
  @moduledoc """
  This module defines the test case to be used by
  tests that require setting up a connection.

  Such tests rely on `Phoenix.ConnTest` and also
  import other functionality to make it easier
  to build common data structures and query the data layer.

  Finally, if the test case interacts with the database,
  we enable the SQL sandbox, so changes done to the database
  are reverted at the end of every test. If you are using
  PostgreSQL, you can even run database tests asynchronously
  by setting `use RepobotWeb.ConnCase, async: true`, although
  this option is not recommended for other databases.
  """

  use ExUnit.CaseTemplate

  using do
    quote do
      # The default endpoint for testing
      @endpoint RepobotWeb.Endpoint

      use RepobotWeb, :verified_routes

      # Import conveniences for testing with connections
      import Plug.Conn
      import Phoenix.ConnTest
      import RepobotWeb.ConnCase

      use Repobot.Test.Fixtures

      # Helper function to wait for assertions to become true
      def assert_eventually(func, timeout \\ 1000) do
        start = System.monotonic_time(:millisecond)
        do_assert_eventually(func, start, timeout)
      end

      defp do_assert_eventually(func, start, timeout) do
        try do
          func.()
        rescue
          ExUnit.AssertionError ->
            elapsed = System.monotonic_time(:millisecond) - start

            if elapsed < timeout do
              Process.sleep(10)
              do_assert_eventually(func, start, timeout)
            else
              raise ExUnit.AssertionError,
                message: "Assertion did not become true within #{timeout}ms"
            end
        end
      end
    end
  end

  setup tags do
    Repobot.DataCase.setup_sandbox(tags)

    conn = Phoenix.ConnTest.build_conn()

    if username = tags[:load_repos] do
      only = tags[:only] || []
      {user, repositories} = Repobot.Test.Fixtures.load_repos(username, only)
      {:ok, conn: conn, user: user, repositories: repositories}
    else
      {:ok, conn: conn}
    end
  end

  @doc """
  Helper function to log in a user during tests.
  """
  def log_in_user(conn, user) do
    conn
    |> Plug.Test.init_test_session(%{})
    |> Plug.Conn.put_session(:current_user_id, user.id)
  end
end
