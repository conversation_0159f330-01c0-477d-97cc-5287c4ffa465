defmodule Repobot.Test.Fixtures do
  @moduledoc """
  Test fixtures for creating test data.
  """

  defmacro __using__(_opts \\ []) do
    quote do
      alias Repobot.Repo

      import Repobot.Test.Fixtures
    end
  end

  alias Repobot.{
    Repo,
    Folder,
    Repository,
    SourceFile,
    SourceFileFolder,
    Tag,
    Accounts.User,
    Accounts.Organization,
    Accounts.OrganizationSettings,
    Accounts.UserOrganization
  }

  import Ecto.Query

  @doc """
  Creates a user with the given attributes if one doesn't exist.
  Also creates a default organization for the user if it doesn't exist.
  Returns the user with preloaded default_organization.
  """
  def create_user(attrs \\ %{}) do
    attrs =
      Map.merge(
        %{
          login: "test-user",
          email: "<EMAIL>",
          token: "test-token",
          refresh_token: "test-refresh-token",
          expires_at: System.system_time(:second) + 3600,
          refresh_token_expires_at: System.system_time(:second) + 15_897_600,
          info: %{
            name: "Test User",
            email: "<EMAIL>",
            nickname: "test-user",
            avatar_url: "https://example.com/avatar.png",
            html_url: "https://github.com/test-user"
          }
        },
        attrs
      )

    user =
      case Repo.get_by(User, login: attrs.login) do
        nil ->
          # Create default organization for the user
          organization = organization_fixture(%{name: attrs.login})

          # Create user with organization references
          {:ok, user} =
            %User{}
            |> User.changeset(
              Map.merge(attrs, %{
                default_organization_id: organization.id
              })
            )
            |> Repo.insert()

          # Create user-organization association with admin role for default org
          {:ok, _} =
            %UserOrganization{}
            |> UserOrganization.changeset(%{
              user_id: user.id,
              organization_id: organization.id,
              role: "admin"
            })
            |> Repo.insert()

          user

        user ->
          # Ensure the user has an organization
          case Repo.get_by(Organization, name: user.login) do
            nil ->
              # Create organization if it doesn't exist
              organization = organization_fixture(%{name: user.login})

              # Update user with organization references
              {:ok, user} =
                user
                |> User.changeset(%{
                  default_organization_id: organization.id
                })
                |> Repo.update()

              # Create user-organization association with admin role for default org
              {:ok, _} =
                %UserOrganization{}
                |> UserOrganization.changeset(%{
                  user_id: user.id,
                  organization_id: organization.id,
                  role: "admin"
                })
                |> Repo.insert()

              user

            organization ->
              # Ensure user-organization association exists
              case Repo.get_by(UserOrganization,
                     user_id: user.id,
                     organization_id: organization.id
                   ) do
                nil ->
                  {:ok, _} =
                    %UserOrganization{}
                    |> UserOrganization.changeset(%{
                      user_id: user.id,
                      organization_id: organization.id,
                      role: "admin"
                    })
                    |> Repo.insert()

                _existing ->
                  :ok
              end

              user
          end
      end

    # Always preload default_organization and organizations
    user
    |> Repo.preload([:default_organization, :organizations])
  end

  @doc """
  Creates a tag with the given attributes.
  Can be called with either a user and attributes or just attributes.

  ## Examples

      # Create a tag with default attributes
      create_tag(user)

      # Create a tag with specific attributes
      create_tag(user, %{name: "elixir"})

      # Create a tag with a specific color
      create_tag(user, %{name: "elixir", color: "#4B275F"})

      # Legacy usage with just attributes
      create_tag(%{name: "elixir", user_id: user.id})
  """
  def create_tag(user_or_attrs, attrs \\ %{})

  def create_tag(%User{} = user, attrs) do
    # Called with user and attrs
    attrs =
      Map.merge(
        %{
          user_id: user.id,
          organization_id: user.default_organization_id,
          name: "tag-#{System.unique_integer([:positive])}",
          color: random_color()
        },
        attrs
      )

    {:ok, tag} =
      %Tag{}
      |> Tag.changeset(attrs)
      |> Repo.insert()

    tag
  end

  def create_tag(attrs, _) when is_map(attrs) do
    # Legacy usage with just attributes
    user =
      attrs[:user] ||
        (attrs[:user_id] &&
           Repo.get!(User, attrs[:user_id])
           |> Repo.preload(:default_organization)) || create_user()

    attrs =
      attrs
      |> Map.merge(%{
        user_id: user.id,
        organization_id: user.default_organization_id,
        color: attrs[:color] || random_color()
      })

    {:ok, tag} =
      %Tag{}
      |> Tag.changeset(attrs)
      |> Repo.insert()

    tag
  end

  @doc """
  Creates a source file with the given attributes and optional tags.

  ## Examples

      # Create source file with attributes
      create_source_file(%{name: "test.ex", content: "content"})

      # Create source file with tags
      create_source_file(%{name: "test.ex"}, tags: ["elixir", "config"])

      # Create source file with existing tag structs
      create_source_file(%{name: "test.ex"}, tags: [%Tag{name: "elixir"}])
  """
  def create_source_file(attrs, opts \\ []) do
    user =
      attrs[:user] ||
        (attrs[:user_id] &&
           Repo.get!(User, attrs[:user_id])
           |> Repo.preload(:default_organization)) || create_user()

    tag_names = opts[:tags] || []

    attrs =
      Map.merge(attrs, %{
        user_id: user.id,
        organization_id: user.default_organization_id
      })

    tags =
      tag_names
      |> Enum.map(fn
        %Tag{} = tag -> tag
        name when is_binary(name) -> get_or_create_tag(name, user)
      end)

    %SourceFile{}
    |> SourceFile.changeset(attrs)
    |> Ecto.Changeset.put_assoc(:tags, tags)
    |> Repo.insert!()
    |> Repo.preload(:tags)
  end

  @doc """
  Gets an existing tag by name for a user or creates a new one.
  """
  def get_or_create_tag(name, user) do
    Repo.one(
      from t in Tag,
        where: t.name == ^name and t.user_id == ^user.id
    ) || create_tag(%{name: name, user_id: user.id})
  end

  # Generates a random hex color
  defp random_color do
    ("#" <> Integer.to_string(:rand.uniform(16_777_215), 16)) |> String.pad_leading(7, "0")
  end

  @doc """
  Loads repositories from a fixture file and creates them in the database.
  The repositories will be associated with folders based on their language.
  Returns a tuple containing the user and the list of repositories.
  """
  def load_repos(username, only \\ []) do
    # First ensure we have a user with preloaded default_organization
    user = create_user(%{login: username})

    # Load repositories from JSON fixture
    json_data =
      File.read!("test/fixtures/#{username}_repos.json")
      |> Jason.decode!()

    # Filter repositories if only is provided
    repositories =
      if only != [] do
        Enum.filter(json_data, fn repo -> repo["data"]["full_name"] in only end)
      else
        json_data
      end

    # Create language folders (for non-fork repos)
    language_folders =
      repositories
      |> Enum.reject(fn repo -> repo["data"]["fork"] end)
      |> Enum.map(fn repo -> repo["data"]["language"] end)
      |> Enum.reject(&is_nil/1)
      |> Enum.uniq()
      |> Enum.map(fn language ->
        {:ok, folder} =
          %Folder{}
          |> Folder.changeset(%{
            name: language,
            organization_id: user.default_organization_id
          })
          |> Repo.insert(on_conflict: :nothing)

        folder
      end)
      |> Enum.reduce(%{}, fn folder, acc -> Map.put(acc, folder.name, folder) end)

    # Create Forks folder
    {:ok, forks_folder} =
      %Folder{}
      |> Folder.changeset(%{
        name: "Forks",
        organization_id: user.default_organization_id
      })
      |> Repo.insert(on_conflict: :nothing)

    # Create repositories
    created_repos =
      Enum.map(repositories, fn repo ->
        repo_data = repo["data"]

        create_repository(%{
          name: repo_data["name"],
          owner: repo_data["owner"]["login"],
          full_name: repo_data["full_name"],
          language: repo_data["language"],
          fork: repo_data["fork"],
          data: repo_data,
          user_id: user.id,
          organization_id: user.default_organization_id,
          folder_id:
            cond do
              repo_data["fork"] ->
                forks_folder.id

              repo_data["language"] && language_folders[repo_data["language"]] ->
                language_folders[repo_data["language"]].id

              true ->
                nil
            end
        })
      end)

    {user, created_repos}
  end

  @doc """
  Creates a folder with the given attributes.
  Returns the folder.
  """
  def create_folder(attrs \\ %{}) do
    user =
      attrs[:user] ||
        (attrs[:user_id] &&
           Repo.get!(User, attrs[:user_id])
           |> Repo.preload(:default_organization)) || create_user()

    attrs =
      attrs
      |> Map.merge(%{
        organization_id: attrs[:organization_id] || user.default_organization_id,
        name: attrs[:name] || "Folder #{System.unique_integer([:positive])}"
      })

    {:ok, folder} =
      %Folder{}
      |> Folder.changeset(attrs)
      |> Repo.insert()

    folder
  end

  @doc """
  Creates a repository with the given attributes.
  Returns the repository.
  """
  def create_repository(attrs \\ %{}) do
    user =
      attrs[:user] ||
        (attrs[:user_id] &&
           Repo.get!(User, attrs[:user_id])
           |> Repo.preload(:default_organization)) || create_user()

    attrs =
      attrs
      |> Map.merge(%{
        owner: attrs[:owner] || user.login,
        full_name: attrs[:full_name] || "#{attrs[:owner] || user.login}/#{attrs[:name]}",
        organization_id: Map.get(attrs, :organization_id, user.default_organization_id)
      })

    {:ok, repository} =
      %Repository{}
      |> Repository.changeset(attrs)
      |> Repo.insert()

    repository
  end

  @doc """
  Creates a repository file with the given attributes.
  Returns the repository file.
  """
  def create_repository_file(attrs \\ %{}) do
    repository =
      attrs[:repository] ||
        (attrs[:repository_id] && Repo.get!(Repobot.Repository, attrs[:repository_id])) ||
        create_repository()

    attrs =
      attrs
      |> Map.merge(%{
        repository_id: repository.id,
        name: attrs[:name] || Path.basename(attrs[:path] || "file.txt"),
        path: attrs[:path] || "file.txt",
        type: attrs[:type] || "file",
        size: attrs[:size] || 0,
        sha: attrs[:sha] || "#{:crypto.strong_rand_bytes(8) |> Base.encode16()}"
      })

    {:ok, repository_file} =
      %Repobot.RepositoryFile{}
      |> Repobot.RepositoryFile.changeset(attrs)
      |> Repo.insert()

    repository_file
  end

  @doc """
  Creates multiple repository files for a repository.
  Returns a list of created repository files.

  ## Examples

      # Create default files
      create_repository_files(repository)

      # Create specific files
      create_repository_files(repository, [
        %{path: "mix.exs", type: "file", size: 1024},
        %{path: "lib", type: "dir"}
      ])
  """
  def create_repository_files(repository, files_attrs \\ []) do
    files_attrs =
      if Enum.empty?(files_attrs) do
        [
          %{path: "mix.exs", type: "file", size: 1024},
          %{path: "lib", type: "dir", size: 0},
          %{path: "README.md", type: "file", size: 256}
        ]
      else
        files_attrs
      end

    Enum.map(files_attrs, fn attrs ->
      create_repository_file(Map.put(attrs, :repository_id, repository.id))
    end)
  end

  @doc """
  Creates an organization with the given attributes.
  Returns the organization.
  """
  def organization_fixture(attrs \\ %{}) do
    # Extract settings from attrs to handle them separately
    settings_attrs = Map.get(attrs, :settings, %{})
    org_attrs = Map.drop(attrs, [:settings])

    # Create the organization first
    {:ok, organization} =
      %Organization{}
      |> Organization.changeset(
        Enum.into(org_attrs, %{
          name: "org-#{System.unique_integer()}"
        })
      )
      |> Repo.insert()

    # Create settings for the organization
    {:ok, _settings} =
      %OrganizationSettings{}
      |> OrganizationSettings.changeset(
        Map.merge(settings_attrs, %{
          organization_id: organization.id
        })
      )
      |> Repo.insert()

    # Reload the organization with settings
    Repo.get!(Organization, organization.id)
    |> Repo.preload(:settings)
  end

  def user_fixture(attrs \\ %{}) do
    # Create organization first if not provided
    attrs =
      if Map.has_key?(attrs, :default_organization_id) do
        attrs
      else
        organization =
          organization_fixture(%{name: attrs[:login] || "some-login-#{System.unique_integer()}"})

        Map.put(attrs, :default_organization_id, organization.id)
      end

    {:ok, user} =
      %User{}
      |> User.changeset(
        Enum.into(attrs, %{
          login: "some-login-#{System.unique_integer()}",
          token: "some token",
          refresh_token: "some refresh token",
          expires_at: 42,
          refresh_token_expires_at: System.system_time(:second) + 15_897_600,
          info: %{
            name: "some name",
            email: "some email",
            nickname: "some nickname",
            description: "some description",
            avatar_url: "some avatar_url",
            html_url: "some html_url",
            location: "some location"
          }
        })
      )
      |> Repo.insert()

    user
  end

  def user_fixture_with_organization(attrs \\ %{}) do
    organization = organization_fixture()

    {:ok, user} =
      attrs
      |> Enum.into(%{
        login: "some-login-#{System.unique_integer()}",
        token: "some token",
        refresh_token: "some refresh token",
        expires_at: 42,
        default_organization_id: organization.id,
        info: %{
          name: "some name",
          email: "some email",
          nickname: "some nickname",
          description: "some description",
          avatar_url: "some avatar_url",
          html_url: "some html_url",
          location: "some location"
        }
      })
      |> User.changeset()
      |> Repo.insert()

    %{user | default_organization: organization}
  end

  @doc """
  Creates a category for testing.
  """
  def create_category(user_or_org, attrs \\ %{})

  def create_category(%Repobot.Accounts.Organization{} = organization, attrs) do
    attrs =
      Map.merge(
        %{
          name: "Test Category #{System.unique_integer()}",
          organization_id: organization.id
        },
        attrs
      )

    {:ok, category} = Repobot.Categories.create_category(attrs)
    category
  end

  def create_category(%Repobot.Accounts.User{} = user, attrs) do
    create_category(user.default_organization, attrs)
  end

  @doc """
  Creates a pull request with the given attributes.
  Requires a source_file_id.
  """
  def create_pull_request(attrs \\ %{}) do
    source_file =
      attrs[:source_file] ||
        (attrs[:source_file_id] && Repo.get!(SourceFile, attrs[:source_file_id]))

    unless source_file do
      raise "create_pull_request requires a :source_file_id or :source_file attribute"
    end

    attrs =
      attrs
      |> Map.merge(%{
        repository: attrs[:repository] || "owner/repo-#{System.unique_integer()}",
        branch_name: attrs[:branch_name] || "feat/branch-#{System.unique_integer()}",
        pull_request_number: attrs[:pull_request_number] || :rand.uniform(1000),
        pull_request_url:
          attrs[:pull_request_url] ||
            "https://github.com/#{attrs[:repository]}/pull/#{attrs[:pull_request_number]}",
        status: attrs[:status] || "open",
        source_file_id: source_file.id
      })

    {:ok, pull_request} =
      %Repobot.PullRequest{}
      |> Repobot.PullRequest.changeset(attrs)
      |> Repo.insert()

    pull_request
  end

  @doc """
  Associates a source file with a folder.
  Returns the source file with preloaded folders.
  """
  def add_source_file_to_folder(%SourceFile{} = source_file, %Folder{} = folder) do
    {:ok, _} =
      %SourceFileFolder{}
      |> SourceFileFolder.changeset(%{
        source_file_id: source_file.id,
        folder_id: folder.id
      })
      |> Repo.insert(on_conflict: :nothing)

    source_file
    |> Repo.preload(:folders, force: true)
  end

  @doc """
  Removes the association between a source file and a folder.
  Returns the number of associations removed.
  """
  def remove_source_file_from_folder(%SourceFile{} = source_file, %Folder{} = folder) do
    from(sf in SourceFileFolder,
      where: sf.source_file_id == ^source_file.id and sf.folder_id == ^folder.id
    )
    |> Repo.delete_all()
  end
end
