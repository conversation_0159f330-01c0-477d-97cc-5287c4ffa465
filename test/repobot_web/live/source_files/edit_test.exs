defmodule RepobotWeb.Live.SourceFiles.EditTest do
  use RepobotWeb.ConnCase, async: true
  use Repobot.Test.Fixtures

  import Phoenix.LiveViewTest
  import Mox

  setup do
    user = create_user()

    source_file =
      create_source_file(%{
        name: "test.ex",
        content: "test content",
        target_path: "lib/test.ex",
        user_id: user.id,
        organization_id: user.default_organization_id
      })

    conn =
      build_conn()
      |> init_test_session(%{current_user_id: user.id})
      |> assign(:current_user, user)

    {:ok, %{user: user, source_file: source_file, conn: conn}}
  end

  describe "edit" do
    test "displays edit form with source file details", %{conn: conn, source_file: source_file} do
      {:ok, view, html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Verify page title and breadcrumbs
      assert html =~ "Edit Source File"
      assert html =~ source_file.name
      assert has_element?(view, "nav a", "Source Files")
      assert has_element?(view, "nav li", "Edit")

      # Verify form elements
      assert has_element?(view, "form")
      assert has_element?(view, "h1", "Edit Source File")
      assert has_element?(view, "p", "Update your source file content and settings")

      # Verify form fields
      assert has_element?(view, "input#source-file-name")
      assert has_element?(view, "input#source-file-target-path")
      assert has_element?(view, "input#source-file-is-template")
      assert has_element?(view, "textarea", source_file.content)
    end

    test "navigates back to source files list", %{conn: conn, source_file: source_file} do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the Source Files link in breadcrumbs
      {:ok, _view, html} =
        view
        |> element("a", "Source Files")
        |> render_click()
        |> follow_redirect(conn)

      assert html =~ "Source Files"
      assert html =~ "A list of all your source files"
    end

    test "navigates back to source file details", %{conn: conn, source_file: source_file} do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the source file name link in breadcrumbs
      {:ok, _view, html} =
        view
        |> element("a", source_file.name)
        |> render_click()
        |> follow_redirect(conn)

      assert html =~ source_file.name
      assert html =~ "Source Files"
    end

    test "updates source file with valid data", %{conn: conn, source_file: source_file} do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      view
      |> form("#source-file-form", %{
        source_file: %{
          name: "updated_test.ex",
          content: "updated content",
          target_path: "lib/updated_test.ex",
          is_template: true,
          tags: "elixir, test"
        }
      })
      |> render_submit()

      assert render(view) =~ "Source file updated successfully"
    end

    test "shows validation errors", %{conn: conn, source_file: source_file} do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Submit the form with invalid data (empty name)
      html =
        view
        |> form("#source-file-form", %{
          source_file: %{
            name: "",
            content: "test content",
            target_path: "lib/test.ex"
          }
        })
        |> render_change()

      assert html =~ "can&#39;t be blank"
    end

    test "shows template variables modal when clicking the help button", %{
      conn: conn,
      source_file: source_file
    } do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Initially, check that the help button is present
      assert has_element?(view, "button[phx-click=\"toggle_template_modal\"]")

      # Click the help button
      html = view |> element("button[phx-click=\"toggle_template_modal\"]") |> render_click()

      # Verify that clicking the button shows the modal with template variables
      assert html =~ "Template Variables"
      assert html =~ "Use these variables in your template"
    end

    test "validates tags format", %{conn: conn, source_file: source_file} do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Submit form with valid tags
      html =
        view
        |> form("#source-file-form", %{
          source_file: %{
            name: source_file.name,
            content: source_file.content,
            target_path: source_file.target_path,
            tags: "tag1, tag2, tag3"
          }
        })
        |> render_change()

      # No validation errors should be shown
      refute html =~ "is invalid"

      # Submit form with invalid tags (using semicolons instead of commas)
      html =
        view
        |> form("#source-file-form", %{
          source_file: %{
            name: source_file.name,
            content: source_file.content,
            target_path: source_file.target_path,
            tags: "tag1; tag2; tag3"
          }
        })
        |> render_change()

      # Tags should still be processed (semicolons are just treated as part of tag names)
      assert html =~ "tag1; tag2; tag3"
    end

    test "shows correct template conversion warning for files without .liquid extension", %{
      conn: conn,
      user: user
    } do
      # Create a source file with a source repository to trigger the warning
      source_repository = create_repository(%{user_id: user.id, name: "test-repo"})

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: source_repository.id
        })

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the template checkbox to trigger the warning
      html = view |> element("#source-file-is-template") |> render_click()

      # Verify the warning shows correct filenames
      assert html =~ "Template Conversion Warning"
      assert html =~ "Rename the file from"
      assert html =~ "CONTRIBUTING.md"
      assert html =~ "to"
      assert html =~ "CONTRIBUTING.md.liquid"
      assert html =~ "Set the target path to"
      assert html =~ "CONTRIBUTING.md"
    end

    test "shows correct template conversion warning for files already with .liquid extension", %{
      conn: conn,
      user: user
    } do
      # Create a source file with a source repository and .liquid extension
      source_repository = create_repository(%{user_id: user.id, name: "test-repo"})

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md.liquid",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: source_repository.id
        })

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the template checkbox to trigger the warning
      html = view |> element("#source-file-is-template") |> render_click()

      # Verify the warning shows correct filenames (should not double-add .liquid)
      assert html =~ "Template Conversion Warning"
      assert html =~ "Rename the file from"
      assert html =~ "CONTRIBUTING.md"
      assert html =~ "to"
      assert html =~ "CONTRIBUTING.md.liquid"
      assert html =~ "Set the target path to"
      assert html =~ "CONTRIBUTING.md"
      # Should not show double .liquid extension
      refute html =~ "CONTRIBUTING.md.liquid.liquid"
    end

    test "confirms template conversion when user clicks proceed", %{
      conn: conn,
      user: user
    } do
      # Create a source file with a source repository to trigger the warning
      source_repository = create_repository(%{user_id: user.id, name: "test-repo"})

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: source_repository.id,
          is_template: false
        })

      # Mock GitHub API calls for file renaming using Git Data API
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        assert owner == "test-user"
        assert repo == "test-repo"
        test_client
      end)
      |> expect(:get_file_content, fn ^test_client, owner, repo, path ->
        assert owner == "test-user"
        assert repo == "test-repo"
        assert path == "CONTRIBUTING.md"
        {:ok, "test content", %{"sha" => "test-sha"}}
      end)
      |> expect(:rename_file, fn ^test_client,
                                 owner,
                                 repo,
                                 old_path,
                                 new_path,
                                 content,
                                 message ->
        assert owner == "test-user"
        assert repo == "test-repo"
        assert old_path == "CONTRIBUTING.md"
        assert new_path == "CONTRIBUTING.md.liquid"
        assert content == "test content"
        assert message =~ "Convert CONTRIBUTING.md to template"
        {:ok, "new-commit-sha"}
      end)

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the template checkbox to trigger the warning
      html = view |> element("#source-file-is-template") |> render_click()

      # Verify the warning is shown
      assert html =~ "Template Conversion Warning"
      assert has_element?(view, "button[phx-click=\"confirm_template_conversion\"]")

      # Click the "Proceed with Conversion" button
      html =
        view |> element("button[phx-click=\"confirm_template_conversion\"]") |> render_click()

      # Verify the warning is hidden and the checkbox is now checked
      refute html =~ "Template Conversion Warning"
      assert has_element?(view, "#source-file-is-template[checked]")

      # Verify the file was actually updated in the database
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)
      assert updated_source_file.is_template == true
      assert updated_source_file.name == "CONTRIBUTING.md.liquid"
      assert updated_source_file.target_path == "CONTRIBUTING.md"

      # Verify the form shows the updated values
      assert has_element?(view, "input[name='source_file[name]'][value='CONTRIBUTING.md.liquid']")
      assert has_element?(view, "input[name='source_file[target_path]'][value='CONTRIBUTING.md']")
    end

    test "cancels template conversion when user clicks cancel", %{
      conn: conn,
      user: user
    } do
      # Create a source file with a source repository to trigger the warning
      source_repository = create_repository(%{user_id: user.id, name: "test-repo"})

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: source_repository.id,
          is_template: false
        })

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the template checkbox to trigger the warning
      html = view |> element("#source-file-is-template") |> render_click()

      # Verify the warning is shown
      assert html =~ "Template Conversion Warning"
      assert has_element?(view, "button[phx-click=\"cancel_template_conversion\"]")

      # Click the "Cancel" button
      html = view |> element("button[phx-click=\"cancel_template_conversion\"]") |> render_click()

      # Verify the warning is hidden and the checkbox remains unchecked
      refute html =~ "Template Conversion Warning"
      refute has_element?(view, "#source-file-is-template[checked]")
    end

    test "reproduces bug: form state changes when checkbox is clicked without source repository",
         %{
           conn: conn,
           user: user
         } do
      # Create a source file WITHOUT a source repository - this will trigger immediate form update
      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          # No source repository
          source_repository_id: nil,
          is_template: false
        })

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Initial state: verify form shows original values
      initial_html = render(view)
      assert initial_html =~ "value=\"CONTRIBUTING.md\""
      assert initial_html =~ "value=\"CONTRIBUTING.md\""

      # Step 1: Check the template checkbox - this should immediately update form state
      html = view |> element("#source-file-is-template") |> render_click()

      # No warning should be shown since there's no source repository
      refute html =~ "Template Conversion Warning"

      # Step 2: Check if the form state has changed after clicking the checkbox
      # This is where the issue occurs - the form updates immediately
      _form_html_after_click = render(view)

      # The form should now show the checkbox as checked
      assert has_element?(view, "#source-file-is-template[checked]")

      # Step 3: Now manually change the target_path to simulate user interaction
      view
      |> form("#source-file-form", %{
        source_file: %{
          target_path: "CONTRIBUTING.md.liquid"
        }
      })
      |> render_change()

      # Step 4: Submit the form with the current form state
      # The form shows name="CONTRIBUTING.md.liquid" and target_path="CONTRIBUTING.md"
      _form_data = view |> form("#source-file-form") |> render_submit()

      # Verify the file was updated in the database
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)
      assert updated_source_file.is_template == true
      assert updated_source_file.name == "CONTRIBUTING.md.liquid"

      # The target_path should be correctly set to the original name
      assert updated_source_file.target_path == "CONTRIBUTING.md"
    end

    test "correctly handles template conversion when target path is manually set to .liquid filename",
         %{
           conn: conn,
           user: user
         } do
      # Create a source file with a source repository to trigger the warning
      source_repository = create_repository(%{user_id: user.id, name: "test-repo"})

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: source_repository.id,
          is_template: false
        })

      # Mock GitHub API calls for file renaming using Git Data API
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        assert owner == "test-user"
        assert repo == "test-repo"
        test_client
      end)
      |> expect(:get_file_content, fn ^test_client, owner, repo, path ->
        assert owner == "test-user"
        assert repo == "test-repo"
        assert path == "CONTRIBUTING.md"
        {:ok, "test content", %{"sha" => "test-sha"}}
      end)
      |> expect(:rename_file, fn ^test_client,
                                 owner,
                                 repo,
                                 old_path,
                                 new_path,
                                 content,
                                 message ->
        assert owner == "test-user"
        assert repo == "test-repo"
        assert old_path == "CONTRIBUTING.md"
        assert new_path == "CONTRIBUTING.md.liquid"
        assert content == "test content"
        assert message =~ "Convert CONTRIBUTING.md to template"
        {:ok, "new-commit-sha"}
      end)

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # First, manually change the target_path to include .liquid extension to simulate the bug
      view
      |> form("#source-file-form", %{
        source_file: %{
          target_path: "CONTRIBUTING.md.liquid"
        }
      })
      |> render_change()

      # Now click the template checkbox to trigger the warning
      _html = view |> element("#source-file-is-template") |> render_click()

      # Verify the warning is shown
      assert has_element?(view, "button[phx-click=\"confirm_template_conversion\"]")

      # Click the "Proceed with Conversion" button
      _html =
        view |> element("button[phx-click=\"confirm_template_conversion\"]") |> render_click()

      # Verify the file was updated in the database
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)
      assert updated_source_file.is_template == true
      assert updated_source_file.name == "CONTRIBUTING.md.liquid"

      # The target_path should be correctly set to the original filename without .liquid extension
      assert updated_source_file.target_path == "CONTRIBUTING.md"
    end

    test "reproduces bug: source file loses source_repository_id association after template conversion",
         %{
           conn: conn,
           user: user
         } do
      # Create a template repository that will serve as the source repository
      template_repository =
        create_repository(%{
          user_id: user.id,
          name: "template-repo",
          template: true
        })

      # Create a source file that was imported from the template repository
      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "# Contributing\n\nThis is a guide for {{ repo.name }}",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: template_repository.id,
          is_template: false
        })

      # Verify the source file is initially associated with the template repository
      assert source_file.source_repository_id == template_repository.id

      # Mock GitHub API calls for file renaming using Git Data API
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        assert owner == "test-user"
        assert repo == "template-repo"
        test_client
      end)
      |> expect(:get_file_content, fn ^test_client, owner, repo, path ->
        assert owner == "test-user"
        assert repo == "template-repo"
        assert path == "CONTRIBUTING.md"
        {:ok, "# Contributing\n\nThis is a guide for {{ repo.name }}", %{"sha" => "test-sha"}}
      end)
      |> expect(:rename_file, fn ^test_client,
                                 owner,
                                 repo,
                                 old_path,
                                 new_path,
                                 content,
                                 message ->
        assert owner == "test-user"
        assert repo == "template-repo"
        assert old_path == "CONTRIBUTING.md"
        assert new_path == "CONTRIBUTING.md.liquid"
        assert content == "# Contributing\n\nThis is a guide for {{ repo.name }}"
        assert message =~ "Convert CONTRIBUTING.md to template"
        {:ok, "new-commit-sha"}
      end)

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the template checkbox to trigger the warning
      html = view |> element("#source-file-is-template") |> render_click()

      # Verify the warning is shown
      assert html =~ "Template Conversion Warning"
      assert has_element?(view, "button[phx-click=\"confirm_template_conversion\"]")

      # Click the "Proceed with Conversion" button
      html =
        view |> element("button[phx-click=\"confirm_template_conversion\"]") |> render_click()

      # Verify the warning is hidden and the checkbox is now checked
      refute html =~ "Template Conversion Warning"
      assert has_element?(view, "#source-file-is-template[checked]")

      # Verify the file was updated in the database
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)
      assert updated_source_file.is_template == true
      assert updated_source_file.name == "CONTRIBUTING.md.liquid"
      assert updated_source_file.target_path == "CONTRIBUTING.md"

      # BUG: The source_repository_id should still be preserved after template conversion
      # This is the main issue - the source file loses its association with the template repository
      assert updated_source_file.source_repository_id == template_repository.id,
             "Source file should maintain its association with the template repository after conversion"

      # Verify that the source file would still appear in the source files index
      # by checking that it has a source_repository association
      source_files_list =
        Repobot.SourceFiles.list_source_files(user, user.default_organization)
        |> Repobot.Repo.preload(:source_repository)

      converted_file = Enum.find(source_files_list, &(&1.id == updated_source_file.id))
      assert converted_file != nil, "Source file should still appear in the source files list"

      assert converted_file.source_repository_id == template_repository.id,
             "Source file should maintain source repository association in listings"
    end

    test "reproduces bug: source file loses source_repository_id when template repository is deleted",
         %{user: user} do
      # Create a template repository that will serve as the source repository
      template_repository =
        create_repository(%{
          user_id: user.id,
          name: "template-repo",
          template: true
        })

      # Create a source file that was imported from the template repository
      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "# Contributing\n\nThis is a guide for {{ repo.name }}",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: template_repository.id,
          is_template: false
        })

      # Verify the source file is initially associated with the template repository
      assert source_file.source_repository_id == template_repository.id

      # Now simulate the template repository being deleted (which sets source_repository_id to nil due to on_delete: :nilify_all)
      Repobot.Repo.delete(template_repository)

      # Check that the source file's source_repository_id is now nil
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)

      assert updated_source_file.source_repository_id == nil,
             "Source file should lose source repository association when repository is deleted"

      # Verify that the source file would still appear in the source files index
      # even without a source repository association
      source_files_list =
        Repobot.SourceFiles.list_source_files(user, user.default_organization)
        |> Repobot.Repo.preload(:source_repository)

      orphaned_file = Enum.find(source_files_list, &(&1.id == updated_source_file.id))

      assert orphaned_file != nil,
             "Source file should still appear in the source files list even without source repository"

      assert orphaned_file.source_repository_id == nil,
             "Source file should have nil source_repository_id"

      assert orphaned_file.source_repository == nil,
             "Source file should have nil source_repository association"
    end

    test "reproduces bug: source file loses source_repository_id when form doesn't include it",
         %{user: user} do
      # Create a template repository that will serve as the source repository
      template_repository =
        create_repository(%{
          user_id: user.id,
          name: "template-repo",
          template: true
        })

      # Create a source file that was imported from the template repository
      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "# Contributing\n\nThis is a guide for {{ repo.name }}",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: template_repository.id,
          is_template: false
        })

      # Verify the source file is initially associated with the template repository
      assert source_file.source_repository_id == template_repository.id

      # Mock GitHub API calls for file renaming since we're converting to template
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        assert owner == "test-user"
        assert repo == "template-repo"
        test_client
      end)
      |> expect(:get_file_content, fn ^test_client, owner, repo, path ->
        assert owner == "test-user"
        assert repo == "template-repo"
        assert path == "CONTRIBUTING.md"
        {:ok, "# Contributing\n\nThis is a guide for {{ repo.name }}", %{"sha" => "test-sha"}}
      end)
      |> expect(:rename_file, fn ^test_client,
                                 owner,
                                 repo,
                                 old_path,
                                 new_path,
                                 content,
                                 message ->
        assert owner == "test-user"
        assert repo == "template-repo"
        assert old_path == "CONTRIBUTING.md"
        assert new_path == "CONTRIBUTING.md.liquid"
        assert content == "# Contributing\n\nThis is a guide for {{ repo.name }}"
        assert message =~ "Convert CONTRIBUTING.md to template"
        {:ok, "new-commit-sha"}
      end)

      # Simulate a form submission that doesn't include source_repository_id
      # This could happen if the form component doesn't include this field
      form_params = %{
        "name" => "CONTRIBUTING.md.liquid",
        "content" => "# Contributing\n\nThis is a guide for {{ repo.name }}",
        "target_path" => "CONTRIBUTING.md",
        "is_template" => true
      }

      # Update the source file with these limited parameters
      case Repobot.SourceFiles.update_source_file(source_file, form_params) do
        {:ok, updated_source_file} ->
          # The source_repository_id should be preserved even if not included in the form
          assert updated_source_file.source_repository_id == template_repository.id,
                 "Source file should maintain source repository association even when not included in form params"

        {:error, reason} ->
          flunk("Update should succeed: #{inspect(reason)}")
      end
    end

    test "reproduces bug: webhook handler breaks source file association when file is renamed",
         %{user: user} do
      # Create a template repository
      template_repository =
        create_repository(%{
          user_id: user.id,
          name: "template-repo",
          template: true
        })

      # Create a repository file that represents the original file in the template repository
      {:ok, original_repo_file} =
        Repobot.RepositoryFiles.create_repository_file(%{
          repository_id: template_repository.id,
          path: "testfile",
          name: "testfile",
          type: "file",
          size: 100,
          sha: "original-sha",
          content: "# Test file content for {{ repo.name }}"
        })

      # Create a source file that was imported from this repository file
      source_file =
        create_source_file(%{
          name: "testfile",
          content: "# Test file content for {{ repo.name }}",
          target_path: "testfile",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: template_repository.id,
          is_template: false
        })

      # Verify the source file is initially associated with the template repository
      assert source_file.source_repository_id == template_repository.id

      # Verify the repository file exists
      assert Repobot.RepositoryFiles.get_repository_file_by_path(
               template_repository.id,
               "testfile"
             ) != nil

      # Now simulate what happens during webhook processing when a file is renamed
      # Step 1: Delete the original repository file (simulating the "removed" action)
      {:ok, _} = Repobot.RepositoryFiles.delete_repository_file(original_repo_file)

      # Step 2: Create a new repository file for the renamed file (simulating the "added" action)
      {:ok, _new_repo_file} =
        Repobot.RepositoryFiles.create_repository_file(%{
          repository_id: template_repository.id,
          path: "testfile.liquid",
          name: "testfile.liquid",
          type: "file",
          size: 150,
          sha: "new-sha",
          content: "# Test file content for {{ repo.name }}"
        })

      # Check that the original repository file was deleted
      assert Repobot.RepositoryFiles.get_repository_file_by_path(
               template_repository.id,
               "testfile"
             ) == nil

      # Check that a new repository file was created for the renamed file
      new_repo_file =
        Repobot.RepositoryFiles.get_repository_file_by_path(
          template_repository.id,
          "testfile.liquid"
        )

      assert new_repo_file != nil

      # The source file should still exist and maintain its source_repository_id
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)
      assert updated_source_file.source_repository_id == template_repository.id

      # However, the source file is now "orphaned" because it references a file name that no longer exists
      # in the repository files table. This is the bug - the source file should be updated to reference
      # the new file name or the association should be preserved in some other way.

      # Verify that the source file would still appear in the source files index
      source_files_list =
        Repobot.SourceFiles.list_source_files(user, user.default_organization)
        |> Repobot.Repo.preload(:source_repository)

      orphaned_file = Enum.find(source_files_list, &(&1.id == updated_source_file.id))
      assert orphaned_file != nil, "Source file should still appear in the source files list"

      # But the issue is that the source file's name doesn't match any repository file anymore
      # This would cause issues when trying to sync or display the file
      assert orphaned_file.name == "testfile"
      assert new_repo_file.name == "testfile.liquid"

      # This mismatch is the core of the bug - the source file name and the repository file name
      # are now out of sync after the webhook processing
    end

    test "fix: webhook handler preserves source file association during template conversion",
         %{user: user} do
      # Create a template repository
      template_repository =
        create_repository(%{
          user_id: user.id,
          name: "template-repo",
          template: true
        })

      # Create a repository file that represents the original file in the template repository
      {:ok, _original_repo_file} =
        Repobot.RepositoryFiles.create_repository_file(%{
          repository_id: template_repository.id,
          path: "testfile",
          name: "testfile",
          type: "file",
          size: 100,
          sha: "original-sha",
          content: "# Test file content for {{ repo.name }}"
        })

      # Create a source file that was imported from this repository file
      source_file =
        create_source_file(%{
          name: "testfile",
          content: "# Test file content for {{ repo.name }}",
          target_path: "testfile",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: template_repository.id,
          is_template: false
        })

      # Verify the source file is initially associated with the template repository
      assert source_file.source_repository_id == template_repository.id

      # Verify the repository file exists
      assert Repobot.RepositoryFiles.get_repository_file_by_path(
               template_repository.id,
               "testfile"
             ) != nil

      # Mock GitHub API calls that will be made when updating the source file to template
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        assert owner == "test-user"
        assert repo == "template-repo"
        test_client
      end)
      |> expect(:get_file_content, fn ^test_client, owner, repo, path ->
        assert owner == "test-user"
        assert repo == "template-repo"
        assert path == "testfile"
        {:ok, "# Test file content for {{ repo.name }}", %{"sha" => "test-sha"}}
      end)
      |> expect(:rename_file, fn ^test_client,
                                 owner,
                                 repo,
                                 old_path,
                                 new_path,
                                 content,
                                 message ->
        assert owner == "test-user"
        assert repo == "template-repo"
        assert old_path == "testfile"
        assert new_path == "testfile.liquid"
        assert content == "# Test file content for {{ repo.name }}"
        assert message =~ "Convert testfile to template"
        {:ok, "new-commit-sha"}
      end)

      # Now simulate the webhook processing with the fix
      # This should detect the template conversion and update the source file accordingly
      changed_files = [
        %{path: "testfile", action: :removed},
        %{path: "testfile.liquid", action: :added}
      ]

      # Call the new detection function to verify it works
      renames = Repobot.Handlers.GitHub.PushHandler.detect_template_renames(changed_files)
      assert renames == %{"testfile" => "testfile.liquid"}

      # Simulate the template conversion rename handling
      {:ok, _result} =
        Repobot.Handlers.GitHub.PushHandler.handle_template_conversion_rename(
          template_repository,
          "testfile",
          "testfile.liquid"
        )

      # Check that the original repository file was deleted
      assert Repobot.RepositoryFiles.get_repository_file_by_path(
               template_repository.id,
               "testfile"
             ) == nil

      # The source file should now be updated to use the new name and be marked as a template
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)
      assert updated_source_file.source_repository_id == template_repository.id
      assert updated_source_file.name == "testfile.liquid"
      assert updated_source_file.target_path == "testfile"
      assert updated_source_file.is_template == true

      # Verify that the source file still appears in the source files index
      source_files_list =
        Repobot.SourceFiles.list_source_files(user, user.default_organization)
        |> Repobot.Repo.preload(:source_repository)

      updated_file = Enum.find(source_files_list, &(&1.id == updated_source_file.id))
      assert updated_file != nil, "Source file should still appear in the source files list"
      assert updated_file.source_repository_id == template_repository.id
      assert updated_file.name == "testfile.liquid"

      # The source file name now matches the pattern expected for templates
      # and maintains its association with the template repository
    end

    test "fix: webhook handler handles already converted source files correctly",
         %{user: user} do
      # Create a template repository
      template_repository =
        create_repository(%{
          user_id: user.id,
          name: "template-repo",
          template: true
        })

      # Create a repository file that represents the original file in the template repository
      {:ok, _original_repo_file} =
        Repobot.RepositoryFiles.create_repository_file(%{
          repository_id: template_repository.id,
          path: "settings.yml",
          name: "settings.yml",
          type: "file",
          size: 100,
          sha: "original-sha",
          content: "# Settings for {{ repo.name }}"
        })

      # Create a source file that was ALREADY converted to template (this is the real-world scenario)
      # The source file already has the .liquid name and is marked as template
      source_file =
        create_source_file(%{
          name: "settings.yml.liquid",
          content: "# Settings for {{ repo.name }}",
          target_path: "settings.yml",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: template_repository.id,
          is_template: true
        })

      # Verify the source file is initially associated with the template repository
      assert source_file.source_repository_id == template_repository.id
      assert source_file.name == "settings.yml.liquid"
      assert source_file.target_path == "settings.yml"
      assert source_file.is_template == true

      # Verify the repository file exists
      assert Repobot.RepositoryFiles.get_repository_file_by_path(
               template_repository.id,
               "settings.yml"
             ) != nil

      # Now simulate the webhook processing with the fix
      # This should detect the template conversion and handle the already-converted source file
      changed_files = [
        %{path: "settings.yml", action: :removed},
        %{path: "settings.yml.liquid", action: :added}
      ]

      # Call the new detection function to verify it works
      renames = Repobot.Handlers.GitHub.PushHandler.detect_template_renames(changed_files)
      assert renames == %{"settings.yml" => "settings.yml.liquid"}

      # Simulate the template conversion rename handling
      # This should find the already-converted source file and just delete the old repository file
      {:ok, _result} =
        Repobot.Handlers.GitHub.PushHandler.handle_template_conversion_rename(
          template_repository,
          "settings.yml",
          "settings.yml.liquid"
        )

      # Check that the original repository file was deleted
      assert Repobot.RepositoryFiles.get_repository_file_by_path(
               template_repository.id,
               "settings.yml"
             ) == nil

      # The source file should remain unchanged since it was already correctly converted
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)
      assert updated_source_file.source_repository_id == template_repository.id
      assert updated_source_file.name == "settings.yml.liquid"
      assert updated_source_file.target_path == "settings.yml"
      assert updated_source_file.is_template == true

      # Verify that the source file still appears in the source files index
      source_files_list =
        Repobot.SourceFiles.list_source_files(user, user.default_organization)
        |> Repobot.Repo.preload(:source_repository)

      found_file = Enum.find(source_files_list, &(&1.id == updated_source_file.id))
      assert found_file != nil, "Source file should still appear in the source files list"
      assert found_file.source_repository_id == template_repository.id
      assert found_file.name == "settings.yml.liquid"

      # The source file maintains its association with the template repository
      # even when the webhook processes the file rename
    end

    test "fix: repository show page correctly marks template files as imported",
         %{user: user} do
      # Create a template repository
      template_repository =
        create_repository(%{
          user_id: user.id,
          name: "template-repo",
          template: true
        })

      # Create a repository file for the template file
      {:ok, _repo_file} =
        Repobot.RepositoryFiles.create_repository_file(%{
          repository_id: template_repository.id,
          path: "settings.yml.liquid",
          name: "settings.yml.liquid",
          type: "file",
          size: 150,
          sha: "template-sha",
          content: "# Settings for {{ repo.name }}"
        })

      # Create a source file that was imported from this template repository
      source_file =
        create_source_file(%{
          name: "settings.yml.liquid",
          content: "# Settings for {{ repo.name }}",
          target_path: "settings.yml",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: template_repository.id,
          is_template: true
        })

      # Reload the repository with imported files
      template_repository =
        Repobot.Repositories.get_repository!(template_repository.id)

      # Test the find_source_files function directly
      item = %{"path" => "settings.yml.liquid", "type" => "file"}

      # This should find the source file even though:
      # - Repository file path is "settings.yml.liquid"
      # - Source file target_path is "settings.yml"
      # - Source file is_template is true
      found_files = RepobotWeb.Live.Repositories.Show.find_source_files(item, template_repository)

      assert length(found_files) == 1
      assert hd(found_files).id == source_file.id
      assert hd(found_files).target_path == "settings.yml"
      assert hd(found_files).name == "settings.yml.liquid"
      assert hd(found_files).is_template == true

      # Test the is_imported? function
      is_imported = RepobotWeb.Live.Repositories.Show.is_imported?(item, template_repository)
      assert is_imported == true
    end

    test "webhook handler updates source file content when template repository file is updated",
         %{user: user} do
      # Create a template repository
      template_repository =
        create_repository(%{
          user_id: user.id,
          name: "template-repo",
          template: true
        })

      # Create a repository file for the template file
      {:ok, repo_file} =
        Repobot.RepositoryFiles.create_repository_file(%{
          repository_id: template_repository.id,
          path: "settings.yml.liquid",
          name: "settings.yml.liquid",
          type: "file",
          size: 150,
          sha: "original-sha",
          content: "# Original settings for {{ repo.name }}"
        })

      # Create a source file that was imported from this template repository
      source_file =
        create_source_file(%{
          name: "settings.yml.liquid",
          content: "# Original settings for {{ repo.name }}",
          target_path: "settings.yml",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: template_repository.id,
          is_template: true
        })

      # Verify initial state
      assert source_file.content == "# Original settings for {{ repo.name }}"
      assert repo_file.content == "# Original settings for {{ repo.name }}"

      # Mock GitHub API to return updated content
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn user_param ->
        # The webhook handler gets a user from the organization for repository file refresh
        assert user_param.id == user.id
        test_client
      end)
      |> expect(:client, fn owner, repo ->
        # The webhook handler creates a client for the template repository
        assert owner == "test-user"
        assert repo == "template-repo"
        test_client
      end)
      |> expect(:get_file_content, fn ^test_client, owner, repo, path ->
        # This is called by refresh_repository_files to update the repository file
        assert owner == "test-user"
        assert repo == "template-repo"
        assert path == "settings.yml.liquid"
        {:ok, "# Updated settings for {{ repo.name }}", %{"sha" => "new-sha", "size" => 50}}
      end)
      |> expect(:get_file_content, fn ^test_client, owner, repo, path, commit_sha ->
        # This is called by the source file update logic
        assert owner == "test-user"
        assert repo == "template-repo"
        assert path == "settings.yml.liquid"
        assert commit_sha == "new-commit-sha"
        {:ok, "# Updated settings for {{ repo.name }}", %{"sha" => "new-sha"}}
      end)

      # Simulate a webhook push event that updates the template file
      webhook_payload = %{
        "repository" => %{
          "full_name" => "test-user/template-repo",
          "default_branch" => "main"
        },
        "ref" => "refs/heads/main",
        "commits" => [
          %{
            "id" => "new-commit-sha",
            "message" => "Update settings template",
            "modified" => ["settings.yml.liquid"],
            "added" => []
          }
        ]
      }

      # Process the webhook
      result = Repobot.Handlers.GitHub.PushHandler.handle(webhook_payload)

      # The webhook should process successfully and find source files to update
      assert {:ok, _} = result

      # FIXED: Both repository file and source file updates are asynchronous
      # Use assert_eventually for both because the webhook handler runs repository file refresh asynchronously
      # and the source file update depends on the repository file being updated first
      assert_eventually(
        fn ->
          # Check that the repository file was updated first
          updated_repo_file = Repobot.RepositoryFiles.get_repository_file!(repo_file.id)
          assert updated_repo_file.content == "# Updated settings for {{ repo.name }}"

          # Then check that the source file content was also updated
          updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)

          # This assertion now passes because the webhook handler correctly finds the source file
          # by mapping repository file paths (with .liquid) to source file target paths (without .liquid)
          assert updated_source_file.content == "# Updated settings for {{ repo.name }}",
                 "Source file content should be updated when template repository file is updated via webhook"
        end,
        5000
      )
    end
  end
end
