defmodule RepobotWeb.Live.SourceFiles.ShowTest do
  use RepobotWeb.ConnCase

  import ExUnit.CaptureLog
  import Phoenix.LiveViewTest
  import Repobot.Test.Fixtures
  import Mox

  require Logger

  setup :verify_on_exit!

  setup do
    user = create_user()

    # Create a source repository first
    source_repository =
      create_repository(%{
        name: "source-repo",
        owner: "source-owner",
        full_name: "source-owner/source-repo",
        user_id: user.id,
        organization_id: user.default_organization_id,
        settings: %{"provider" => "github"}
      })

    # Create a source file with source repository
    source_file =
      create_source_file(%{
        name: "test.ex",
        content: "test content",
        target_path: "lib/test.ex",
        user_id: user.id,
        organization_id: user.default_organization_id,
        source_repository_id: source_repository.id
      })

    # Create two repositories with VCS provider settings
    repository1 =
      create_repository(%{
        name: "repo1",
        owner: "owner1",
        full_name: "owner1/repo1",
        user_id: user.id,
        organization_id: user.default_organization_id,
        settings: %{"provider" => "github"}
      })

    repository2 =
      create_repository(%{
        name: "repo2",
        owner: "owner2",
        full_name: "owner2/repo2",
        user_id: user.id,
        organization_id: user.default_organization_id,
        settings: %{"provider" => "github"}
      })

    # Associate repositories with the source file
    Repobot.Repositories.add_source_file(repository1, source_file)
    Repobot.Repositories.add_source_file(repository2, source_file)

    {:ok,
     user: user,
     source_file: source_file,
     repository1: repository1,
     repository2: repository2,
     source_repository: source_repository}
  end

  describe "show source file" do
    test "displays source file details and repositories", %{
      conn: conn,
      user: user,
      source_file: source_file,
      repository1: repository1,
      repository2: repository2
    } do
      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> :test_client end)
      |> expect(:get_file_status, 2, fn :test_client, owner, repo, _path ->
        case "#{owner}/#{repo}" do
          "owner1/repo1" -> {:ok, :exists}
          "owner2/repo2" -> {:ok, :exists}
        end
      end)

      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files/#{source_file}")

      # Initially, we should see loading indicators
      assert html =~ "animate-spin"

      # Wait for the status checks to complete
      assert render(view) =~ "File exists"

      # Verify source file name and path are displayed
      assert has_element?(view, "h1", source_file.name)
      assert render(view) =~ source_file.target_path

      # Verify repositories are listed
      assert has_element?(view, "a", repository1.full_name)
      assert has_element?(view, "a", repository2.full_name)
    end

    test "creates pull requests when clicking Create PRs button", %{
      conn: conn,
      user: user,
      source_file: source_file
    } do
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> test_client end)
      |> expect(:get_file_status, 2, fn ^test_client, owner, repo, _path ->
        case "#{owner}/#{repo}" do
          "owner1/repo1" -> {:ok, :exists}
          "owner2/repo2" -> {:ok, :exists}
        end
      end)
      |> expect(:client, 1, fn "source-owner", "source-repo" -> test_client end)
      |> expect(:get_file_content, 2, fn ^test_client, owner, _repo, _path ->
        case owner do
          "owner1" -> {:ok, "existing content", %{"sha" => "def456"}}
          "owner2" -> {:error, 404}
        end
      end)
      |> expect(:get_repo, 2, fn ^test_client, owner, _repo ->
        case owner do
          "owner1" -> {200, %{"default_branch" => "main"}, nil}
          "owner2" -> {404, %{"message" => "Not Found"}, nil}
        end
      end)
      |> expect(:get_ref, fn ^test_client, "owner1", "repo1", "heads/main" ->
        {200, %{"object" => %{"sha" => "abc123"}}, nil}
      end)
      |> expect(:create_ref, fn ^test_client, "owner1", "repo1", ref, "abc123" ->
        {201, %{"ref" => ref}, nil}
      end)
      |> expect(:update_file, fn ^test_client, "owner1", "repo1", _, _, _, "def456", _ ->
        {200, %{}, nil}
      end)
      |> expect(:create_pull_request, fn ^test_client, "owner1", "repo1", _, _, _, _ ->
        {201,
         %{
           "number" => 1,
           "html_url" => "https://github.com/owner1/repo1/pull/1"
         }, nil}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files/#{source_file}")

      # Wait for initial load to complete
      assert render(view) =~ "File exists"

      # Verify Create PRs button is present and enabled
      assert has_element?(view, "button", "Create PRs")
      refute has_element?(view, "button[disabled]", "Create PRs")

      # Click the Create PRs button
      view |> element("button", "Create PRs") |> render_click()

      assert_eventually(
        fn ->
          html = render(view)
          assert has_element?(view, "button[disabled]", "Create PRs")

          assert html =~ "https://github.com/owner1/repo1/pull/1"
          assert html =~ "Error: Failed to get repository info: 404"
        end,
        5000
      )
    end

    test "handles PR creation errors gracefully", %{
      conn: conn,
      user: user,
      source_file: source_file
    } do
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> test_client end)
      |> expect(:get_file_status, 2, fn ^test_client, owner, repo, _path ->
        case "#{owner}/#{repo}" do
          "owner1/repo1" -> {:ok, :exists}
          "owner2/repo2" -> {:ok, :exists}
        end
      end)
      |> expect(:client, 1, fn "source-owner", "source-repo" -> test_client end)
      |> expect(:get_file_content, 2, fn ^test_client, owner, _repo, _path ->
        case owner do
          "owner1" -> {:ok, "existing content", %{"sha" => "def456"}}
          "owner2" -> {:error, 404}
        end
      end)
      |> expect(:get_repo, 2, fn ^test_client, owner, _repo ->
        case owner do
          "owner1" -> {200, %{"default_branch" => "main"}, nil}
          "owner2" -> {404, %{"message" => "Not Found"}, nil}
        end
      end)
      |> expect(:get_ref, fn ^test_client, "owner1", "repo1", "heads/main" ->
        {200, %{"object" => %{"sha" => "abc123"}}, nil}
      end)
      |> expect(:create_ref, fn ^test_client, "owner1", "repo1", ref, "abc123" ->
        {201, %{"ref" => ref}, nil}
      end)
      |> expect(:update_file, fn ^test_client, "owner1", "repo1", _, _, _, "def456", _ ->
        {200, %{}, nil}
      end)
      |> expect(:create_pull_request, fn ^test_client, "owner1", "repo1", _, _, _, _ ->
        {201,
         %{
           "number" => 1,
           "html_url" => "https://github.com/owner1/repo1/pull/1"
         }, nil}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files/#{source_file}")

      # Wait for initial load to complete
      assert render(view) =~ "File exists"

      log =
        capture_log(fn ->
          # Click the Create PRs button
          view |> element("button", "Create PRs") |> render_click()
          # Let the async operation complete
          Process.sleep(100)
        end)

      # Verify error is logged
      assert log =~
               "Failed to create PR for owner2/repo2: \"Failed to get repository info: 404 - %{\\\"message\\\" => \\\"Not Found\\\"}\""

      # Verify mixed results are displayed
      html = render(view)
      assert html =~ "https://github.com/owner1/repo1/pull/1"
      assert html =~ "Error: Failed to get repository info: 404"
    end

    test "updates files directly when clicking Update button", %{
      conn: conn,
      user: user,
      source_file: source_file
    } do
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> test_client end)
      |> expect(:get_file_status, 2, fn ^test_client, owner, repo, _path ->
        case "#{owner}/#{repo}" do
          "owner1/repo1" -> {:ok, :exists}
          "owner2/repo2" -> {:ok, :exists}
        end
      end)
      |> expect(:client, 2, fn _owner, _repo ->
        test_client
      end)
      |> expect(:get_file_content, 2, fn ^test_client, _owner, _repo, _path ->
        {:ok, "old content", %{"sha" => "def456"}}
      end)
      # Expect 2 calls now
      |> expect(
        :update_file,
        2,
        # Accept any owner
        # Accept any repo
        fn ^test_client, _owner, _repo, _, _, _, "def456", _ ->
          {200, %{}, nil}
        end
      )

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files/#{source_file}")

      # Wait for initial load to complete
      assert render(view) =~ "File exists"

      # Click the Update button
      view |> element("button", "Update") |> render_click()

      # Verify update results are displayed using assert_eventually
      assert_eventually(fn ->
        html = render(view)
        # The success message should appear for both repositories
        assert html =~ "File updated successfully"
        # We can be more specific and check it appears twice
        assert String.split(html, "File updated successfully") |> length() == 3
      end)
    end

    test "handles update errors gracefully", %{
      conn: conn,
      user: user,
      source_file: source_file
    } do
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> test_client end)
      |> expect(:get_file_status, 2, fn ^test_client, owner, repo, _path ->
        case "#{owner}/#{repo}" do
          "owner1/repo1" -> {:ok, :exists}
          "owner2/repo2" -> {:ok, :exists}
        end
      end)
      |> expect(:client, 2, fn _owner, _repo -> test_client end)
      |> expect(:get_file_content, 2, fn ^test_client, owner, _repo, _path ->
        case owner do
          "owner1" -> {:error, 422}
          "owner2" -> {:ok, "old content", %{"sha" => "def456"}}
        end
      end)
      |> expect(:update_file, 1, fn ^test_client, "owner2", "repo2", _, _, _, "def456", _ ->
        {200, %{}, nil}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files/#{source_file}")

      # Wait for initial load to complete
      assert render(view) =~ "File exists"

      # Click the Update button
      log =
        capture_log(fn ->
          view |> element("button", "Update") |> render_click()
          # Allow time for the async task to run and log
          Process.sleep(100)
        end)

      # Verify error message is displayed in logs (match exact logged format)
      assert log =~ "Failed to update in owner1/repo1: 422"

      # Also verify the final rendered state shows the error for repo1 and success for repo2
      assert_eventually(
        fn ->
          html = render(view)
          assert html =~ "Error: 422"
          assert html =~ "File updated successfully"
        end,
        5000
      )
    end

    test "handles installation errors gracefully", %{
      conn: conn,
      user: user,
      source_file: source_file
    } do
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> test_client end)
      |> expect(:get_file_status, 2, fn ^test_client, owner, repo, _path ->
        case "#{owner}/#{repo}" do
          "owner1/repo1" -> {:ok, :not_found}
          "owner2/repo2" -> {:error, 422}
        end
      end)
      |> expect(:client, 1, fn "owner1", "repo1" -> test_client end)
      |> expect(:create_file, 1, fn ^test_client, "owner1", "repo1", _, _, _, nil ->
        {201, %{}, nil}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files/#{source_file}")

      # Wait for initial load to complete
      assert render(view) =~ "File not installed"

      # Click the Install button and wait for the async operation to complete
      view |> element("button", "Install") |> render_click()

      # Verify the final rendered state shows the error for repo2 and success for repo1
      assert_eventually(
        fn ->
          html = render(view)
          assert html =~ "Error: 422"
          assert html =~ "File created successfully"
        end,
        5000
      )
    end

    test "installs files when clicking Install button", %{
      conn: conn,
      user: user,
      source_file: source_file
    } do
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> test_client end)
      |> expect(:get_file_status, 2, fn ^test_client, owner, repo, _path ->
        case "#{owner}/#{repo}" do
          "owner1/repo1" -> {:ok, :not_found}
          "owner2/repo2" -> {:ok, :not_found}
        end
      end)
      |> expect(:client, 2, fn owner, repo ->
        assert "#{owner}/#{repo}" in ["owner1/repo1", "owner2/repo2"]
        test_client
      end)
      |> expect(:create_file, 2, fn ^test_client, owner, repo, _, _, _, nil ->
        assert "#{owner}/#{repo}" in ["owner1/repo1", "owner2/repo2"]
        {201, %{}, nil}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files/#{source_file}")

      # Wait for initial load to complete
      assert render(view) =~ "File not installed"

      # Click the Install button
      view |> element("button", "Install") |> render_click()

      # Verify both files are installed
      assert_eventually(
        fn ->
          html = render(view)
          assert html =~ "File created successfully"
          # Check it appears twice
          assert String.split(html, "File created successfully") |> length() == 3
        end,
        5000
      )
    end

    test "skips PR creation when content is identical", %{
      conn: conn,
      user: user,
      source_file: source_file
    } do
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> test_client end)
      |> expect(:get_file_status, 2, fn ^test_client, owner, repo, _path ->
        case "#{owner}/#{repo}" do
          "owner1/repo1" -> {:ok, :exists}
          "owner2/repo2" -> {:ok, :exists}
        end
      end)
      |> expect(:client, 1, fn "source-owner", "source-repo" -> test_client end)
      |> expect(:get_file_content, 2, fn ^test_client, _owner, _repo, _path ->
        {:ok, "test content", %{"sha" => "def456"}}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files/#{source_file}")

      # Wait for initial load to complete
      assert render(view) =~ "File exists"

      # Click the Create PRs button
      view |> element("button", "Create PRs") |> render_click()

      assert_eventually(
        fn ->
          html = render(view)
          assert html =~ "Content is already up to date"
        end,
        5000
      )
    end

    test "compares files between repositories successfully", %{
      conn: conn,
      user: user,
      source_file: source_file
    } do
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn _user -> test_client end)
      |> expect(:get_file_status, 2, fn ^test_client, owner, repo, _path ->
        case "#{owner}/#{repo}" do
          "owner1/repo1" -> {:ok, :exists}
          "owner2/repo2" -> {:ok, :exists}
        end
      end)
      |> expect(:get_file_content, 2, fn ^test_client, owner, repo, _path ->
        content =
          case "#{owner}/#{repo}" do
            "owner1/repo1" -> "content from repo1"
            "owner2/repo2" -> "content from repo2"
          end

        {:ok, content, %{"sha" => "def456"}}
      end)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/source-files/#{source_file}")

      # Wait for initial load to complete
      assert render(view) =~ "File exists"

      # Select both repositories
      view
      |> element("input[value=\"owner1/repo1\"]")
      |> render_click()

      view
      |> element("input[value=\"owner2/repo2\"]")
      |> render_click()

      # Click the Compare Selected button
      assert view |> element("button", "Compare Selected") |> render_click()

      # Verify the diff modal is shown with correct title
      html = render(view)
      assert html =~ "File diff between repositories"
    end
  end
end
