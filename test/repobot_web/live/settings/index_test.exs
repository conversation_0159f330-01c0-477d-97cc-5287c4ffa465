defmodule RepobotWeb.Live.Settings.IndexTest do
  use RepobotWeb.ConnCase, async: true

  import Phoenix.LiveViewTest

  describe "settings page" do
    test "renders user's GitHub information", %{conn: conn} do
      # Create a test user with specific information
      user =
        create_user(%{
          login: "test-github-user",
          info: %{
            name: "Test User",
            email: "<EMAIL>",
            nickname: "test-github-user",
            avatar_url: "https://example.com/avatar.png",
            html_url: "https://github.com/test-github-user",
            description: "Test user bio",
            location: "Test Location"
          }
        })

      # Visit the settings page
      {:ok, view, html} =
        conn
        |> init_test_session(%{current_user_id: user.id})
        |> live(~p"/settings")

      # Verify the page rendered successfully
      assert html =~ "Settings"

      # Check that user GitHub login is visible
      assert html =~ "test-github-user"

      # Check that user email is visible
      assert html =~ "<EMAIL>"

      # Verify specific elements exist on the page
      assert has_element?(view, "input[value='test-github-user']")
      assert has_element?(view, "input[value='<EMAIL>']")
    end
  end
end
