defmodule RepobotWeb.Live.Dashboard.IndexTest do
  use RepobotWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  use Repobot.Test.Fixtures
  alias Repobot.Events.Event
  alias Repobot.PullRequest
  alias Repobot.Repo

  setup do
    user = create_user()
    # Organization is already created with the user
    org = user.default_organization

    # Create repositories with names
    repo1 =
      create_repository(%{
        organization_id: org.id,
        name: "repo1",
        owner: user.login,
        full_name: "#{user.login}/repo1"
      })

    repo2 =
      create_repository(%{
        organization_id: org.id,
        name: "repo2",
        owner: user.login,
        full_name: "#{user.login}/repo2"
      })

    # Create repository files for the repos
    create_repository_files(repo1)
    create_repository_files(repo2)

    # Create a source file for the PR
    source_file =
      create_source_file(%{
        name: "test-file.ex",
        content: "defmodule Test do\nend",
        user_id: user.id,
        organization_id: org.id,
        target_path: "lib/test.ex"
      })

    # Create some events with proper event types
    %Event{}
    |> Event.changeset(%{
      type: "repobot.sync",
      payload: %{"commit_sha" => "abc1234", "file_ids" => [1, 2, 3]},
      status: "completed",
      organization_id: org.id,
      user_id: user.id,
      repository_id: repo1.id
    })
    |> Repobot.Repo.insert!()

    %Event{}
    |> Event.changeset(%{
      type: "github.push",
      payload: %{"commits" => [%{"message" => "Test commit"}], "ref" => "refs/heads/main"},
      status: "completed",
      organization_id: org.id,
      user_id: user.id,
      repository_id: repo1.id
    })
    |> Repobot.Repo.insert!()

    %Event{}
    |> Event.changeset(%{
      type: "github.pull_request",
      payload: %{"action" => "opened", "number" => 1},
      status: "completed",
      organization_id: org.id,
      user_id: user.id,
      repository_id: repo1.id
    })
    |> Repobot.Repo.insert!()

    # Create a PR
    %PullRequest{}
    |> PullRequest.changeset(%{
      repository: "#{user.login}/repo1",
      branch_name: "feature/test",
      pull_request_number: 1,
      pull_request_url: "https://github.com/#{user.login}/repo1/pull/1",
      status: "open",
      source_file_id: source_file.id
    })
    |> Repobot.Repo.insert!()

    %{user: user, organization: org, repos: [repo1, repo2]}
  end

  describe "Dashboard" do
    test "renders dashboard with stats and activity", %{conn: conn, user: user, organization: org} do
      conn =
        conn
        |> log_in_user(user)
        |> put_session(:current_organization_id, org.id)

      {:ok, view, _html} = live(conn, ~p"/dashboard")

      assert has_element?(view, "h1", "Dashboard")
      assert has_element?(view, "h3", "Events")
      assert has_element?(view, "h3", "GitHub Activity")
    end

    test "displays repository events in chronological order with latest on top", %{
      conn: conn,
      user: user,
      organization: org,
      repos: [repo1, repo2]
    } do
      now = DateTime.utc_now()

      # Force clear any existing events to have a clean state
      Repo.delete_all(Event)

      # Create older event for repo2 (3rd)
      _older_event =
        %Event{}
        |> Event.changeset(%{
          type: "repobot.sync",
          payload: %{"commit_sha" => "abc1234", "file_ids" => [1, 2, 3]},
          status: "success",
          organization_id: org.id,
          user_id: user.id,
          repository_id: repo2.id,
          # 1 hour ago
          inserted_at: DateTime.add(now, -3600, :second)
        })
        |> Repo.insert!()

      # Create middle event for repo2 (2nd)
      _middle_event =
        %Event{}
        |> Event.changeset(%{
          type: "github.pull_request",
          payload: %{"action" => "opened", "number" => 5},
          status: "completed",
          organization_id: org.id,
          user_id: user.id,
          repository_id: repo2.id,
          # 30 minutes ago
          inserted_at: DateTime.add(now, -1800, :second)
        })
        |> Repo.insert!()

      # Create newest event for repo1 (1st/top)
      _newest_event =
        %Event{}
        |> Event.changeset(%{
          type: "github.push",
          payload: %{"commits" => [%{"message" => "Latest commit"}], "ref" => "refs/heads/main"},
          status: "completed",
          organization_id: org.id,
          user_id: user.id,
          repository_id: repo1.id,
          inserted_at: now
        })
        |> Repo.insert!()

      conn =
        conn
        |> log_in_user(user)
        |> put_session(:current_organization_id, org.id)

      {:ok, view, _html} = live(conn, ~p"/dashboard")

      assert view |> has_element?("[data-test-repo-status]")
    end

    test "handles dashboard render when there are no repositories", %{
      conn: conn,
      user: user,
      organization: org
    } do
      Repo.delete_all(Repobot.Repository)

      conn =
        conn
        |> log_in_user(user)
        |> put_session(:current_organization_id, org.id)

      {:ok, view, _html} = live(conn, ~p"/dashboard")

      assert has_element?(view, "h1", "Dashboard")
    end
  end
end
