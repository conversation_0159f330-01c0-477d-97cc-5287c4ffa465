defmodule RepobotWeb.Live.Repositories.IndexTest do
  use RepobotWeb.ConnCase, async: true

  import Mox
  import Phoenix.LiveViewTest

  use Repobot.Test.Fixtures

  setup :set_mox_from_context
  setup :verify_on_exit!

  describe "index" do
    @tag load_repos: "solnic"
    test "lists repositories in folders", %{conn: conn, user: user} do
      {:ok, view, html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/repositories")

      # Verify the page rendered successfully
      assert html =~ "Repositories"

      # Verify folders are displayed
      assert has_element?(view, "h2", "Forks")
      assert has_element?(view, "h2", "Elixir")

      # Verify repositories are displayed in correct folders
      assert has_element?(view, "div:has(h2) a", "solnic/drops")

      # Verify unorganized repositories section exists
      assert has_element?(view, "h2", "Unorganized Repositories")
    end

    @tag load_repos: "solnic"
    test "can search repositories", %{conn: conn, user: user} do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/repositories")

      # Search for a specific repository
      rendered =
        view
        |> form("#search-form", %{search: "drops"})
        |> render_change()

      # Verify only matching repositories are shown
      assert rendered =~ "solnic/drops"
      refute rendered =~ "solnic/rom-session"
    end

    @tag load_repos: "solnic"
    test "can create a new folder", %{conn: conn, user: user} do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/repositories")

      # Click the "New Folder" button
      assert render_click(view, "show_folder_form") =~ "Folder Name"

      # Fill in and submit the form
      rendered =
        view
        |> form("#folder-form", folder: %{name: "Test Folder"})
        |> render_submit()

      # Verify the new folder appears
      assert rendered =~ "Test Folder"
    end

    @tag load_repos: "solnic"
    test "can move repository to a folder", %{conn: conn, user: user} do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/repositories")

      # Find an unorganized repository
      repo =
        Repobot.Repositories.user_repositories(user)
        |> Enum.find(&(&1.folder_id == nil))

      # Create a new folder
      render_click(view, "show_folder_form")

      view
      |> form("#folder-form", folder: %{name: "Test Folder"})
      |> render_submit()

      folder =
        Repobot.Folders.list_folders(user, nil, user.default_organization_id)
        |> Enum.find(&(&1.name == "Test Folder"))

      rendered =
        view
        |> form("#move-repo-form-#{repo.id}", %{
          repository_id: repo.id,
          folder_id: folder.id
        })
        |> render_submit()

      assert rendered =~ repo.full_name

      moved_repo = Repo.reload(repo) |> Repo.preload(:folder)

      assert moved_repo.folder_id == folder.id
      assert moved_repo.organization_id == user.default_organization_id
    end

    @tag load_repos: "solnic"
    test "users in same organization can see each other's folders and repositories", %{
      conn: conn,
      user: user1
    } do
      # Create second user and add them to the first user's organization
      user2 = create_user(%{login: "other-user"})

      {:ok, _} =
        Repobot.Accounts.add_user_to_organization(user2, user1.default_organization, "member")

      # Create a folder and repository for the second user in the shared organization
      folder =
        create_folder(%{
          name: "Shared Folder",
          organization_id: user1.default_organization_id
        })

      repository =
        create_repository(%{
          owner: user2.login,
          name: "shared-repo",
          full_name: "#{user2.login}/shared-repo",
          organization_id: user1.default_organization_id,
          folder_id: folder.id
        })

      # Test that first user can see the folder and repository
      {:ok, _view, html1} =
        conn
        |> init_test_session(%{
          current_user_id: user1.id,
          current_organization_id: user1.default_organization_id
        })
        |> live(~p"/repositories")

      assert html1 =~ "Shared Folder"
      assert html1 =~ repository.full_name

      # Test that second user can see the folder and repository
      {:ok, _view, html2} =
        build_conn()
        |> init_test_session(%{
          current_user_id: user2.id,
          current_organization_id: user1.default_organization_id
        })
        |> live(~p"/repositories")

      assert html2 =~ "Shared Folder"
      assert html2 =~ repository.full_name
    end

    @tag load_repos: "solnic"
    test "can refresh repositories", %{conn: conn, user: user} do
      other_org = organization_fixture(%{name: "other-org"})

      Repobot.Test.GitHubMock
      |> expect(:client, 3, fn _ -> :test_client end)
      |> expect(:user_repos, 1, fn :test_client, "solnic" ->
        {:ok,
         [
           %{
             "name" => "drops",
             "owner" => %{"login" => "solnic"},
             "full_name" => "solnic/drops",
             "language" => "Elixir",
             "fork" => false,
             "private" => false
           }
         ]}
      end)
      |> expect(:list_repos, 2, fn :test_client, "other-org" ->
        {:ok,
         [
           %{
             "name" => "other-repo",
             "owner" => %{"login" => "other-org"},
             "full_name" => "other-org/other-repo",
             "language" => "Elixir",
             "fork" => false,
             "private" => false
           }
         ]}
      end)

      # Test with default organization
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/repositories")

      # Click the refresh button and verify loading state
      assert render_click(view, "refresh") =~ "Refreshing..."

      # Wait for the refresh to complete and verify it's done
      assert_eventually(fn ->
        rendered = render(view)
        assert rendered =~ "Refresh"
        assert rendered =~ "solnic/drops"
      end)

      # Test with other organization
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: other_org.id
        })
        |> assign(:current_user, Repo.preload(user, :default_organization))
        |> live(~p"/repositories")

      # Click the refresh button and verify loading state
      assert render_click(view, "refresh") =~ "Refreshing..."

      # Wait for the refresh to complete and verify it's done
      assert_eventually(fn ->
        rendered = render(view)
        assert rendered =~ "Refresh"
        assert rendered =~ "other-org/other-repo"
        refute rendered =~ "solnic/drops"
      end)

      # Verify repositories are in the correct organization
      repos = Repobot.Repositories.user_repositories(user, false, other_org.id)
      assert length(repos) == 1
      assert hd(repos).organization_id == other_org.id
      assert hd(repos).full_name == "other-org/other-repo"
    end

    @tag load_repos: "solnic"
    test "renders multiple template repositories in a folder", %{conn: conn, user: user} do
      folder =
        create_folder(%{name: "Templates Folder", organization_id: user.default_organization_id})

      template_repo1 =
        create_repository(%{
          owner: user.login,
          name: "template-repo-1",
          full_name: "#{user.login}/template-repo-1",
          organization_id: user.default_organization_id,
          template: true
        })

      template_repo2 =
        create_repository(%{
          owner: user.login,
          name: "template-repo-2",
          full_name: "#{user.login}/template-repo-2",
          organization_id: user.default_organization_id,
          template: true
        })

      {:ok, _} = Repobot.Repositories.add_template_folder(template_repo1, folder)
      {:ok, _} = Repobot.Repositories.add_template_folder(template_repo2, folder)

      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/repositories")

      # Verify the folder is displayed
      assert has_element?(view, "h2", folder.name)

      # Define a selector for the specific folder's container
      folder_selector = "div:has(h2)"

      # Verify both template repositories are rendered within the folder using data attributes
      assert has_element?(
               view,
               folder_selector <>
                 " div[data-repo='#{template_repo1.full_name}'][data-template-repo='true']"
             )

      assert has_element?(
               view,
               folder_selector <>
                 " div[data-repo='#{template_repo2.full_name}'][data-template-repo='true']"
             )

      # Optional: Verify the link text within the data-attribute selected element if needed
      assert has_element?(
               view,
               folder_selector <> " div[data-repo='#{template_repo1.full_name}'] a",
               template_repo1.full_name
             )

      assert has_element?(
               view,
               folder_selector <> " div[data-repo='#{template_repo2.full_name}'] a",
               template_repo2.full_name
             )
    end

    @tag load_repos: "solnic"
    test "can set a repository as template and maintain folder association", %{
      conn: conn,
      user: user
    } do
      # Create a folder
      folder =
        create_folder(%{name: "Test Folder", organization_id: user.default_organization_id})

      # Create a regular repository in the folder
      regular_repo =
        create_repository(%{
          owner: user.login,
          name: "regular-repo",
          full_name: "#{user.login}/regular-repo",
          organization_id: user.default_organization_id,
          folder_id: folder.id,
          template: false
        })

      # Load the page
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/repositories")

      # Verify the repository is in the folder
      assert has_element?(view, "h2", folder.name)

      assert has_element?(
               view,
               "div:has(h2) a",
               regular_repo.full_name
             )

      # Set the repository as template
      view
      |> element(
        "button[phx-click='set_as_template'][phx-value-repository_id='#{regular_repo.id}']"
      )
      |> render_click()

      # Verify the repository is still in the folder but now as a template
      assert has_element?(
               view,
               "div:has(h2) a",
               regular_repo.full_name
             )

      # Verify the repository is now a template
      updated_repo =
        Repobot.Repositories.get_repository!(regular_repo.id)
        |> Repobot.Repo.preload(:template_folders)

      assert updated_repo.template == true
      assert updated_repo.folder_id == nil

      assert Enum.any?(updated_repo.template_folders, fn template_folder ->
               template_folder.id == folder.id
             end)
    end

    @tag load_repos: "solnic"
    test "can disable template for a repository and maintain folder association", %{
      conn: conn,
      user: user
    } do
      # Create a folder
      folder =
        create_folder(%{name: "Test Folder", organization_id: user.default_organization_id})

      # Create a template repository
      template_repo =
        create_repository(%{
          owner: user.login,
          name: "template-repo",
          full_name: "#{user.login}/template-repo",
          organization_id: user.default_organization_id,
          template: true
        })

      # Add the template repository to the folder
      {:ok, _} = Repobot.Repositories.add_template_folder(template_repo, folder)

      # Load the page
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/repositories")

      # Verify the repository is in the folder as a template
      assert has_element?(view, "h2", folder.name)

      assert has_element?(
               view,
               "div:has(h2) a",
               template_repo.full_name
             )

      # Disable the template
      view
      |> element(
        "button[phx-click='disable_template'][phx-value-repository_id='#{template_repo.id}'][phx-value-folder_id='#{folder.id}']"
      )
      |> render_click()

      # Verify the repository is still in the folder but now as a regular repository
      assert has_element?(
               view,
               "div:has(h2) a",
               template_repo.full_name
             )

      # Verify the repository is no longer a template and has the folder_id set
      updated_repo = Repobot.Repositories.get_repository!(template_repo.id)
      assert updated_repo.template == false
      assert updated_repo.folder_id == folder.id
    end

    @tag load_repos: "solnic"
    test "correctly handles drag and drop for template repositories", %{
      conn: conn,
      user: user
    } do
      # Create two folders
      folder1 =
        create_folder(%{name: "Folder 1", organization_id: user.default_organization_id})

      folder2 =
        create_folder(%{name: "Folder 2", organization_id: user.default_organization_id})

      # Create a template repository
      template_repo =
        create_repository(%{
          owner: user.login,
          name: "template-repo",
          full_name: "#{user.login}/template-repo",
          organization_id: user.default_organization_id,
          template: true
        })

      # Add the template repository to folder1
      {:ok, _} = Repobot.Repositories.add_template_folder(template_repo, folder1)

      # Load the page
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/repositories")

      # Simulate drag and drop by directly calling the move_to_folder event
      # with the template_repo flag set to true
      view
      |> element("#repository-drag-drop")
      |> render_hook("move_to_folder", %{
        "repository_id" => template_repo.id,
        "folder_id" => folder2.id,
        "template_repo" => true
      })

      # Verify the repository is now in both folders
      updated_repo =
        Repobot.Repositories.get_repository!(template_repo.id)
        |> Repobot.Repo.preload(:template_folders)

      # The repository should still be a template
      assert updated_repo.template == true

      # The repository should be in both folders
      folder_ids = Enum.map(updated_repo.template_folders, & &1.id)
      assert Enum.member?(folder_ids, folder1.id)
      assert Enum.member?(folder_ids, folder2.id)
    end

    @tag load_repos: "solnic"
    test "can drag and drop template repository from template section to folder", %{
      conn: conn,
      user: user
    } do
      # Create a folder
      folder =
        create_folder(%{name: "Test Folder", organization_id: user.default_organization_id})

      # Create a template repository (not in any folder)
      template_repo =
        create_repository(%{
          owner: user.login,
          name: "template-repo",
          full_name: "#{user.login}/template-repo",
          organization_id: user.default_organization_id,
          template: true
        })

      # Load the page
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/repositories")

      # Simulate drag and drop by directly calling the move_to_folder event
      # with the template_repo flag set to true
      view
      |> element("#repository-drag-drop")
      |> render_hook("move_to_folder", %{
        "repository_id" => template_repo.id,
        "folder_id" => folder.id,
        "template_repo" => true
      })

      # Verify the repository is now in the folder
      updated_repo =
        Repobot.Repositories.get_repository!(template_repo.id)
        |> Repobot.Repo.preload(:template_folders)

      # The repository should still be a template
      assert updated_repo.template == true

      # The repository should be in the folder
      folder_ids = Enum.map(updated_repo.template_folders, & &1.id)
      assert Enum.member?(folder_ids, folder.id)
    end

    @tag load_repos: "solnic"
    test "removing template repository from folder unsyncs source files from target repositories",
         %{
           conn: conn,
           user: user
         } do
      # Create a folder
      folder =
        create_folder(%{name: "Test Folder", organization_id: user.default_organization_id})

      # Create target repositories in the folder
      target_repo1 =
        create_repository(%{
          owner: user.login,
          name: "target-repo-1",
          full_name: "#{user.login}/target-repo-1",
          folder_id: folder.id,
          organization_id: user.default_organization_id
        })

      target_repo2 =
        create_repository(%{
          owner: user.login,
          name: "target-repo-2",
          full_name: "#{user.login}/target-repo-2",
          folder_id: folder.id,
          organization_id: user.default_organization_id
        })

      # Create a template repository with source files
      template_repo =
        create_repository(%{
          owner: user.login,
          name: "template-repo",
          full_name: "#{user.login}/template-repo",
          organization_id: user.default_organization_id,
          template: true
        })

      _source_file =
        create_source_file(%{
          name: "README.md",
          content: "# Template README",
          target_path: "README.md",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Add the template repository to the folder (this should propagate source files)
      {:ok, _} = Repobot.Repositories.add_template_folder(template_repo, folder)

      # Verify source files are propagated to target repositories
      target_repo1_with_files = Repobot.Repositories.get_repository!(target_repo1.id)
      target_repo2_with_files = Repobot.Repositories.get_repository!(target_repo2.id)
      assert length(target_repo1_with_files.source_files) == 1
      assert length(target_repo2_with_files.source_files) == 1

      # Load the page
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/repositories")

      # Click the remove from folder button for the template repository
      view
      |> element(
        "button[phx-click='remove_from_folder'][phx-value-repository_id='#{template_repo.id}'][phx-value-folder_id='#{folder.id}']"
      )
      |> render_click()

      # Verify source files are unsynced from target repositories
      target_repo1_after_removal = Repobot.Repositories.get_repository!(target_repo1.id)
      target_repo2_after_removal = Repobot.Repositories.get_repository!(target_repo2.id)
      assert target_repo1_after_removal.source_files == []
      assert target_repo2_after_removal.source_files == []
    end
  end
end
