defmodule RepobotWeb.Live.Onboarding.Steps.TemplateRepositorySelectTest do
  use RepobotWeb.ConnCase, async: true
  use Repobot.Test.Fixtures

  import Phoenix.LiveViewTest

  describe "template repository select search functionality" do
    setup do
      user = create_user()

      # Create repositories for testing
      repo1 =
        create_repository(%{
          name: "rb-template",
          owner: user.login,
          full_name: "#{user.login}/rb-template",
          organization_id: user.default_organization_id
        })

      repo2 =
        create_repository(%{
          name: "my-project",
          owner: user.login,
          full_name: "#{user.login}/my-project",
          organization_id: user.default_organization_id
        })

      repo3 =
        create_repository(%{
          name: "another-repo",
          owner: user.login,
          full_name: "#{user.login}/another-repo",
          organization_id: user.default_organization_id
        })

      # Create a folder and assign one repo to it
      folder =
        create_folder(%{
          name: "Test Folder",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      {:ok, repo2} = Repobot.Repositories.update_repository(repo2, %{folder_id: folder.id})

      repositories = [repo1, repo2, repo3]

      {:ok,
       user: user,
       repositories: repositories,
       repo1: repo1,
       repo2: repo2,
       repo3: repo3,
       folder: folder}
    end

    test "search functionality works without crashing and filters repositories correctly", %{
      conn: conn,
      user: user
    } do
      # Navigate to onboarding and get to the template repository selection step
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Step 1: Welcome - click Next
      view |> element("button", "Next") |> render_click()

      # Step 2: Template Repository - select existing repositories
      view |> element("#select_existing") |> render_click()

      # Verify we can see the search input and all repositories initially
      assert has_element?(view, "input[name='template_search']")
      assert has_element?(view, "span", "#{user.login}/rb-template")
      assert has_element?(view, "span", "#{user.login}/my-project")
      assert has_element?(view, "span", "#{user.login}/another-repo")

      # Test search functionality - this should NOT crash anymore
      view
      |> element("input[name='template_search']")
      |> render_keyup(%{value: "rb-template"})

      # Verify search results are filtered correctly
      assert has_element?(view, "span", "#{user.login}/rb-template")
      refute has_element?(view, "span", "#{user.login}/my-project")
      refute has_element?(view, "span", "#{user.login}/another-repo")

      # Test clearing search
      view
      |> element("input[name='template_search']")
      |> render_keyup(%{value: ""})

      # Verify all repositories are shown again
      assert has_element?(view, "span", "#{user.login}/rb-template")
      assert has_element?(view, "span", "#{user.login}/my-project")
      assert has_element?(view, "span", "#{user.login}/another-repo")
    end

    test "search is case insensitive", %{
      conn: conn,
      user: user
    } do
      # Navigate to onboarding and get to the template repository selection step
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Step 1: Welcome - click Next
      view |> element("button", "Next") |> render_click()

      # Step 2: Template Repository - select existing repositories
      view |> element("#select_existing") |> render_click()

      # Search with different case
      view
      |> element("input[name='template_search']")
      |> render_keyup(%{value: "RB-TEMPLATE"})

      # Verify case insensitive search works
      assert has_element?(view, "span", "#{user.login}/rb-template")
      refute has_element?(view, "span", "#{user.login}/my-project")
      refute has_element?(view, "span", "#{user.login}/another-repo")
    end
  end
end
