defmodule RepobotWeb.Live.Onboarding.Steps.RepositorySyncTest do
  use RepobotWeb.ConnCase, async: true
  use Repobot.Test.Fixtures

  import Phoenix.LiveViewTest

  describe "repository sync filtering functionality" do
    setup do
      user = create_user()

      # Create a template repository
      template_repo =
        create_repository(%{
          name: "template-repo",
          owner: user.login,
          full_name: "#{user.login}/template-repo",
          template: true,
          organization_id: user.default_organization_id
        })

      # Create target repositories for testing filtering
      repo1 =
        create_repository(%{
          name: "my-awesome-project",
          owner: user.login,
          full_name: "#{user.login}/my-awesome-project",
          organization_id: user.default_organization_id
        })

      repo2 =
        create_repository(%{
          name: "another-project",
          owner: user.login,
          full_name: "#{user.login}/another-project",
          organization_id: user.default_organization_id
        })

      repo3 =
        create_repository(%{
          name: "test-utils",
          owner: user.login,
          full_name: "#{user.login}/test-utils",
          organization_id: user.default_organization_id
        })

      # Create a folder and assign some repos to it
      folder =
        create_folder(%{
          name: "Development",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      {:ok, repo1} = Repobot.Repositories.update_repository(repo1, %{folder_id: folder.id})
      {:ok, repo2} = Repobot.Repositories.update_repository(repo2, %{folder_id: folder.id})

      {:ok,
       user: user,
       template_repo: template_repo,
       repo1: repo1,
       repo2: repo2,
       repo3: repo3,
       folder: folder}
    end

    test "filter repositories by name works correctly", %{
      conn: conn,
      user: user,
      template_repo: template_repo
    } do
      # Navigate to onboarding and get to the repository sync step
      {:ok, view, _html} = navigate_to_repository_sync_step(conn, user, template_repo)

      # Verify all repositories are shown initially (excluding template repo)
      assert has_element?(view, "span", "#{user.login}/my-awesome-project")
      assert has_element?(view, "span", "#{user.login}/another-project")
      assert has_element?(view, "span", "#{user.login}/test-utils")

      # Filter by "project" - should show repos containing "project"
      view
      |> element("input[name='repository_filter']")
      |> render_keyup(%{value: "project"})

      # Verify filtered results
      assert has_element?(view, "span", "#{user.login}/my-awesome-project")
      assert has_element?(view, "span", "#{user.login}/another-project")
      refute has_element?(view, "span", "#{user.login}/test-utils")

      # Filter by "awesome" - should show only one repo
      view
      |> element("input[name='repository_filter']")
      |> render_keyup(%{value: "awesome"})

      assert has_element?(view, "span", "#{user.login}/my-awesome-project")
      refute has_element?(view, "span", "#{user.login}/another-project")
      refute has_element?(view, "span", "#{user.login}/test-utils")
    end

    test "clearing filter restores all repositories", %{
      conn: conn,
      user: user,
      template_repo: template_repo
    } do
      {:ok, view, _html} = navigate_to_repository_sync_step(conn, user, template_repo)

      # Apply a filter first
      view
      |> element("input[name='repository_filter']")
      |> render_keyup(%{value: "awesome"})

      # Verify only one repo is shown
      assert has_element?(view, "span", "#{user.login}/my-awesome-project")
      refute has_element?(view, "span", "#{user.login}/another-project")
      refute has_element?(view, "span", "#{user.login}/test-utils")

      # Clear the filter
      view
      |> element("input[name='repository_filter']")
      |> render_keyup(%{value: ""})

      # Verify all repositories are shown again
      assert has_element?(view, "span", "#{user.login}/my-awesome-project")
      assert has_element?(view, "span", "#{user.login}/another-project")
      assert has_element?(view, "span", "#{user.login}/test-utils")
    end

    test "filter is case insensitive", %{
      conn: conn,
      user: user,
      template_repo: template_repo
    } do
      {:ok, view, _html} = navigate_to_repository_sync_step(conn, user, template_repo)

      # Filter with uppercase
      view
      |> element("input[name='repository_filter']")
      |> render_keyup(%{value: "AWESOME"})

      # Should still find the repo
      assert has_element?(view, "span", "#{user.login}/my-awesome-project")
      refute has_element?(view, "span", "#{user.login}/another-project")
      refute has_element?(view, "span", "#{user.login}/test-utils")
    end

    test "filter works with owner/repo format", %{
      conn: conn,
      user: user,
      template_repo: template_repo
    } do
      {:ok, view, _html} = navigate_to_repository_sync_step(conn, user, template_repo)

      # Filter by owner name
      view
      |> element("input[name='repository_filter']")
      |> render_keyup(%{value: user.login})

      # Should show all repos since they all have the same owner
      assert has_element?(view, "span", "#{user.login}/my-awesome-project")
      assert has_element?(view, "span", "#{user.login}/another-project")
      assert has_element?(view, "span", "#{user.login}/test-utils")
    end

    test "filter handles whitespace correctly", %{
      conn: conn,
      user: user,
      template_repo: template_repo
    } do
      {:ok, view, _html} = navigate_to_repository_sync_step(conn, user, template_repo)

      # Filter with leading/trailing whitespace
      view
      |> element("input[name='repository_filter']")
      |> render_keyup(%{value: "  awesome  "})

      # Should still work correctly
      assert has_element?(view, "span", "#{user.login}/my-awesome-project")
      refute has_element?(view, "span", "#{user.login}/another-project")
      refute has_element?(view, "span", "#{user.login}/test-utils")
    end

    test "filter works for repositories in folders and unorganized", %{
      conn: conn,
      user: user,
      template_repo: template_repo,
      folder: folder
    } do
      {:ok, view, _html} = navigate_to_repository_sync_step(conn, user, template_repo)

      # Verify folder structure is shown
      assert has_element?(view, "h3", folder.name)

      # Filter should work across both organized and unorganized repos
      # Use "utils" instead of "test" to avoid matching the user login "test-user"
      view
      |> element("input[name='repository_filter']")
      |> render_keyup(%{value: "utils"})

      # Should show test-utils (unorganized)
      assert has_element?(view, "span", "#{user.login}/test-utils")
      refute has_element?(view, "span", "#{user.login}/my-awesome-project")
      refute has_element?(view, "span", "#{user.login}/another-project")
    end

    test "template repository is excluded from sync options", %{
      conn: conn,
      user: user,
      template_repo: template_repo
    } do
      {:ok, view, _html} = navigate_to_repository_sync_step(conn, user, template_repo)

      # Template repository should not appear in the sync options
      refute has_element?(view, "span", template_repo.full_name)

      # Even when filtering, template repo should not appear
      view
      |> element("input[name='repository_filter']")
      |> render_keyup(%{value: "template"})

      refute has_element?(view, "span", template_repo.full_name)
    end

    # Helper function to navigate to the repository sync step
    defp navigate_to_repository_sync_step(conn, user, template_repo) do
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Step 1: Welcome - click Next
      view |> element("button", "Next") |> render_click()

      # Step 2: Template Repository - select existing and choose template
      view |> element("#select_existing") |> render_click()
      view |> element("input[value='#{template_repo.id}']") |> render_click()
      view |> element("button", "Next") |> render_click()

      # Now we should be on Step 3: Repository Sync
      assert has_element?(view, "h2", "Repository Synchronization")
      assert has_element?(view, "input[name='repository_filter']")

      {:ok, view, render(view)}
    end
  end
end
