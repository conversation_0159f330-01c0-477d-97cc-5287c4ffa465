defmodule RepobotWeb.PageControllerTest do
  use RepobotWeb.ConnCase

  alias Repobot.Repo
  alias Repobot.Waitlist.Entry

  test "GET /", %{conn: conn} do
    conn = get(conn, ~p"/")
    assert html_response(conn, 200) =~ "RepoBot"
  end

  describe "POST /waitlist" do
    @valid_attrs %{"email" => "<EMAIL>", "github_username" => "testuser"}
    @invalid_attrs %{"email" => "", "github_username" => "testuser"}

    test "creates a waitlist entry with valid data", %{conn: conn} do
      conn =
        post(conn, ~p"/waitlist", %{
          "entry" => @valid_attrs,
          "confirm_email" => ""
        })

      assert redirected_to(conn) == ~p"/"
      assert Phoenix.Flash.get(conn.assigns.flash, :info) == "Thank you for joining the waitlist!"

      # Check if the entry was actually created
      assert Repo.get_by(Entry, email: @valid_attrs["email"])
    end

    test "does not create entry with invalid data", %{conn: conn} do
      params = %{
        "entry" => @invalid_attrs,
        "confirm_email" => ""
      }

      conn = post(conn, ~p"/waitlist", params)

      assert html_response(conn, 200) =~ "Could not join the waitlist"
      assert conn.private.phoenix_template == "home.html"
      # Ensure no entry was created
      refute Repo.get_by(Entry, github_username: @invalid_attrs["github_username"])
    end

    test "redirects immediately if honeypot field is filled", %{conn: conn} do
      params = %{
        "entry" => @valid_attrs,
        "confirm_email" => "honeypot_value"
      }

      conn = post(conn, ~p"/waitlist", params)

      assert redirected_to(conn) == ~p"/"
      assert Phoenix.Flash.get(conn.assigns.flash, :info) == "Thank you for joining the waitlist!"

      # Ensure no entry was created despite valid attrs
      refute Repo.get_by(Entry, email: @valid_attrs["email"])
    end
  end
end
