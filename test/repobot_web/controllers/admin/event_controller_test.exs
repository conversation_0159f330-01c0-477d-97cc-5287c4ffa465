defmodule RepobotWeb.Admin.EventControllerTest do
  use RepobotWeb.ConnCase
  use Repobot.Test.Fixtures

  setup %{conn: conn} do
    user = create_user()
    organization = Repo.get(Repobot.Accounts.Organization, user.default_organization_id)

    # Create some test events
    event1 =
      create_event(%{
        type: "github.push",
        payload: %{"test" => "data1"},
        organization_id: organization.id,
        user_id: user.id,
        status: "success"
      })

    event2 =
      create_event(%{
        type: "repobot.sync",
        payload: %{"test" => "data2"},
        organization_id: organization.id,
        user_id: user.id,
        status: "failed"
      })

    # Add BasicAuth credentials
    auth_config = Application.get_env(:repobot, :basic_auth_admin)

    conn =
      conn
      |> put_req_header(
        "authorization",
        "Basic " <> Base.encode64("#{auth_config[:username]}:#{auth_config[:password]}")
      )

    {:ok, conn: conn, user: user, organization: organization, events: [event1, event2]}
  end

  describe "index" do
    test "lists all events", %{conn: conn, events: [event1, event2]} do
      conn = get(conn, ~p"/admin/events")
      response = html_response(conn, 200)

      assert response =~ "Events"
      assert response =~ event1.type
      assert response =~ event2.type
    end

    test "filters events by type", %{conn: conn, events: [event1, _event2]} do
      conn = get(conn, ~p"/admin/events?type=#{event1.type}")
      response = html_response(conn, 200)

      # Check that the filtered event is in the response
      assert response =~ event1.type

      # Check that the event not matching the filter doesn't appear in the table
      refute response =~
               "<td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                repobot.sync\n              </td>"

      # Verify we're only showing one event in the count
      assert response =~ "Showing 1 events"
    end
  end

  describe "show" do
    test "shows event details", %{conn: conn, events: [event1, _event2]} do
      conn = get(conn, ~p"/admin/events/#{event1.id}")
      response = html_response(conn, 200)

      assert response =~ "Event Details"
      assert response =~ event1.type
      assert response =~ event1.id
      assert response =~ "success"
    end
  end

  # Helper to create an event directly
  defp create_event(attrs) do
    {:ok, event} =
      %Repobot.Events.Event{}
      |> Repobot.Events.Event.changeset(attrs)
      |> Repo.insert()

    event
  end
end
