defmodule RepobotWeb.Live.Folders.ShowReadOnlyTest do
  use RepobotWeb.ConnCase

  alias Repobot.{SourceFiles, Repository}
  import Repobot.Test.Fixtures

  describe "import_common_file with template repositories" do
    setup do
      user = create_user(%{login: "test_user", email: "<EMAIL>"})
      organization = user.default_organization

      # Create a folder
      folder = create_folder(%{
        name: "Test Folder",
        organization_id: organization.id,
        user_id: user.id
      })

      # Create a template repository
      template_repo = %Repository{
        id: Ecto.UUID.generate(),
        name: "template-repo",
        owner: "test-owner",
        full_name: "test-owner/template-repo",
        template: true,
        organization_id: organization.id,
        folder_id: folder.id
      }
      |> Repo.insert!()

      # Create a regular repository
      regular_repo = %Repository{
        id: Ecto.UUID.generate(),
        name: "regular-repo",
        owner: "test-owner",
        full_name: "test-owner/regular-repo",
        template: false,
        organization_id: organization.id,
        folder_id: folder.id
      }
      |> Repo.insert!()

      %{
        user: user,
        organization: organization,
        folder: folder,
        template_repo: template_repo,
        regular_repo: regular_repo
      }
    end

    test "source files imported from template repositories are marked as read-only", %{
      user: user,
      organization: organization,
      template_repo: template_repo,
      regular_repo: regular_repo
    } do
      # Simulate the attrs creation in Folders.Show for template repository
      template_attrs = %{
        name: "config.yml",
        content: "# Template config",
        target_path: "config.yml",
        organization_id: organization.id,
        source_repository_id: template_repo.id,
        read_only: template_repo.template,  # This is the key fix
        user_id: user.id
      }

      {:ok, template_source_file} = SourceFiles.create_source_file(template_attrs)
      assert template_source_file.read_only == true

      # Simulate the attrs creation in Folders.Show for regular repository
      regular_attrs = %{
        name: "regular.txt",
        content: "Regular content",
        target_path: "regular.txt",
        organization_id: organization.id,
        source_repository_id: regular_repo.id,
        read_only: regular_repo.template,  # This should be false
        user_id: user.id
      }

      {:ok, regular_source_file} = SourceFiles.create_source_file(regular_attrs)
      assert regular_source_file.read_only == false
    end
  end
end
