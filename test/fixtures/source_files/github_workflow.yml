name: CI

on:
  push:
    branches: [ {{ default_branch }} ]
  pull_request:
    branches: [ {{ default_branch }} ]

jobs:
  test:
    runs-on: ubuntu-latest
    name: Test on ${{ matrix.os }}

    env:
      REPO_NAME: {{ name }}
      REPO_URL: {{ html_url }}

    steps:
      - uses: actions/checkout@v3
      - name: Set up repository
        run: |
          echo "Setting up ${{ env.REPO_NAME }}"
          echo "Repository URL: ${{ env.REPO_URL }}"
