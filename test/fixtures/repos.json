[{"labels_url": "https://api.github.com/repos/solnic/alpinist/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/alpinist/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/alpinist/hooks", "id": 48807219, "teams_url": "https://api.github.com/repos/solnic/alpinist/teams", "full_name": "solnic/alpinist", "git_commits_url": "https://api.github.com/repos/solnic/alpinist/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/alpinist/downloads", "stargazers_url": "https://api.github.com/repos/solnic/alpinist/stargazers", "blobs_url": "https://api.github.com/repos/solnic/alpinist/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/alpinist/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk0ODgwNzIxOQ==", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/alpinist/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/alpinist/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/alpinist/git/trees{/sha}", "clone_url": "https://github.com/solnic/alpinist.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/alpinist/subscription", "url": "https://api.github.com/repos/solnic/alpinist", "statuses_url": "https://api.github.com/repos/solnic/alpinist/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/alpinist/milestones{/number}", "svn_url": "https://github.com/solnic/alpinist", "events_url": "https://api.github.com/repos/solnic/alpinist/events", "updated_at": "2019-07-10T06:13:38Z", "created_at": "2015-12-30T15:37:34Z", "html_url": "https://github.com/solnic/alpinist", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/alpinist/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/alpinist/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/alpinist/issues/events{/number}", "forks": 2, "merges_url": "https://api.github.com/repos/solnic/alpinist/merges", "deployments_url": "https://api.github.com/repos/solnic/alpinist/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/alpinist/assignees{/user}", "git_url": "git://github.com/solnic/alpinist.git", "forks_url": "https://api.github.com/repos/solnic/alpinist/forks", "tags_url": "https://api.github.com/repos/solnic/alpinist/tags", "open_issues": 0, "size": 74, "pushed_at": "2016-01-04T11:15:43Z", "issues_url": "https://api.github.com/repos/solnic/alpinist/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 2, "git_tags_url": "https://api.github.com/repos/solnic/alpinist/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/alpinist/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/alpinist/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/alpinist/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/alpinist/branches{/branch}", "description": "Estimate and invoice handling for client service agencies. Example Rodakase app.", "subscribers_url": "https://api.github.com/repos/solnic/alpinist/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "other", "name": "Other", "node_id": "MDc6TGljZW5zZTA=", "spdx_id": "NOASSERTION", "url": null}, "commits_url": "https://api.github.com/repos/solnic/alpinist/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/alpinist/git/refs{/sha}", "ssh_url": "**************:solnic/alpinist.git", "releases_url": "https://api.github.com/repos/solnic/alpinist/releases{/id}", "is_template": false, "name": "alpinist", "languages_url": "https://api.github.com/repos/solnic/alpinist/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/alpinist/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/hooks", "id": 126345737, "teams_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/teams", "full_name": "solnic/architecture-talk-example-app", "git_commits_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/downloads", "stargazers_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/stargazers", "blobs_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxMjYzNDU3Mzc=", "watchers_count": 7, "notifications_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/git/trees{/sha}", "clone_url": "https://github.com/solnic/architecture-talk-example-app.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/subscription", "url": "https://api.github.com/repos/solnic/architecture-talk-example-app", "statuses_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/milestones{/number}", "svn_url": "https://github.com/solnic/architecture-talk-example-app", "events_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/events", "updated_at": "2021-01-07T14:41:58Z", "created_at": "2018-03-22T14:14:12Z", "html_url": "https://github.com/solnic/architecture-talk-example-app", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/merges", "deployments_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/assignees{/user}", "git_url": "git://github.com/solnic/architecture-talk-example-app.git", "forks_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/forks", "tags_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/tags", "open_issues": 0, "size": 7, "pushed_at": "2018-03-22T16:52:44Z", "issues_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/branches{/branch}", "description": null, "subscribers_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/subscribers", "stargazers_count": 7, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/git/refs{/sha}", "ssh_url": "**************:solnic/architecture-talk-example-app.git", "releases_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/releases{/id}", "is_template": false, "name": "architecture-talk-example-app", "languages_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/architecture-talk-example-app/contents/{+path}", "watchers": 7}, {"labels_url": "https://api.github.com/repos/solnic/axiom/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/axiom/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/axiom/hooks", "id": 19174245, "teams_url": "https://api.github.com/repos/solnic/axiom/teams", "full_name": "solnic/axiom", "git_commits_url": "https://api.github.com/repos/solnic/axiom/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/axiom/downloads", "stargazers_url": "https://api.github.com/repos/solnic/axiom/stargazers", "blobs_url": "https://api.github.com/repos/solnic/axiom/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/axiom/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxOTE3NDI0NQ==", "watchers_count": 5, "notifications_url": "https://api.github.com/repos/solnic/axiom/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/axiom/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/axiom/git/trees{/sha}", "clone_url": "https://github.com/solnic/axiom.git", "has_downloads": false, "subscription_url": "https://api.github.com/repos/solnic/axiom/subscription", "url": "https://api.github.com/repos/solnic/axiom", "statuses_url": "https://api.github.com/repos/solnic/axiom/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/axiom/milestones{/number}", "svn_url": "https://github.com/solnic/axiom", "events_url": "https://api.github.com/repos/solnic/axiom/events", "updated_at": "2019-08-13T15:40:29Z", "created_at": "2014-04-26T09:51:30Z", "html_url": "https://github.com/solnic/axiom", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/axiom/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/axiom/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/axiom/issues/events{/number}", "forks": 2, "merges_url": "https://api.github.com/repos/solnic/axiom/merges", "deployments_url": "https://api.github.com/repos/solnic/axiom/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/axiom/assignees{/user}", "git_url": "git://github.com/solnic/axiom.git", "forks_url": "https://api.github.com/repos/solnic/axiom/forks", "tags_url": "https://api.github.com/repos/solnic/axiom/tags", "open_issues": 0, "size": 6346, "pushed_at": "2014-05-03T19:38:06Z", "issues_url": "https://api.github.com/repos/solnic/axiom/issues{/number}", "homepage": "https://github.com/dkubb/axiom", "private": false, "disabled": false, "forks_count": 2, "git_tags_url": "https://api.github.com/repos/solnic/axiom/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/axiom/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/axiom/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/axiom/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/axiom/branches{/branch}", "description": "This is an experimental fork of Axiom", "subscribers_url": "https://api.github.com/repos/solnic/axiom/subscribers", "stargazers_count": 5, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/axiom/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/axiom/git/refs{/sha}", "ssh_url": "**************:solnic/axiom.git", "releases_url": "https://api.github.com/repos/solnic/axiom/releases{/id}", "is_template": false, "name": "axiom", "languages_url": "https://api.github.com/repos/solnic/axiom/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/axiom/contents/{+path}", "watchers": 5}, {"labels_url": "https://api.github.com/repos/solnic/call_sheet/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/call_sheet/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/call_sheet/hooks", "id": 45738323, "teams_url": "https://api.github.com/repos/solnic/call_sheet/teams", "full_name": "solnic/call_sheet", "git_commits_url": "https://api.github.com/repos/solnic/call_sheet/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/call_sheet/downloads", "stargazers_url": "https://api.github.com/repos/solnic/call_sheet/stargazers", "blobs_url": "https://api.github.com/repos/solnic/call_sheet/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/call_sheet/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk0NTczODMyMw==", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/call_sheet/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/call_sheet/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/call_sheet/git/trees{/sha}", "clone_url": "https://github.com/solnic/call_sheet.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/call_sheet/subscription", "url": "https://api.github.com/repos/solnic/call_sheet", "statuses_url": "https://api.github.com/repos/solnic/call_sheet/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/call_sheet/milestones{/number}", "svn_url": "https://github.com/solnic/call_sheet", "events_url": "https://api.github.com/repos/solnic/call_sheet/events", "updated_at": "2016-03-21T02:09:32Z", "created_at": "2015-11-07T13:52:06Z", "html_url": "https://github.com/solnic/call_sheet", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/call_sheet/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/call_sheet/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/call_sheet/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/call_sheet/merges", "deployments_url": "https://api.github.com/repos/solnic/call_sheet/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/call_sheet/assignees{/user}", "git_url": "git://github.com/solnic/call_sheet.git", "forks_url": "https://api.github.com/repos/solnic/call_sheet/forks", "tags_url": "https://api.github.com/repos/solnic/call_sheet/tags", "open_issues": 0, "size": 81, "pushed_at": "2015-11-02T22:15:42Z", "issues_url": "https://api.github.com/repos/solnic/call_sheet/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/call_sheet/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/call_sheet/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/call_sheet/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/call_sheet/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/call_sheet/branches{/branch}", "description": "Business transaction DSL", "subscribers_url": "https://api.github.com/repos/solnic/call_sheet/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/call_sheet/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/call_sheet/git/refs{/sha}", "ssh_url": "**************:solnic/call_sheet.git", "releases_url": "https://api.github.com/repos/solnic/call_sheet/releases{/id}", "is_template": false, "name": "call_sheet", "languages_url": "https://api.github.com/repos/solnic/call_sheet/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/call_sheet/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/charlatan/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/charlatan/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/charlatan/hooks", "id": 12665605, "teams_url": "https://api.github.com/repos/solnic/charlatan/teams", "full_name": "solnic/charlatan", "git_commits_url": "https://api.github.com/repos/solnic/charlatan/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/charlatan/downloads", "stargazers_url": "https://api.github.com/repos/solnic/charlatan/stargazers", "blobs_url": "https://api.github.com/repos/solnic/charlatan/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/charlatan/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxMjY2NTYwNQ==", "watchers_count": 69, "notifications_url": "https://api.github.com/repos/solnic/charlatan/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/charlatan/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/charlatan/git/trees{/sha}", "clone_url": "https://github.com/solnic/charlatan.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/charlatan/subscription", "url": "https://api.github.com/repos/solnic/charlatan", "statuses_url": "https://api.github.com/repos/solnic/charlatan/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/charlatan/milestones{/number}", "svn_url": "https://github.com/solnic/charlatan", "events_url": "https://api.github.com/repos/solnic/charlatan/events", "updated_at": "2023-12-07T23:23:32Z", "created_at": "2013-09-07T13:54:53Z", "html_url": "https://github.com/solnic/charlatan", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/charlatan/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/charlatan/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/charlatan/issues/events{/number}", "forks": 3, "merges_url": "https://api.github.com/repos/solnic/charlatan/merges", "deployments_url": "https://api.github.com/repos/solnic/charlatan/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/charlatan/assignees{/user}", "git_url": "git://github.com/solnic/charlatan.git", "forks_url": "https://api.github.com/repos/solnic/charlatan/forks", "tags_url": "https://api.github.com/repos/solnic/charlatan/tags", "open_issues": 3, "size": 269, "pushed_at": "2014-12-15T11:14:01Z", "issues_url": "https://api.github.com/repos/solnic/charlatan/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 3, "git_tags_url": "https://api.github.com/repos/solnic/charlatan/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/charlatan/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/charlatan/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/charlatan/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/charlatan/branches{/branch}", "description": "Neat delegation for ruby objects", "subscribers_url": "https://api.github.com/repos/solnic/charlatan/subscribers", "stargazers_count": 69, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/charlatan/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/charlatan/git/refs{/sha}", "ssh_url": "**************:solnic/charlatan.git", "releases_url": "https://api.github.com/repos/solnic/charlatan/releases{/id}", "is_template": false, "name": "charlatan", "languages_url": "https://api.github.com/repos/solnic/charlatan/languages", "open_issues_count": 3, "contents_url": "https://api.github.com/repos/solnic/charlatan/contents/{+path}", "watchers": 69}, {"labels_url": "https://api.github.com/repos/solnic/coercible/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/coercible/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/coercible/hooks", "id": 7284309, "teams_url": "https://api.github.com/repos/solnic/coercible/teams", "full_name": "solnic/coercible", "git_commits_url": "https://api.github.com/repos/solnic/coercible/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/coercible/downloads", "stargazers_url": "https://api.github.com/repos/solnic/coercible/stargazers", "blobs_url": "https://api.github.com/repos/solnic/coercible/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/coercible/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk3Mjg0MzA5", "watchers_count": 137, "notifications_url": "https://api.github.com/repos/solnic/coercible/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/coercible/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/coercible/git/trees{/sha}", "clone_url": "https://github.com/solnic/coercible.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/coercible/subscription", "url": "https://api.github.com/repos/solnic/coercible", "statuses_url": "https://api.github.com/repos/solnic/coercible/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/coercible/milestones{/number}", "svn_url": "https://github.com/solnic/coercible", "events_url": "https://api.github.com/repos/solnic/coercible/events", "updated_at": "2024-03-26T02:36:47Z", "created_at": "2012-12-22T09:18:46Z", "html_url": "https://github.com/solnic/coercible", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/coercible/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/coercible/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/coercible/issues/events{/number}", "forks": 16, "merges_url": "https://api.github.com/repos/solnic/coercible/merges", "deployments_url": "https://api.github.com/repos/solnic/coercible/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/coercible/assignees{/user}", "git_url": "git://github.com/solnic/coercible.git", "forks_url": "https://api.github.com/repos/solnic/coercible/forks", "tags_url": "https://api.github.com/repos/solnic/coercible/tags", "open_issues": 13, "size": 394, "pushed_at": "2017-09-01T21:14:40Z", "issues_url": "https://api.github.com/repos/solnic/coercible/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 16, "git_tags_url": "https://api.github.com/repos/solnic/coercible/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/coercible/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/coercible/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/coercible/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/coercible/branches{/branch}", "description": "Powerful, flexible and configurable coercion library. And nothing more.", "subscribers_url": "https://api.github.com/repos/solnic/coercible/subscribers", "stargazers_count": 137, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/coercible/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/coercible/git/refs{/sha}", "ssh_url": "**************:solnic/coercible.git", "releases_url": "https://api.github.com/repos/solnic/coercible/releases{/id}", "is_template": false, "name": "coercible", "languages_url": "https://api.github.com/repos/solnic/coercible/languages", "open_issues_count": 13, "contents_url": "https://api.github.com/repos/solnic/coercible/contents/{+path}", "watchers": 137}, {"labels_url": "https://api.github.com/repos/solnic/csa-a-2018/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/csa-a-2018/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/csa-a-2018/hooks", "id": 153431466, "teams_url": "https://api.github.com/repos/solnic/csa-a-2018/teams", "full_name": "solnic/csa-a-2018", "git_commits_url": "https://api.github.com/repos/solnic/csa-a-2018/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/csa-a-2018/downloads", "stargazers_url": "https://api.github.com/repos/solnic/csa-a-2018/stargazers", "blobs_url": "https://api.github.com/repos/solnic/csa-a-2018/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/csa-a-2018/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxNTM0MzE0NjY=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/csa-a-2018/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/csa-a-2018/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/csa-a-2018/git/trees{/sha}", "clone_url": "https://github.com/solnic/csa-a-2018.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/csa-a-2018/subscription", "url": "https://api.github.com/repos/solnic/csa-a-2018", "statuses_url": "https://api.github.com/repos/solnic/csa-a-2018/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/csa-a-2018/milestones{/number}", "svn_url": "https://github.com/solnic/csa-a-2018", "events_url": "https://api.github.com/repos/solnic/csa-a-2018/events", "updated_at": "2018-10-17T09:33:34Z", "created_at": "2018-10-17T09:33:32Z", "html_url": "https://github.com/solnic/csa-a-2018", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/csa-a-2018/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "HTML", "contributors_url": "https://api.github.com/repos/solnic/csa-a-2018/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/csa-a-2018/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/csa-a-2018/merges", "deployments_url": "https://api.github.com/repos/solnic/csa-a-2018/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/csa-a-2018/assignees{/user}", "git_url": "git://github.com/solnic/csa-a-2018.git", "forks_url": "https://api.github.com/repos/solnic/csa-a-2018/forks", "tags_url": "https://api.github.com/repos/solnic/csa-a-2018/tags", "open_issues": 0, "size": 22571, "pushed_at": "2018-07-02T22:29:50Z", "issues_url": "https://api.github.com/repos/solnic/csa-a-2018/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/csa-a-2018/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/csa-a-2018/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/csa-a-2018/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/csa-a-2018/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/csa-a-2018/branches{/branch}", "description": null, "subscribers_url": "https://api.github.com/repos/solnic/csa-a-2018/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/csa-a-2018/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/csa-a-2018/git/refs{/sha}", "ssh_url": "**************:solnic/csa-a-2018.git", "releases_url": "https://api.github.com/repos/solnic/csa-a-2018/releases{/id}", "is_template": false, "name": "csa-a-2018", "languages_url": "https://api.github.com/repos/solnic/csa-a-2018/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/csa-a-2018/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/devtools/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/devtools/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/devtools/hooks", "id": 240799473, "teams_url": "https://api.github.com/repos/solnic/devtools/teams", "full_name": "solnic/devtools", "git_commits_url": "https://api.github.com/repos/solnic/devtools/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/devtools/downloads", "stargazers_url": "https://api.github.com/repos/solnic/devtools/stargazers", "blobs_url": "https://api.github.com/repos/solnic/devtools/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/devtools/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkyNDA3OTk0NzM=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/devtools/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/devtools/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/devtools/git/trees{/sha}", "clone_url": "https://github.com/solnic/devtools.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/devtools/subscription", "url": "https://api.github.com/repos/solnic/devtools", "statuses_url": "https://api.github.com/repos/solnic/devtools/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/devtools/milestones{/number}", "svn_url": "https://github.com/solnic/devtools", "events_url": "https://api.github.com/repos/solnic/devtools/events", "updated_at": "2020-02-15T22:55:37Z", "created_at": "2020-02-15T22:55:35Z", "html_url": "https://github.com/solnic/devtools", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/devtools/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": null, "contributors_url": "https://api.github.com/repos/solnic/devtools/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/devtools/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/devtools/merges", "deployments_url": "https://api.github.com/repos/solnic/devtools/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/devtools/assignees{/user}", "git_url": "git://github.com/solnic/devtools.git", "forks_url": "https://api.github.com/repos/solnic/devtools/forks", "tags_url": "https://api.github.com/repos/solnic/devtools/tags", "open_issues": 0, "size": 253, "pushed_at": "2020-02-14T09:47:25Z", "issues_url": "https://api.github.com/repos/solnic/devtools/issues{/number}", "homepage": "https://dry-rb.org", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/devtools/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/devtools/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/devtools/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/devtools/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/devtools/branches{/branch}", "description": "Shared tools, configuration and maintenance automation for dry-rb repos", "subscribers_url": "https://api.github.com/repos/solnic/devtools/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/devtools/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/devtools/git/refs{/sha}", "ssh_url": "**************:solnic/devtools.git", "releases_url": "https://api.github.com/repos/solnic/devtools/releases{/id}", "is_template": false, "name": "devtools", "languages_url": "https://api.github.com/repos/solnic/devtools/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/devtools/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/hooks", "id": 220816, "teams_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/teams", "full_name": "solnic/dm-gdata-adapters", "git_commits_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/downloads", "stargazers_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/stargazers", "blobs_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkyMjA4MTY=", "watchers_count": 1, "notifications_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/git/trees{/sha}", "clone_url": "https://github.com/solnic/dm-gdata-adapters.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/subscription", "url": "https://api.github.com/repos/solnic/dm-gdata-adapters", "statuses_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/milestones{/number}", "svn_url": "https://github.com/solnic/dm-gdata-adapters", "events_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/events", "updated_at": "2013-10-07T04:42:03Z", "created_at": "2009-06-07T11:45:34Z", "html_url": "https://github.com/solnic/dm-gdata-adapters", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/merges", "deployments_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/assignees{/user}", "git_url": "git://github.com/solnic/dm-gdata-adapters.git", "forks_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/forks", "tags_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/tags", "open_issues": 0, "size": 76, "pushed_at": "2009-06-07T11:53:10Z", "issues_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/branches{/branch}", "description": "A set of DataMapper adapters for Google Data API", "subscribers_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/subscribers", "stargazers_count": 1, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/git/refs{/sha}", "ssh_url": "**************:solnic/dm-gdata-adapters.git", "releases_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/releases{/id}", "is_template": false, "name": "dm-gdata-adapters", "languages_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/dm-gdata-adapters/contents/{+path}", "watchers": 1}, {"labels_url": "https://api.github.com/repos/solnic/dm-is-configurable/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/dm-is-configurable/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/dm-is-configurable/hooks", "id": 68993, "teams_url": "https://api.github.com/repos/solnic/dm-is-configurable/teams", "full_name": "solnic/dm-is-configurable", "git_commits_url": "https://api.github.com/repos/solnic/dm-is-configurable/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/dm-is-configurable/downloads", "stargazers_url": "https://api.github.com/repos/solnic/dm-is-configurable/stargazers", "blobs_url": "https://api.github.com/repos/solnic/dm-is-configurable/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/dm-is-configurable/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk2ODk5Mw==", "watchers_count": 2, "notifications_url": "https://api.github.com/repos/solnic/dm-is-configurable/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/dm-is-configurable/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/dm-is-configurable/git/trees{/sha}", "clone_url": "https://github.com/solnic/dm-is-configurable.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/dm-is-configurable/subscription", "url": "https://api.github.com/repos/solnic/dm-is-configurable", "statuses_url": "https://api.github.com/repos/solnic/dm-is-configurable/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/dm-is-configurable/milestones{/number}", "svn_url": "https://github.com/solnic/dm-is-configurable", "events_url": "https://api.github.com/repos/solnic/dm-is-configurable/events", "updated_at": "2019-08-13T13:39:58Z", "created_at": "2008-10-28T21:25:22Z", "html_url": "https://github.com/solnic/dm-is-configurable", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/dm-is-configurable/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/dm-is-configurable/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/dm-is-configurable/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/dm-is-configurable/merges", "deployments_url": "https://api.github.com/repos/solnic/dm-is-configurable/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/dm-is-configurable/assignees{/user}", "git_url": "git://github.com/solnic/dm-is-configurable.git", "forks_url": "https://api.github.com/repos/solnic/dm-is-configurable/forks", "tags_url": "https://api.github.com/repos/solnic/dm-is-configurable/tags", "open_issues": 0, "size": 96, "pushed_at": "2009-11-28T14:23:35Z", "issues_url": "https://api.github.com/repos/solnic/dm-is-configurable/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/dm-is-configurable/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/dm-is-configurable/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/dm-is-configurable/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/dm-is-configurable/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/dm-is-configurable/branches{/branch}", "description": "A DataMapper plugin which adds configuration to resources", "subscribers_url": "https://api.github.com/repos/solnic/dm-is-configurable/subscribers", "stargazers_count": 2, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/dm-is-configurable/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/dm-is-configurable/git/refs{/sha}", "ssh_url": "**************:solnic/dm-is-configurable.git", "releases_url": "https://api.github.com/repos/solnic/dm-is-configurable/releases{/id}", "is_template": false, "name": "dm-is-configurable", "languages_url": "https://api.github.com/repos/solnic/dm-is-configurable/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/dm-is-configurable/contents/{+path}", "watchers": 2}, {"labels_url": "https://api.github.com/repos/solnic/dm-is-formattable/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/dm-is-formattable/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/dm-is-formattable/hooks", "id": 69195, "teams_url": "https://api.github.com/repos/solnic/dm-is-formattable/teams", "full_name": "solnic/dm-is-formattable", "git_commits_url": "https://api.github.com/repos/solnic/dm-is-formattable/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/dm-is-formattable/downloads", "stargazers_url": "https://api.github.com/repos/solnic/dm-is-formattable/stargazers", "blobs_url": "https://api.github.com/repos/solnic/dm-is-formattable/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/dm-is-formattable/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk2OTE5NQ==", "watchers_count": 3, "notifications_url": "https://api.github.com/repos/solnic/dm-is-formattable/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/dm-is-formattable/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/dm-is-formattable/git/trees{/sha}", "clone_url": "https://github.com/solnic/dm-is-formattable.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/dm-is-formattable/subscription", "url": "https://api.github.com/repos/solnic/dm-is-formattable", "statuses_url": "https://api.github.com/repos/solnic/dm-is-formattable/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/dm-is-formattable/milestones{/number}", "svn_url": "https://github.com/solnic/dm-is-formattable", "events_url": "https://api.github.com/repos/solnic/dm-is-formattable/events", "updated_at": "2021-10-31T10:17:11Z", "created_at": "2008-10-29T10:11:27Z", "html_url": "https://github.com/solnic/dm-is-formattable", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/dm-is-formattable/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/dm-is-formattable/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/dm-is-formattable/issues/events{/number}", "forks": 2, "merges_url": "https://api.github.com/repos/solnic/dm-is-formattable/merges", "deployments_url": "https://api.github.com/repos/solnic/dm-is-formattable/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/dm-is-formattable/assignees{/user}", "git_url": "git://github.com/solnic/dm-is-formattable.git", "forks_url": "https://api.github.com/repos/solnic/dm-is-formattable/forks", "tags_url": "https://api.github.com/repos/solnic/dm-is-formattable/tags", "open_issues": 0, "size": 91, "pushed_at": "2008-11-08T12:44:53Z", "issues_url": "https://api.github.com/repos/solnic/dm-is-formattable/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 2, "git_tags_url": "https://api.github.com/repos/solnic/dm-is-formattable/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/dm-is-formattable/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/dm-is-formattable/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/dm-is-formattable/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/dm-is-formattable/branches{/branch}", "description": null, "subscribers_url": "https://api.github.com/repos/solnic/dm-is-formattable/subscribers", "stargazers_count": 3, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/dm-is-formattable/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/dm-is-formattable/git/refs{/sha}", "ssh_url": "**************:solnic/dm-is-formattable.git", "releases_url": "https://api.github.com/repos/solnic/dm-is-formattable/releases{/id}", "is_template": false, "name": "dm-is-formattable", "languages_url": "https://api.github.com/repos/solnic/dm-is-formattable/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/dm-is-formattable/contents/{+path}", "watchers": 3}, {"labels_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/hooks", "id": 287903, "teams_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/teams", "full_name": "solnic/dm-mongo-adapter", "git_commits_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/downloads", "stargazers_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/stargazers", "blobs_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkyODc5MDM=", "watchers_count": 73, "notifications_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/git/trees{/sha}", "clone_url": "https://github.com/solnic/dm-mongo-adapter.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/subscription", "url": "https://api.github.com/repos/solnic/dm-mongo-adapter", "statuses_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/milestones{/number}", "svn_url": "https://github.com/solnic/dm-mongo-adapter", "events_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/events", "updated_at": "2023-10-12T11:23:13Z", "created_at": "2009-08-25T18:33:45Z", "html_url": "https://github.com/solnic/dm-mongo-adapter", "archived": true, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/issues/events{/number}", "forks": 13, "merges_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/merges", "deployments_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/assignees{/user}", "git_url": "git://github.com/solnic/dm-mongo-adapter.git", "forks_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/forks", "tags_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/tags", "open_issues": 1, "size": 602, "pushed_at": "2012-02-25T22:06:54Z", "issues_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/issues{/number}", "homepage": "https://github.com/solnic/dm-mongo-adapter", "private": false, "disabled": false, "forks_count": 13, "git_tags_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/branches{/branch}", "description": "MongoDB DataMapper Adapter", "subscribers_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/subscribers", "stargazers_count": 73, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/git/refs{/sha}", "ssh_url": "**************:solnic/dm-mongo-adapter.git", "releases_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/releases{/id}", "is_template": false, "name": "dm-mongo-adapter", "languages_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/languages", "open_issues_count": 1, "contents_url": "https://api.github.com/repos/solnic/dm-mongo-adapter/contents/{+path}", "watchers": 73}, {"labels_url": "https://api.github.com/repos/solnic/dm-timezones/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/dm-timezones/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/dm-timezones/hooks", "id": 715753, "teams_url": "https://api.github.com/repos/solnic/dm-timezones/teams", "full_name": "solnic/dm-timezones", "git_commits_url": "https://api.github.com/repos/solnic/dm-timezones/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/dm-timezones/downloads", "stargazers_url": "https://api.github.com/repos/solnic/dm-timezones/stargazers", "blobs_url": "https://api.github.com/repos/solnic/dm-timezones/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/dm-timezones/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk3MTU3NTM=", "watchers_count": 5, "notifications_url": "https://api.github.com/repos/solnic/dm-timezones/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/dm-timezones/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/dm-timezones/git/trees{/sha}", "clone_url": "https://github.com/solnic/dm-timezones.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/dm-timezones/subscription", "url": "https://api.github.com/repos/solnic/dm-timezones", "statuses_url": "https://api.github.com/repos/solnic/dm-timezones/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/dm-timezones/milestones{/number}", "svn_url": "https://github.com/solnic/dm-timezones", "events_url": "https://api.github.com/repos/solnic/dm-timezones/events", "updated_at": "2017-06-05T06:29:39Z", "created_at": "2010-06-11T14:14:50Z", "html_url": "https://github.com/solnic/dm-timezones", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/dm-timezones/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": null, "contributors_url": "https://api.github.com/repos/solnic/dm-timezones/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/dm-timezones/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/dm-timezones/merges", "deployments_url": "https://api.github.com/repos/solnic/dm-timezones/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/dm-timezones/assignees{/user}", "git_url": "git://github.com/solnic/dm-timezones.git", "forks_url": "https://api.github.com/repos/solnic/dm-timezones/forks", "tags_url": "https://api.github.com/repos/solnic/dm-timezones/tags", "open_issues": 0, "size": 100, "pushed_at": "2010-06-11T15:22:13Z", "issues_url": "https://api.github.com/repos/solnic/dm-timezones/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/dm-timezones/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/dm-timezones/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/dm-timezones/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/dm-timezones/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/dm-timezones/branches{/branch}", "description": "Timezones support for DataMapper", "subscribers_url": "https://api.github.com/repos/solnic/dm-timezones/subscribers", "stargazers_count": 5, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/dm-timezones/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/dm-timezones/git/refs{/sha}", "ssh_url": "**************:solnic/dm-timezones.git", "releases_url": "https://api.github.com/repos/solnic/dm-timezones/releases{/id}", "is_template": false, "name": "dm-timezones", "languages_url": "https://api.github.com/repos/solnic/dm-timezones/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/dm-timezones/contents/{+path}", "watchers": 5}, {"labels_url": "https://api.github.com/repos/solnic/dm-validations-ext/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/dm-validations-ext/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/dm-validations-ext/hooks", "id": 899034, "teams_url": "https://api.github.com/repos/solnic/dm-validations-ext/teams", "full_name": "solnic/dm-validations-ext", "git_commits_url": "https://api.github.com/repos/solnic/dm-validations-ext/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/dm-validations-ext/downloads", "stargazers_url": "https://api.github.com/repos/solnic/dm-validations-ext/stargazers", "blobs_url": "https://api.github.com/repos/solnic/dm-validations-ext/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/dm-validations-ext/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk4OTkwMzQ=", "watchers_count": 11, "notifications_url": "https://api.github.com/repos/solnic/dm-validations-ext/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/dm-validations-ext/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/dm-validations-ext/git/trees{/sha}", "clone_url": "https://github.com/solnic/dm-validations-ext.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/dm-validations-ext/subscription", "url": "https://api.github.com/repos/solnic/dm-validations-ext", "statuses_url": "https://api.github.com/repos/solnic/dm-validations-ext/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/dm-validations-ext/milestones{/number}", "svn_url": "https://github.com/solnic/dm-validations-ext", "events_url": "https://api.github.com/repos/solnic/dm-validations-ext/events", "updated_at": "2023-10-12T09:50:40Z", "created_at": "2010-09-09T16:30:16Z", "html_url": "https://github.com/solnic/dm-validations-ext", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/dm-validations-ext/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/dm-validations-ext/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/dm-validations-ext/issues/events{/number}", "forks": 5, "merges_url": "https://api.github.com/repos/solnic/dm-validations-ext/merges", "deployments_url": "https://api.github.com/repos/solnic/dm-validations-ext/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/dm-validations-ext/assignees{/user}", "git_url": "git://github.com/solnic/dm-validations-ext.git", "forks_url": "https://api.github.com/repos/solnic/dm-validations-ext/forks", "tags_url": "https://api.github.com/repos/solnic/dm-validations-ext/tags", "open_issues": 1, "size": 103, "pushed_at": "2011-03-19T12:48:25Z", "issues_url": "https://api.github.com/repos/solnic/dm-validations-ext/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 5, "git_tags_url": "https://api.github.com/repos/solnic/dm-validations-ext/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/dm-validations-ext/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/dm-validations-ext/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/dm-validations-ext/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/dm-validations-ext/branches{/branch}", "description": "Various additions to the dm-validations API", "subscribers_url": "https://api.github.com/repos/solnic/dm-validations-ext/subscribers", "stargazers_count": 11, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/dm-validations-ext/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/dm-validations-ext/git/refs{/sha}", "ssh_url": "**************:solnic/dm-validations-ext.git", "releases_url": "https://api.github.com/repos/solnic/dm-validations-ext/releases{/id}", "is_template": false, "name": "dm-validations-ext", "languages_url": "https://api.github.com/repos/solnic/dm-validations-ext/languages", "open_issues_count": 1, "contents_url": "https://api.github.com/repos/solnic/dm-validations-ext/contents/{+path}", "watchers": 11}, {"labels_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/hooks", "id": 149754421, "teams_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/teams", "full_name": "solnic/docker-nginx-proxy-example", "git_commits_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/downloads", "stargazers_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/stargazers", "blobs_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxNDk3NTQ0MjE=", "watchers_count": 2, "notifications_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/git/trees{/sha}", "clone_url": "https://github.com/solnic/docker-nginx-proxy-example.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/subscription", "url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example", "statuses_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/milestones{/number}", "svn_url": "https://github.com/solnic/docker-nginx-proxy-example", "events_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/events", "updated_at": "2020-01-31T18:35:24Z", "created_at": "2018-09-21T11:30:51Z", "html_url": "https://github.com/solnic/docker-nginx-proxy-example", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "JavaScript", "contributors_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/issues/events{/number}", "forks": 1, "merges_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/merges", "deployments_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/assignees{/user}", "git_url": "git://github.com/solnic/docker-nginx-proxy-example.git", "forks_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/forks", "tags_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/tags", "open_issues": 0, "size": 6, "pushed_at": "2018-09-21T11:55:25Z", "issues_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 1, "git_tags_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/branches{/branch}", "description": null, "subscribers_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/subscribers", "stargazers_count": 2, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/git/refs{/sha}", "ssh_url": "**************:solnic/docker-nginx-proxy-example.git", "releases_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/releases{/id}", "is_template": false, "name": "docker-nginx-proxy-example", "languages_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/docker-nginx-proxy-example/contents/{+path}", "watchers": 2}, {"labels_url": "https://api.github.com/repos/solnic/docs/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/docs/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/docs/hooks", "id": 345411965, "teams_url": "https://api.github.com/repos/solnic/docs/teams", "full_name": "solnic/docs", "git_commits_url": "https://api.github.com/repos/solnic/docs/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/docs/downloads", "stargazers_url": "https://api.github.com/repos/solnic/docs/stargazers", "blobs_url": "https://api.github.com/repos/solnic/docs/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/docs/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzNDU0MTE5NjU=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/docs/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/docs/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/docs/git/trees{/sha}", "clone_url": "https://github.com/solnic/docs.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/docs/subscription", "url": "https://api.github.com/repos/solnic/docs", "statuses_url": "https://api.github.com/repos/solnic/docs/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/docs/milestones{/number}", "svn_url": "https://github.com/solnic/docs", "events_url": "https://api.github.com/repos/solnic/docs/events", "updated_at": "2021-03-07T17:40:43Z", "created_at": "2021-03-07T17:40:42Z", "html_url": "https://github.com/solnic/docs", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/docs/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": null, "contributors_url": "https://api.github.com/repos/solnic/docs/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/docs/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/docs/merges", "deployments_url": "https://api.github.com/repos/solnic/docs/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/docs/assignees{/user}", "git_url": "git://github.com/solnic/docs.git", "forks_url": "https://api.github.com/repos/solnic/docs/forks", "tags_url": "https://api.github.com/repos/solnic/docs/tags", "open_issues": 0, "size": 339217, "pushed_at": "2021-03-07T17:43:15Z", "issues_url": "https://api.github.com/repos/solnic/docs/issues{/number}", "homepage": "https://docs.github.com", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/docs/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/docs/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/docs/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/docs/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/docs/branches{/branch}", "description": "The open-source repo for docs.github.com", "subscribers_url": "https://api.github.com/repos/solnic/docs/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "cc-by-4.0", "name": "Creative Commons Attribution 4.0 International", "node_id": "MDc6TGljZW5zZTI1", "spdx_id": "CC-BY-4.0", "url": "https://api.github.com/licenses/cc-by-4.0"}, "commits_url": "https://api.github.com/repos/solnic/docs/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/docs/git/refs{/sha}", "ssh_url": "**************:solnic/docs.git", "releases_url": "https://api.github.com/repos/solnic/docs/releases{/id}", "is_template": false, "name": "docs", "languages_url": "https://api.github.com/repos/solnic/docs/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/docs/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/drops/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/drops/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/drops/hooks", "id": 686925389, "teams_url": "https://api.github.com/repos/solnic/drops/teams", "full_name": "solnic/drops", "git_commits_url": "https://api.github.com/repos/solnic/drops/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/drops/downloads", "stargazers_url": "https://api.github.com/repos/solnic/drops/stargazers", "blobs_url": "https://api.github.com/repos/solnic/drops/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/drops/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "R_kgDOKPGmTQ", "watchers_count": 227, "notifications_url": "https://api.github.com/repos/solnic/drops/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/drops/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/drops/git/trees{/sha}", "clone_url": "https://github.com/solnic/drops.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/drops/subscription", "url": "https://api.github.com/repos/solnic/drops", "statuses_url": "https://api.github.com/repos/solnic/drops/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/drops/milestones{/number}", "svn_url": "https://github.com/solnic/drops", "events_url": "https://api.github.com/repos/solnic/drops/events", "updated_at": "2024-05-23T09:28:51Z", "created_at": "2023-09-04T08:36:15Z", "html_url": "https://github.com/solnic/drops", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/drops/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": ["data", "elixir", "elixir-lang", "elixir-library", "json", "schema", "validation"], "language": "<PERSON><PERSON><PERSON>", "contributors_url": "https://api.github.com/repos/solnic/drops/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/drops/issues/events{/number}", "forks": 3, "merges_url": "https://api.github.com/repos/solnic/drops/merges", "deployments_url": "https://api.github.com/repos/solnic/drops/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/drops/assignees{/user}", "git_url": "git://github.com/solnic/drops.git", "forks_url": "https://api.github.com/repos/solnic/drops/forks", "tags_url": "https://api.github.com/repos/solnic/drops/tags", "open_issues": 9, "size": 384, "pushed_at": "2024-02-01T08:06:25Z", "issues_url": "https://api.github.com/repos/solnic/drops/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 3, "git_tags_url": "https://api.github.com/repos/solnic/drops/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/drops/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/drops/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/drops/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/drops/branches{/branch}", "description": "🛠️ Tools for working with data effectively - data contracts using types, schemas, domain validation rules, type-safe casting, and more.", "subscribers_url": "https://api.github.com/repos/solnic/drops/subscribers", "stargazers_count": 227, "has_discussions": true, "license": {"key": "other", "name": "Other", "node_id": "MDc6TGljZW5zZTA=", "spdx_id": "NOASSERTION", "url": null}, "commits_url": "https://api.github.com/repos/solnic/drops/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/drops/git/refs{/sha}", "ssh_url": "**************:solnic/drops.git", "releases_url": "https://api.github.com/repos/solnic/drops/releases{/id}", "is_template": false, "name": "drops", "languages_url": "https://api.github.com/repos/solnic/drops/languages", "open_issues_count": 9, "contents_url": "https://api.github.com/repos/solnic/drops/contents/{+path}", "watchers": 227}, {"labels_url": "https://api.github.com/repos/solnic/dry-rb.org/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/dry-rb.org/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/dry-rb.org/hooks", "id": 192691804, "teams_url": "https://api.github.com/repos/solnic/dry-rb.org/teams", "full_name": "solnic/dry-rb.org", "git_commits_url": "https://api.github.com/repos/solnic/dry-rb.org/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/dry-rb.org/downloads", "stargazers_url": "https://api.github.com/repos/solnic/dry-rb.org/stargazers", "blobs_url": "https://api.github.com/repos/solnic/dry-rb.org/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/dry-rb.org/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxOTI2OTE4MDQ=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/dry-rb.org/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/dry-rb.org/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/dry-rb.org/git/trees{/sha}", "clone_url": "https://github.com/solnic/dry-rb.org.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/dry-rb.org/subscription", "url": "https://api.github.com/repos/solnic/dry-rb.org", "statuses_url": "https://api.github.com/repos/solnic/dry-rb.org/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/dry-rb.org/milestones{/number}", "svn_url": "https://github.com/solnic/dry-rb.org", "events_url": "https://api.github.com/repos/solnic/dry-rb.org/events", "updated_at": "2019-06-19T08:31:51Z", "created_at": "2019-06-19T08:31:48Z", "html_url": "https://github.com/solnic/dry-rb.org", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/dry-rb.org/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": "CSS", "contributors_url": "https://api.github.com/repos/solnic/dry-rb.org/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/dry-rb.org/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/dry-rb.org/merges", "deployments_url": "https://api.github.com/repos/solnic/dry-rb.org/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/dry-rb.org/assignees{/user}", "git_url": "git://github.com/solnic/dry-rb.org.git", "forks_url": "https://api.github.com/repos/solnic/dry-rb.org/forks", "tags_url": "https://api.github.com/repos/solnic/dry-rb.org/tags", "open_issues": 0, "size": 12588, "pushed_at": "2019-06-20T08:41:02Z", "issues_url": "https://api.github.com/repos/solnic/dry-rb.org/issues{/number}", "homepage": "https://dry-rb.org/", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/dry-rb.org/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/dry-rb.org/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/dry-rb.org/comments{/number}", "has_pages": true, "issue_comment_url": "https://api.github.com/repos/solnic/dry-rb.org/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/dry-rb.org/branches{/branch}", "description": "The official website of dry-rb", "subscribers_url": "https://api.github.com/repos/solnic/dry-rb.org/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/dry-rb.org/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/dry-rb.org/git/refs{/sha}", "ssh_url": "**************:solnic/dry-rb.org.git", "releases_url": "https://api.github.com/repos/solnic/dry-rb.org/releases{/id}", "is_template": false, "name": "dry-rb.org", "languages_url": "https://api.github.com/repos/solnic/dry-rb.org/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/dry-rb.org/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/ecto/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/ecto/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/ecto/hooks", "id": 43966287, "teams_url": "https://api.github.com/repos/solnic/ecto/teams", "full_name": "solnic/ecto", "git_commits_url": "https://api.github.com/repos/solnic/ecto/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/ecto/downloads", "stargazers_url": "https://api.github.com/repos/solnic/ecto/stargazers", "blobs_url": "https://api.github.com/repos/solnic/ecto/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/ecto/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk0Mzk2NjI4Nw==", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/ecto/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/ecto/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/ecto/git/trees{/sha}", "clone_url": "https://github.com/solnic/ecto.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/ecto/subscription", "url": "https://api.github.com/repos/solnic/ecto", "statuses_url": "https://api.github.com/repos/solnic/ecto/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/ecto/milestones{/number}", "svn_url": "https://github.com/solnic/ecto", "events_url": "https://api.github.com/repos/solnic/ecto/events", "updated_at": "2016-06-16T10:52:32Z", "created_at": "2015-10-09T16:07:10Z", "html_url": "https://github.com/solnic/ecto", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/ecto/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON><PERSON><PERSON>", "contributors_url": "https://api.github.com/repos/solnic/ecto/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/ecto/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/ecto/merges", "deployments_url": "https://api.github.com/repos/solnic/ecto/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/ecto/assignees{/user}", "git_url": "git://github.com/solnic/ecto.git", "forks_url": "https://api.github.com/repos/solnic/ecto/forks", "tags_url": "https://api.github.com/repos/solnic/ecto/tags", "open_issues": 0, "size": 5329, "pushed_at": "2015-10-09T16:10:25Z", "issues_url": "https://api.github.com/repos/solnic/ecto/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/ecto/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/ecto/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/ecto/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/ecto/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/ecto/branches{/branch}", "description": "A database wrapper and language integrated query for Elixir", "subscribers_url": "https://api.github.com/repos/solnic/ecto/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "other", "name": "Other", "node_id": "MDc6TGljZW5zZTA=", "spdx_id": "NOASSERTION", "url": null}, "commits_url": "https://api.github.com/repos/solnic/ecto/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/ecto/git/refs{/sha}", "ssh_url": "**************:solnic/ecto.git", "releases_url": "https://api.github.com/repos/solnic/ecto/releases{/id}", "is_template": false, "name": "ecto", "languages_url": "https://api.github.com/repos/solnic/ecto/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/ecto/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/hooks", "id": 509024655, "teams_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/teams", "full_name": "solnic/elixir-lang.github.com", "git_commits_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/downloads", "stargazers_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/stargazers", "blobs_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "R_kgDOHlcZjw", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/git/trees{/sha}", "clone_url": "https://github.com/solnic/elixir-lang.github.com.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/subscription", "url": "https://api.github.com/repos/solnic/elixir-lang.github.com", "statuses_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/milestones{/number}", "svn_url": "https://github.com/solnic/elixir-lang.github.com", "events_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/events", "updated_at": "2022-06-06T14:47:39Z", "created_at": "2022-06-30T09:55:30Z", "html_url": "https://github.com/solnic/elixir-lang.github.com", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": null, "contributors_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/merges", "deployments_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/assignees{/user}", "git_url": "git://github.com/solnic/elixir-lang.github.com.git", "forks_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/forks", "tags_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/tags", "open_issues": 0, "size": 15161, "pushed_at": "2022-06-30T09:56:13Z", "issues_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/issues{/number}", "homepage": "elixir-lang.org", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/branches{/branch}", "description": "Website for Elixir", "subscribers_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/git/refs{/sha}", "ssh_url": "**************:solnic/elixir-lang.github.com.git", "releases_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/releases{/id}", "is_template": false, "name": "elixir-lang.github.com", "languages_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/elixir-lang.github.com/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/exercism-elixir/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/exercism-elixir/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/exercism-elixir/hooks", "id": 480767359, "teams_url": "https://api.github.com/repos/solnic/exercism-elixir/teams", "full_name": "solnic/exercism-elixir", "git_commits_url": "https://api.github.com/repos/solnic/exercism-elixir/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/exercism-elixir/downloads", "stargazers_url": "https://api.github.com/repos/solnic/exercism-elixir/stargazers", "blobs_url": "https://api.github.com/repos/solnic/exercism-elixir/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/exercism-elixir/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "R_kgDOHKftfw", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/exercism-elixir/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/exercism-elixir/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/exercism-elixir/git/trees{/sha}", "clone_url": "https://github.com/solnic/exercism-elixir.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/exercism-elixir/subscription", "url": "https://api.github.com/repos/solnic/exercism-elixir", "statuses_url": "https://api.github.com/repos/solnic/exercism-elixir/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/exercism-elixir/milestones{/number}", "svn_url": "https://github.com/solnic/exercism-elixir", "events_url": "https://api.github.com/repos/solnic/exercism-elixir/events", "updated_at": "2022-04-11T18:11:29Z", "created_at": "2022-04-12T10:48:05Z", "html_url": "https://github.com/solnic/exercism-elixir", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/exercism-elixir/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": null, "contributors_url": "https://api.github.com/repos/solnic/exercism-elixir/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/exercism-elixir/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/exercism-elixir/merges", "deployments_url": "https://api.github.com/repos/solnic/exercism-elixir/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/exercism-elixir/assignees{/user}", "git_url": "git://github.com/solnic/exercism-elixir.git", "forks_url": "https://api.github.com/repos/solnic/exercism-elixir/forks", "tags_url": "https://api.github.com/repos/solnic/exercism-elixir/tags", "open_issues": 0, "size": 3451, "pushed_at": "2022-04-13T16:12:07Z", "issues_url": "https://api.github.com/repos/solnic/exercism-elixir/issues{/number}", "homepage": "https://exercism.org/tracks/elixir", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/exercism-elixir/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/exercism-elixir/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/exercism-elixir/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/exercism-elixir/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/exercism-elixir/branches{/branch}", "description": "Exercism exercises in Elixir.", "subscribers_url": "https://api.github.com/repos/solnic/exercism-elixir/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/exercism-elixir/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/exercism-elixir/git/refs{/sha}", "ssh_url": "**************:solnic/exercism-elixir.git", "releases_url": "https://api.github.com/repos/solnic/exercism-elixir/releases{/id}", "is_template": false, "name": "exercism-elixir", "languages_url": "https://api.github.com/repos/solnic/exercism-elixir/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/exercism-elixir/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/hooks", "id": 239617824, "teams_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/teams", "full_name": "solnic/exploding-rails-v2-examples", "git_commits_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/downloads", "stargazers_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/stargazers", "blobs_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkyMzk2MTc4MjQ=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/git/trees{/sha}", "clone_url": "https://github.com/solnic/exploding-rails-v2-examples.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/subscription", "url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples", "statuses_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/milestones{/number}", "svn_url": "https://github.com/solnic/exploding-rails-v2-examples", "events_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/events", "updated_at": "2020-02-10T21:43:16Z", "created_at": "2020-02-10T21:33:06Z", "html_url": "https://github.com/solnic/exploding-rails-v2-examples", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/merges", "deployments_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/assignees{/user}", "git_url": "git://github.com/solnic/exploding-rails-v2-examples.git", "forks_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/forks", "tags_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/tags", "open_issues": 0, "size": 69, "pushed_at": "2020-02-10T21:43:13Z", "issues_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/branches{/branch}", "description": null, "subscribers_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/git/refs{/sha}", "ssh_url": "**************:solnic/exploding-rails-v2-examples.git", "releases_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/releases{/id}", "is_template": false, "name": "exploding-rails-v2-examples", "languages_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/exploding-rails-v2-examples/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/hanami/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/hanami/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/hanami/hooks", "id": 240799375, "teams_url": "https://api.github.com/repos/solnic/hanami/teams", "full_name": "solnic/hanami", "git_commits_url": "https://api.github.com/repos/solnic/hanami/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/hanami/downloads", "stargazers_url": "https://api.github.com/repos/solnic/hanami/stargazers", "blobs_url": "https://api.github.com/repos/solnic/hanami/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/hanami/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkyNDA3OTkzNzU=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/hanami/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/hanami/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/hanami/git/trees{/sha}", "clone_url": "https://github.com/solnic/hanami.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/hanami/subscription", "url": "https://api.github.com/repos/solnic/hanami", "statuses_url": "https://api.github.com/repos/solnic/hanami/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/hanami/milestones{/number}", "svn_url": "https://github.com/solnic/hanami", "events_url": "https://api.github.com/repos/solnic/hanami/events", "updated_at": "2020-02-15T22:54:38Z", "created_at": "2020-02-15T22:54:36Z", "html_url": "https://github.com/solnic/hanami", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/hanami/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": null, "contributors_url": "https://api.github.com/repos/solnic/hanami/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/hanami/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/hanami/merges", "deployments_url": "https://api.github.com/repos/solnic/hanami/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/hanami/assignees{/user}", "git_url": "git://github.com/solnic/hanami.git", "forks_url": "https://api.github.com/repos/solnic/hanami/forks", "tags_url": "https://api.github.com/repos/solnic/hanami/tags", "open_issues": 0, "size": 25879, "pushed_at": "2021-04-20T07:42:59Z", "issues_url": "https://api.github.com/repos/solnic/hanami/issues{/number}", "homepage": "http://hanamirb.org", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/hanami/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/hanami/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/hanami/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/hanami/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/hanami/branches{/branch}", "description": "The web, with simplicity.", "subscribers_url": "https://api.github.com/repos/solnic/hanami/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/hanami/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/hanami/git/refs{/sha}", "ssh_url": "**************:solnic/hanami.git", "releases_url": "https://api.github.com/repos/solnic/hanami/releases{/id}", "is_template": false, "name": "hanami", "languages_url": "https://api.github.com/repos/solnic/hanami/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/hanami/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/hanami-2-application-template/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/hanami-2-application-template/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/hanami-2-application-template/hooks", "id": 349408784, "teams_url": "https://api.github.com/repos/solnic/hanami-2-application-template/teams", "full_name": "solnic/hanami-2-application-template", "git_commits_url": "https://api.github.com/repos/solnic/hanami-2-application-template/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/hanami-2-application-template/downloads", "stargazers_url": "https://api.github.com/repos/solnic/hanami-2-application-template/stargazers", "blobs_url": "https://api.github.com/repos/solnic/hanami-2-application-template/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/hanami-2-application-template/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzNDk0MDg3ODQ=", "watchers_count": 1, "notifications_url": "https://api.github.com/repos/solnic/hanami-2-application-template/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/hanami-2-application-template/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/hanami-2-application-template/git/trees{/sha}", "clone_url": "https://github.com/solnic/hanami-2-application-template.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/hanami-2-application-template/subscription", "url": "https://api.github.com/repos/solnic/hanami-2-application-template", "statuses_url": "https://api.github.com/repos/solnic/hanami-2-application-template/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/hanami-2-application-template/milestones{/number}", "svn_url": "https://github.com/solnic/hanami-2-application-template", "events_url": "https://api.github.com/repos/solnic/hanami-2-application-template/events", "updated_at": "2022-09-17T10:52:24Z", "created_at": "2021-03-19T12:02:42Z", "html_url": "https://github.com/solnic/hanami-2-application-template", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/hanami-2-application-template/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": null, "contributors_url": "https://api.github.com/repos/solnic/hanami-2-application-template/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/hanami-2-application-template/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/hanami-2-application-template/merges", "deployments_url": "https://api.github.com/repos/solnic/hanami-2-application-template/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/hanami-2-application-template/assignees{/user}", "git_url": "git://github.com/solnic/hanami-2-application-template.git", "forks_url": "https://api.github.com/repos/solnic/hanami-2-application-template/forks", "tags_url": "https://api.github.com/repos/solnic/hanami-2-application-template/tags", "open_issues": 0, "size": 313, "pushed_at": "2021-04-30T11:29:20Z", "issues_url": "https://api.github.com/repos/solnic/hanami-2-application-template/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/hanami-2-application-template/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/hanami-2-application-template/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/hanami-2-application-template/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/hanami-2-application-template/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/hanami-2-application-template/branches{/branch}", "description": "Hanami 2 application starter template", "subscribers_url": "https://api.github.com/repos/solnic/hanami-2-application-template/subscribers", "stargazers_count": 1, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/hanami-2-application-template/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/hanami-2-application-template/git/refs{/sha}", "ssh_url": "**************:solnic/hanami-2-application-template.git", "releases_url": "https://api.github.com/repos/solnic/hanami-2-application-template/releases{/id}", "is_template": true, "name": "hanami-2-application-template", "languages_url": "https://api.github.com/repos/solnic/hanami-2-application-template/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/hanami-2-application-template/contents/{+path}", "watchers": 1}, {"labels_url": "https://api.github.com/repos/solnic/hanami-bookshelf/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/hanami-bookshelf/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/hanami-bookshelf/hooks", "id": 576657023, "teams_url": "https://api.github.com/repos/solnic/hanami-bookshelf/teams", "full_name": "solnic/hanami-bookshelf", "git_commits_url": "https://api.github.com/repos/solnic/hanami-bookshelf/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/hanami-bookshelf/downloads", "stargazers_url": "https://api.github.com/repos/solnic/hanami-bookshelf/stargazers", "blobs_url": "https://api.github.com/repos/solnic/hanami-bookshelf/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/hanami-bookshelf/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "R_kgDOIl8Wfw", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/hanami-bookshelf/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/hanami-bookshelf/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/hanami-bookshelf/git/trees{/sha}", "clone_url": "https://github.com/solnic/hanami-bookshelf.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/hanami-bookshelf/subscription", "url": "https://api.github.com/repos/solnic/hanami-bookshelf", "statuses_url": "https://api.github.com/repos/solnic/hanami-bookshelf/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/hanami-bookshelf/milestones{/number}", "svn_url": "https://github.com/solnic/hanami-bookshelf", "events_url": "https://api.github.com/repos/solnic/hanami-bookshelf/events", "updated_at": "2022-12-10T14:59:44Z", "created_at": "2022-12-10T14:58:49Z", "html_url": "https://github.com/solnic/hanami-bookshelf", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/hanami-bookshelf/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/hanami-bookshelf/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/hanami-bookshelf/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/hanami-bookshelf/merges", "deployments_url": "https://api.github.com/repos/solnic/hanami-bookshelf/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/hanami-bookshelf/assignees{/user}", "git_url": "git://github.com/solnic/hanami-bookshelf.git", "forks_url": "https://api.github.com/repos/solnic/hanami-bookshelf/forks", "tags_url": "https://api.github.com/repos/solnic/hanami-bookshelf/tags", "open_issues": 0, "size": 12, "pushed_at": "2022-12-10T14:59:39Z", "issues_url": "https://api.github.com/repos/solnic/hanami-bookshelf/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/hanami-bookshelf/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/hanami-bookshelf/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/hanami-bookshelf/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/hanami-bookshelf/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/hanami-bookshelf/branches{/branch}", "description": "Hanami 2 Bookshelf", "subscribers_url": "https://api.github.com/repos/solnic/hanami-bookshelf/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/hanami-bookshelf/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/hanami-bookshelf/git/refs{/sha}", "ssh_url": "**************:solnic/hanami-bookshelf.git", "releases_url": "https://api.github.com/repos/solnic/hanami-bookshelf/releases{/id}", "is_template": false, "name": "hanami-bookshelf", "languages_url": "https://api.github.com/repos/solnic/hanami-bookshelf/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/hanami-bookshelf/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/hooks", "id": 180831876, "teams_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/teams", "full_name": "solnic/hanami-bookshelf-rom", "git_commits_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/downloads", "stargazers_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/stargazers", "blobs_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxODA4MzE4NzY=", "watchers_count": 18, "notifications_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/git/trees{/sha}", "clone_url": "https://github.com/solnic/hanami-bookshelf-rom.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/subscription", "url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom", "statuses_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/milestones{/number}", "svn_url": "https://github.com/solnic/hanami-bookshelf-rom", "events_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/events", "updated_at": "2022-09-13T07:25:07Z", "created_at": "2019-04-11T16:22:52Z", "html_url": "https://github.com/solnic/hanami-bookshelf-rom", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/issues/events{/number}", "forks": 3, "merges_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/merges", "deployments_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/assignees{/user}", "git_url": "git://github.com/solnic/hanami-bookshelf-rom.git", "forks_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/forks", "tags_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/tags", "open_issues": 0, "size": 36, "pushed_at": "2019-04-12T07:24:49Z", "issues_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 3, "git_tags_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/branches{/branch}", "description": "Hanami 1.x bookshelf app based on tutorial, tweaked to use rom-rb 4.x standalone", "subscribers_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/subscribers", "stargazers_count": 18, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/git/refs{/sha}", "ssh_url": "**************:solnic/hanami-bookshelf-rom.git", "releases_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/releases{/id}", "is_template": false, "name": "hanami-bookshelf-rom", "languages_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/hanami-bookshelf-rom/contents/{+path}", "watchers": 18}, {"labels_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/hooks", "id": 375391781, "teams_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/teams", "full_name": "solnic/hanami-dockerized-demo", "git_commits_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/downloads", "stargazers_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/stargazers", "blobs_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzNzUzOTE3ODE=", "watchers_count": 7, "notifications_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/git/trees{/sha}", "clone_url": "https://github.com/solnic/hanami-dockerized-demo.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/subscription", "url": "https://api.github.com/repos/solnic/hanami-dockerized-demo", "statuses_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/milestones{/number}", "svn_url": "https://github.com/solnic/hanami-dockerized-demo", "events_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/events", "updated_at": "2022-05-20T13:44:42Z", "created_at": "2021-06-09T14:51:15Z", "html_url": "https://github.com/solnic/hanami-dockerized-demo", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/merges", "deployments_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/assignees{/user}", "git_url": "git://github.com/solnic/hanami-dockerized-demo.git", "forks_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/forks", "tags_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/tags", "open_issues": 0, "size": 34, "pushed_at": "2021-06-09T15:38:19Z", "issues_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/branches{/branch}", "description": "A sample Hanami app with a docker-compose-driven-development-and-CI aka one docker-compose to rule them all.", "subscribers_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/subscribers", "stargazers_count": 7, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/git/refs{/sha}", "ssh_url": "**************:solnic/hanami-dockerized-demo.git", "releases_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/releases{/id}", "is_template": false, "name": "hanami-dockerized-demo", "languages_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/hanami-dockerized-demo/contents/{+path}", "watchers": 7}, {"labels_url": "https://api.github.com/repos/solnic/hanami-docker_demo/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/hanami-docker_demo/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/hanami-docker_demo/hooks", "id": 613752533, "teams_url": "https://api.github.com/repos/solnic/hanami-docker_demo/teams", "full_name": "solnic/hanami-docker_demo", "git_commits_url": "https://api.github.com/repos/solnic/hanami-docker_demo/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/hanami-docker_demo/downloads", "stargazers_url": "https://api.github.com/repos/solnic/hanami-docker_demo/stargazers", "blobs_url": "https://api.github.com/repos/solnic/hanami-docker_demo/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/hanami-docker_demo/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "R_kgDOJJUe1Q", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/hanami-docker_demo/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/hanami-docker_demo/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/hanami-docker_demo/git/trees{/sha}", "clone_url": "https://github.com/solnic/hanami-docker_demo.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/hanami-docker_demo/subscription", "url": "https://api.github.com/repos/solnic/hanami-docker_demo", "statuses_url": "https://api.github.com/repos/solnic/hanami-docker_demo/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/hanami-docker_demo/milestones{/number}", "svn_url": "https://github.com/solnic/hanami-docker_demo", "events_url": "https://api.github.com/repos/solnic/hanami-docker_demo/events", "updated_at": "2023-03-14T07:48:12Z", "created_at": "2023-03-14T07:47:57Z", "html_url": "https://github.com/solnic/hanami-docker_demo", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/hanami-docker_demo/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/hanami-docker_demo/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/hanami-docker_demo/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/hanami-docker_demo/merges", "deployments_url": "https://api.github.com/repos/solnic/hanami-docker_demo/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/hanami-docker_demo/assignees{/user}", "git_url": "git://github.com/solnic/hanami-docker_demo.git", "forks_url": "https://api.github.com/repos/solnic/hanami-docker_demo/forks", "tags_url": "https://api.github.com/repos/solnic/hanami-docker_demo/tags", "open_issues": 0, "size": 20, "pushed_at": "2023-03-14T08:22:24Z", "issues_url": "https://api.github.com/repos/solnic/hanami-docker_demo/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/hanami-docker_demo/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/hanami-docker_demo/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/hanami-docker_demo/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/hanami-docker_demo/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/hanami-docker_demo/branches{/branch}", "description": null, "subscribers_url": "https://api.github.com/repos/solnic/hanami-docker_demo/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/hanami-docker_demo/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/hanami-docker_demo/git/refs{/sha}", "ssh_url": "**************:solnic/hanami-docker_demo.git", "releases_url": "https://api.github.com/repos/solnic/hanami-docker_demo/releases{/id}", "is_template": false, "name": "hanami-docker_demo", "languages_url": "https://api.github.com/repos/solnic/hanami-docker_demo/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/hanami-docker_demo/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/hanami-model/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/hanami-model/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/hanami-model/hooks", "id": 78637587, "teams_url": "https://api.github.com/repos/solnic/hanami-model/teams", "full_name": "solnic/hanami-model", "git_commits_url": "https://api.github.com/repos/solnic/hanami-model/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/hanami-model/downloads", "stargazers_url": "https://api.github.com/repos/solnic/hanami-model/stargazers", "blobs_url": "https://api.github.com/repos/solnic/hanami-model/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/hanami-model/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk3ODYzNzU4Nw==", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/hanami-model/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/hanami-model/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/hanami-model/git/trees{/sha}", "clone_url": "https://github.com/solnic/hanami-model.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/hanami-model/subscription", "url": "https://api.github.com/repos/solnic/hanami-model", "statuses_url": "https://api.github.com/repos/solnic/hanami-model/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/hanami-model/milestones{/number}", "svn_url": "https://github.com/solnic/hanami-model", "events_url": "https://api.github.com/repos/solnic/hanami-model/events", "updated_at": "2018-06-10T21:09:07Z", "created_at": "2017-01-11T12:36:02Z", "html_url": "https://github.com/solnic/hanami-model", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/hanami-model/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/hanami-model/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/hanami-model/issues/events{/number}", "forks": 1, "merges_url": "https://api.github.com/repos/solnic/hanami-model/merges", "deployments_url": "https://api.github.com/repos/solnic/hanami-model/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/hanami-model/assignees{/user}", "git_url": "git://github.com/solnic/hanami-model.git", "forks_url": "https://api.github.com/repos/solnic/hanami-model/forks", "tags_url": "https://api.github.com/repos/solnic/hanami-model/tags", "open_issues": 0, "size": 1499, "pushed_at": "2018-06-10T21:18:44Z", "issues_url": "https://api.github.com/repos/solnic/hanami-model/issues{/number}", "homepage": "http://hanamirb.org", "private": false, "disabled": false, "forks_count": 1, "git_tags_url": "https://api.github.com/repos/solnic/hanami-model/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/hanami-model/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/hanami-model/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/hanami-model/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/hanami-model/branches{/branch}", "description": "A persistence framework with entities and repositories", "subscribers_url": "https://api.github.com/repos/solnic/hanami-model/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/hanami-model/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/hanami-model/git/refs{/sha}", "ssh_url": "**************:solnic/hanami-model.git", "releases_url": "https://api.github.com/repos/solnic/hanami-model/releases{/id}", "is_template": false, "name": "hanami-model", "languages_url": "https://api.github.com/repos/solnic/hanami-model/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/hanami-model/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/homebridge/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/homebridge/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/homebridge/hooks", "id": 154823797, "teams_url": "https://api.github.com/repos/solnic/homebridge/teams", "full_name": "solnic/homebridge", "git_commits_url": "https://api.github.com/repos/solnic/homebridge/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/homebridge/downloads", "stargazers_url": "https://api.github.com/repos/solnic/homebridge/stargazers", "blobs_url": "https://api.github.com/repos/solnic/homebridge/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/homebridge/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxNTQ4MjM3OTc=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/homebridge/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/homebridge/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/homebridge/git/trees{/sha}", "clone_url": "https://github.com/solnic/homebridge.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/homebridge/subscription", "url": "https://api.github.com/repos/solnic/homebridge", "statuses_url": "https://api.github.com/repos/solnic/homebridge/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/homebridge/milestones{/number}", "svn_url": "https://github.com/solnic/homebridge", "events_url": "https://api.github.com/repos/solnic/homebridge/events", "updated_at": "2020-03-13T16:26:29Z", "created_at": "2018-10-26T11:25:44Z", "html_url": "https://github.com/solnic/homebridge", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/homebridge/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "JavaScript", "contributors_url": "https://api.github.com/repos/solnic/homebridge/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/homebridge/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/homebridge/merges", "deployments_url": "https://api.github.com/repos/solnic/homebridge/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/homebridge/assignees{/user}", "git_url": "git://github.com/solnic/homebridge.git", "forks_url": "https://api.github.com/repos/solnic/homebridge/forks", "tags_url": "https://api.github.com/repos/solnic/homebridge/tags", "open_issues": 0, "size": 1679, "pushed_at": "2018-10-26T00:05:50Z", "issues_url": "https://api.github.com/repos/solnic/homebridge/issues{/number}", "homepage": "https://homebridge.io", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/homebridge/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/homebridge/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/homebridge/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/homebridge/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/homebridge/branches{/branch}", "description": "HomeKit support for the impatient", "subscribers_url": "https://api.github.com/repos/solnic/homebridge/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "apache-2.0", "name": "Apache License 2.0", "node_id": "MDc6TGljZW5zZTI=", "spdx_id": "Apache-2.0", "url": "https://api.github.com/licenses/apache-2.0"}, "commits_url": "https://api.github.com/repos/solnic/homebridge/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/homebridge/git/refs{/sha}", "ssh_url": "**************:solnic/homebridge.git", "releases_url": "https://api.github.com/repos/solnic/homebridge/releases{/id}", "is_template": false, "name": "homebridge", "languages_url": "https://api.github.com/repos/solnic/homebridge/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/homebridge/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/karafka/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/karafka/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/karafka/hooks", "id": 240702402, "teams_url": "https://api.github.com/repos/solnic/karafka/teams", "full_name": "solnic/karafka", "git_commits_url": "https://api.github.com/repos/solnic/karafka/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/karafka/downloads", "stargazers_url": "https://api.github.com/repos/solnic/karafka/stargazers", "blobs_url": "https://api.github.com/repos/solnic/karafka/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/karafka/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkyNDA3MDI0MDI=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/karafka/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/karafka/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/karafka/git/trees{/sha}", "clone_url": "https://github.com/solnic/karafka.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/karafka/subscription", "url": "https://api.github.com/repos/solnic/karafka", "statuses_url": "https://api.github.com/repos/solnic/karafka/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/karafka/milestones{/number}", "svn_url": "https://github.com/solnic/karafka", "events_url": "https://api.github.com/repos/solnic/karafka/events", "updated_at": "2020-02-15T12:04:09Z", "created_at": "2020-02-15T12:04:07Z", "html_url": "https://github.com/solnic/karafka", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/karafka/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": null, "contributors_url": "https://api.github.com/repos/solnic/karafka/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/karafka/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/karafka/merges", "deployments_url": "https://api.github.com/repos/solnic/karafka/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/karafka/assignees{/user}", "git_url": "git://github.com/solnic/karafka.git", "forks_url": "https://api.github.com/repos/solnic/karafka/forks", "tags_url": "https://api.github.com/repos/solnic/karafka/tags", "open_issues": 0, "size": 1585, "pushed_at": "2020-02-17T08:46:50Z", "issues_url": "https://api.github.com/repos/solnic/karafka/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/karafka/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/karafka/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/karafka/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/karafka/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/karafka/branches{/branch}", "description": "Framework for Apache Kafka based Ruby and Rails applications development.", "subscribers_url": "https://api.github.com/repos/solnic/karafka/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/karafka/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/karafka/git/refs{/sha}", "ssh_url": "**************:solnic/karafka.git", "releases_url": "https://api.github.com/repos/solnic/karafka/releases{/id}", "is_template": false, "name": "karafka", "languages_url": "https://api.github.com/repos/solnic/karafka/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/karafka/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/literal_enums-rails/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/literal_enums-rails/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/literal_enums-rails/hooks", "id": 578489197, "teams_url": "https://api.github.com/repos/solnic/literal_enums-rails/teams", "full_name": "solnic/literal_enums-rails", "git_commits_url": "https://api.github.com/repos/solnic/literal_enums-rails/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/literal_enums-rails/downloads", "stargazers_url": "https://api.github.com/repos/solnic/literal_enums-rails/stargazers", "blobs_url": "https://api.github.com/repos/solnic/literal_enums-rails/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/literal_enums-rails/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "R_kgDOInsLbQ", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/literal_enums-rails/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/literal_enums-rails/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/literal_enums-rails/git/trees{/sha}", "clone_url": "https://github.com/solnic/literal_enums-rails.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/literal_enums-rails/subscription", "url": "https://api.github.com/repos/solnic/literal_enums-rails", "statuses_url": "https://api.github.com/repos/solnic/literal_enums-rails/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/literal_enums-rails/milestones{/number}", "svn_url": "https://github.com/solnic/literal_enums-rails", "events_url": "https://api.github.com/repos/solnic/literal_enums-rails/events", "updated_at": "2022-12-14T03:09:49Z", "created_at": "2022-12-15T07:14:26Z", "html_url": "https://github.com/solnic/literal_enums-rails", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/literal_enums-rails/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": null, "contributors_url": "https://api.github.com/repos/solnic/literal_enums-rails/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/literal_enums-rails/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/literal_enums-rails/merges", "deployments_url": "https://api.github.com/repos/solnic/literal_enums-rails/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/literal_enums-rails/assignees{/user}", "git_url": "git://github.com/solnic/literal_enums-rails.git", "forks_url": "https://api.github.com/repos/solnic/literal_enums-rails/forks", "tags_url": "https://api.github.com/repos/solnic/literal_enums-rails/tags", "open_issues": 0, "size": 24, "pushed_at": "2022-12-15T09:14:08Z", "issues_url": "https://api.github.com/repos/solnic/literal_enums-rails/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/literal_enums-rails/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/literal_enums-rails/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/literal_enums-rails/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/literal_enums-rails/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/literal_enums-rails/branches{/branch}", "description": "Literal Enums for Rails", "subscribers_url": "https://api.github.com/repos/solnic/literal_enums-rails/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/literal_enums-rails/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/literal_enums-rails/git/refs{/sha}", "ssh_url": "**************:solnic/literal_enums-rails.git", "releases_url": "https://api.github.com/repos/solnic/literal_enums-rails/releases{/id}", "is_template": false, "name": "literal_enums-rails", "languages_url": "https://api.github.com/repos/solnic/literal_enums-rails/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/literal_enums-rails/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/lotuskase/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/lotuskase/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/lotuskase/hooks", "id": 48624434, "teams_url": "https://api.github.com/repos/solnic/lotuskase/teams", "full_name": "solnic/lotuskase", "git_commits_url": "https://api.github.com/repos/solnic/lotuskase/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/lotuskase/downloads", "stargazers_url": "https://api.github.com/repos/solnic/lotuskase/stargazers", "blobs_url": "https://api.github.com/repos/solnic/lotuskase/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/lotuskase/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk0ODYyNDQzNA==", "watchers_count": 8, "notifications_url": "https://api.github.com/repos/solnic/lotuskase/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/lotuskase/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/lotuskase/git/trees{/sha}", "clone_url": "https://github.com/solnic/lotuskase.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/lotuskase/subscription", "url": "https://api.github.com/repos/solnic/lotuskase", "statuses_url": "https://api.github.com/repos/solnic/lotuskase/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/lotuskase/milestones{/number}", "svn_url": "https://github.com/solnic/lotuskase", "events_url": "https://api.github.com/repos/solnic/lotuskase/events", "updated_at": "2020-02-29T10:57:40Z", "created_at": "2015-12-26T21:45:33Z", "html_url": "https://github.com/solnic/lotuskase", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/lotuskase/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/lotuskase/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/lotuskase/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/lotuskase/merges", "deployments_url": "https://api.github.com/repos/solnic/lotuskase/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/lotuskase/assignees{/user}", "git_url": "git://github.com/solnic/lotuskase.git", "forks_url": "https://api.github.com/repos/solnic/lotuskase/forks", "tags_url": "https://api.github.com/repos/solnic/lotuskase/tags", "open_issues": 0, "size": 12, "pushed_at": "2015-12-28T16:31:00Z", "issues_url": "https://api.github.com/repos/solnic/lotuskase/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/lotuskase/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/lotuskase/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/lotuskase/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/lotuskase/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/lotuskase/branches{/branch}", "description": "Custom web stack based on dry-component and lotus", "subscribers_url": "https://api.github.com/repos/solnic/lotuskase/subscribers", "stargazers_count": 8, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/lotuskase/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/lotuskase/git/refs{/sha}", "ssh_url": "**************:solnic/lotuskase.git", "releases_url": "https://api.github.com/repos/solnic/lotuskase/releases{/id}", "is_template": false, "name": "lotuskase", "languages_url": "https://api.github.com/repos/solnic/lotuskase/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/lotuskase/contents/{+path}", "watchers": 8}, {"labels_url": "https://api.github.com/repos/solnic/middleman-docsite/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/middleman-docsite/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/middleman-docsite/hooks", "id": 211646942, "teams_url": "https://api.github.com/repos/solnic/middleman-docsite/teams", "full_name": "solnic/middleman-docsite", "git_commits_url": "https://api.github.com/repos/solnic/middleman-docsite/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/middleman-docsite/downloads", "stargazers_url": "https://api.github.com/repos/solnic/middleman-docsite/stargazers", "blobs_url": "https://api.github.com/repos/solnic/middleman-docsite/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/middleman-docsite/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkyMTE2NDY5NDI=", "watchers_count": 2, "notifications_url": "https://api.github.com/repos/solnic/middleman-docsite/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/middleman-docsite/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/middleman-docsite/git/trees{/sha}", "clone_url": "https://github.com/solnic/middleman-docsite.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/middleman-docsite/subscription", "url": "https://api.github.com/repos/solnic/middleman-docsite", "statuses_url": "https://api.github.com/repos/solnic/middleman-docsite/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/middleman-docsite/milestones{/number}", "svn_url": "https://github.com/solnic/middleman-docsite", "events_url": "https://api.github.com/repos/solnic/middleman-docsite/events", "updated_at": "2022-01-27T07:48:11Z", "created_at": "2019-09-29T10:38:56Z", "html_url": "https://github.com/solnic/middleman-docsite", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/middleman-docsite/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/middleman-docsite/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/middleman-docsite/issues/events{/number}", "forks": 2, "merges_url": "https://api.github.com/repos/solnic/middleman-docsite/merges", "deployments_url": "https://api.github.com/repos/solnic/middleman-docsite/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/middleman-docsite/assignees{/user}", "git_url": "git://github.com/solnic/middleman-docsite.git", "forks_url": "https://api.github.com/repos/solnic/middleman-docsite/forks", "tags_url": "https://api.github.com/repos/solnic/middleman-docsite/tags", "open_issues": 2, "size": 66, "pushed_at": "2023-03-16T08:54:48Z", "issues_url": "https://api.github.com/repos/solnic/middleman-docsite/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 2, "git_tags_url": "https://api.github.com/repos/solnic/middleman-docsite/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/middleman-docsite/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/middleman-docsite/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/middleman-docsite/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/middleman-docsite/branches{/branch}", "description": "Various middleman extensions extracted from rom-rb and dry-rb websites", "subscribers_url": "https://api.github.com/repos/solnic/middleman-docsite/subscribers", "stargazers_count": 2, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/middleman-docsite/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/middleman-docsite/git/refs{/sha}", "ssh_url": "**************:solnic/middleman-docsite.git", "releases_url": "https://api.github.com/repos/solnic/middleman-docsite/releases{/id}", "is_template": false, "name": "middleman-docsite", "languages_url": "https://api.github.com/repos/solnic/middleman-docsite/languages", "open_issues_count": 2, "contents_url": "https://api.github.com/repos/solnic/middleman-docsite/contents/{+path}", "watchers": 2}, {"labels_url": "https://api.github.com/repos/solnic/obsidian-link-embed/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/obsidian-link-embed/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/obsidian-link-embed/hooks", "id": 544737275, "teams_url": "https://api.github.com/repos/solnic/obsidian-link-embed/teams", "full_name": "solnic/obsidian-link-embed", "git_commits_url": "https://api.github.com/repos/solnic/obsidian-link-embed/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/obsidian-link-embed/downloads", "stargazers_url": "https://api.github.com/repos/solnic/obsidian-link-embed/stargazers", "blobs_url": "https://api.github.com/repos/solnic/obsidian-link-embed/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/obsidian-link-embed/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "R_kgDOIHgH-w", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/obsidian-link-embed/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/obsidian-link-embed/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/obsidian-link-embed/git/trees{/sha}", "clone_url": "https://github.com/solnic/obsidian-link-embed.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/obsidian-link-embed/subscription", "url": "https://api.github.com/repos/solnic/obsidian-link-embed", "statuses_url": "https://api.github.com/repos/solnic/obsidian-link-embed/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/obsidian-link-embed/milestones{/number}", "svn_url": "https://github.com/solnic/obsidian-link-embed", "events_url": "https://api.github.com/repos/solnic/obsidian-link-embed/events", "updated_at": "2022-10-03T00:19:15Z", "created_at": "2022-10-03T06:14:02Z", "html_url": "https://github.com/solnic/obsidian-link-embed", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/obsidian-link-embed/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": null, "contributors_url": "https://api.github.com/repos/solnic/obsidian-link-embed/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/obsidian-link-embed/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/obsidian-link-embed/merges", "deployments_url": "https://api.github.com/repos/solnic/obsidian-link-embed/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/obsidian-link-embed/assignees{/user}", "git_url": "git://github.com/solnic/obsidian-link-embed.git", "forks_url": "https://api.github.com/repos/solnic/obsidian-link-embed/forks", "tags_url": "https://api.github.com/repos/solnic/obsidian-link-embed/tags", "open_issues": 0, "size": 1752, "pushed_at": "2022-10-02T17:35:25Z", "issues_url": "https://api.github.com/repos/solnic/obsidian-link-embed/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/obsidian-link-embed/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/obsidian-link-embed/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/obsidian-link-embed/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/obsidian-link-embed/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/obsidian-link-embed/branches{/branch}", "description": "This plugin allow you to convert URLs in your notes into embeded previews.", "subscribers_url": "https://api.github.com/repos/solnic/obsidian-link-embed/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/obsidian-link-embed/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/obsidian-link-embed/git/refs{/sha}", "ssh_url": "**************:solnic/obsidian-link-embed.git", "releases_url": "https://api.github.com/repos/solnic/obsidian-link-embed/releases{/id}", "is_template": false, "name": "obsidian-link-embed", "languages_url": "https://api.github.com/repos/solnic/obsidian-link-embed/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/obsidian-link-embed/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/ossy/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/ossy/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/ossy/hooks", "id": 213887910, "teams_url": "https://api.github.com/repos/solnic/ossy/teams", "full_name": "solnic/ossy", "git_commits_url": "https://api.github.com/repos/solnic/ossy/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/ossy/downloads", "stargazers_url": "https://api.github.com/repos/solnic/ossy/stargazers", "blobs_url": "https://api.github.com/repos/solnic/ossy/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/ossy/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkyMTM4ODc5MTA=", "watchers_count": 9, "notifications_url": "https://api.github.com/repos/solnic/ossy/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/ossy/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/ossy/git/trees{/sha}", "clone_url": "https://github.com/solnic/ossy.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/ossy/subscription", "url": "https://api.github.com/repos/solnic/ossy", "statuses_url": "https://api.github.com/repos/solnic/ossy/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/ossy/milestones{/number}", "svn_url": "https://github.com/solnic/ossy", "events_url": "https://api.github.com/repos/solnic/ossy/events", "updated_at": "2023-12-05T15:21:39Z", "created_at": "2019-10-09T10:24:25Z", "html_url": "https://github.com/solnic/ossy", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/ossy/pulls{/number}", "mirror_url": null, "has_projects": false, "has_wiki": false, "topics": ["cli", "github-actions", "github-api", "helper", "maintenance", "ruby", "rubygem"], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/ossy/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/ossy/issues/events{/number}", "forks": 1, "merges_url": "https://api.github.com/repos/solnic/ossy/merges", "deployments_url": "https://api.github.com/repos/solnic/ossy/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/ossy/assignees{/user}", "git_url": "git://github.com/solnic/ossy.git", "forks_url": "https://api.github.com/repos/solnic/ossy/forks", "tags_url": "https://api.github.com/repos/solnic/ossy/tags", "open_issues": 1, "size": 161, "pushed_at": "2022-12-09T15:54:51Z", "issues_url": "https://api.github.com/repos/solnic/ossy/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 1, "git_tags_url": "https://api.github.com/repos/solnic/ossy/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/ossy/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/ossy/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/ossy/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/ossy/branches{/branch}", "description": "Maintenance automation helper as a CLI tool used by dry-rb and rom-rb", "subscribers_url": "https://api.github.com/repos/solnic/ossy/subscribers", "stargazers_count": 9, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/ossy/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/ossy/git/refs{/sha}", "ssh_url": "**************:solnic/ossy.git", "releases_url": "https://api.github.com/repos/solnic/ossy/releases{/id}", "is_template": false, "name": "ossy", "languages_url": "https://api.github.com/repos/solnic/ossy/languages", "open_issues_count": 1, "contents_url": "https://api.github.com/repos/solnic/ossy/contents/{+path}", "watchers": 9}, {"labels_url": "https://api.github.com/repos/solnic/phoenix-showdown/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/phoenix-showdown/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/phoenix-showdown/hooks", "id": 179311470, "teams_url": "https://api.github.com/repos/solnic/phoenix-showdown/teams", "full_name": "solnic/phoenix-showdown", "git_commits_url": "https://api.github.com/repos/solnic/phoenix-showdown/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/phoenix-showdown/downloads", "stargazers_url": "https://api.github.com/repos/solnic/phoenix-showdown/stargazers", "blobs_url": "https://api.github.com/repos/solnic/phoenix-showdown/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/phoenix-showdown/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxNzkzMTE0NzA=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/phoenix-showdown/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/phoenix-showdown/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/phoenix-showdown/git/trees{/sha}", "clone_url": "https://github.com/solnic/phoenix-showdown.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/phoenix-showdown/subscription", "url": "https://api.github.com/repos/solnic/phoenix-showdown", "statuses_url": "https://api.github.com/repos/solnic/phoenix-showdown/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/phoenix-showdown/milestones{/number}", "svn_url": "https://github.com/solnic/phoenix-showdown", "events_url": "https://api.github.com/repos/solnic/phoenix-showdown/events", "updated_at": "2019-04-03T14:49:28Z", "created_at": "2019-04-03T14:49:25Z", "html_url": "https://github.com/solnic/phoenix-showdown", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/phoenix-showdown/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON><PERSON><PERSON>", "contributors_url": "https://api.github.com/repos/solnic/phoenix-showdown/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/phoenix-showdown/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/phoenix-showdown/merges", "deployments_url": "https://api.github.com/repos/solnic/phoenix-showdown/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/phoenix-showdown/assignees{/user}", "git_url": "git://github.com/solnic/phoenix-showdown.git", "forks_url": "https://api.github.com/repos/solnic/phoenix-showdown/forks", "tags_url": "https://api.github.com/repos/solnic/phoenix-showdown/tags", "open_issues": 0, "size": 1274, "pushed_at": "2017-01-13T12:50:42Z", "issues_url": "https://api.github.com/repos/solnic/phoenix-showdown/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/phoenix-showdown/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/phoenix-showdown/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/phoenix-showdown/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/phoenix-showdown/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/phoenix-showdown/branches{/branch}", "description": ":horse_racing: benchmark Sinatra-like web frameworks", "subscribers_url": "https://api.github.com/repos/solnic/phoenix-showdown/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/phoenix-showdown/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/phoenix-showdown/git/refs{/sha}", "ssh_url": "**************:solnic/phoenix-showdown.git", "releases_url": "https://api.github.com/repos/solnic/phoenix-showdown/releases{/id}", "is_template": false, "name": "phoenix-showdown", "languages_url": "https://api.github.com/repos/solnic/phoenix-showdown/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/phoenix-showdown/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/hooks", "id": 320832224, "teams_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/teams", "full_name": "solnic/pronto-rubocop-action", "git_commits_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/downloads", "stargazers_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/stargazers", "blobs_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzMjA4MzIyMjQ=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/git/trees{/sha}", "clone_url": "https://github.com/solnic/pronto-rubocop-action.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/subscription", "url": "https://api.github.com/repos/solnic/pronto-rubocop-action", "statuses_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/milestones{/number}", "svn_url": "https://github.com/solnic/pronto-rubocop-action", "events_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/events", "updated_at": "2020-12-12T15:15:04Z", "created_at": "2020-12-12T13:03:03Z", "html_url": "https://github.com/solnic/pronto-rubocop-action", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "Shell", "contributors_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/merges", "deployments_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/assignees{/user}", "git_url": "git://github.com/solnic/pronto-rubocop-action.git", "forks_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/forks", "tags_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/tags", "open_issues": 0, "size": 17, "pushed_at": "2020-12-12T15:15:02Z", "issues_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/branches{/branch}", "description": "WIP", "subscribers_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/git/refs{/sha}", "ssh_url": "**************:solnic/pronto-rubocop-action.git", "releases_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/releases{/id}", "is_template": false, "name": "pronto-rubocop-action", "languages_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/pronto-rubocop-action/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/rodakase/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/rodakase/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/rodakase/hooks", "id": 166014576, "teams_url": "https://api.github.com/repos/solnic/rodakase/teams", "full_name": "solnic/rodakase", "git_commits_url": "https://api.github.com/repos/solnic/rodakase/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/rodakase/downloads", "stargazers_url": "https://api.github.com/repos/solnic/rodakase/stargazers", "blobs_url": "https://api.github.com/repos/solnic/rodakase/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/rodaka<PERSON>/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxNjYwMTQ1NzY=", "watchers_count": 2, "notifications_url": "https://api.github.com/repos/solnic/rodakase/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/rodakase/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/rodakase/git/trees{/sha}", "clone_url": "https://github.com/solnic/rodakase.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/rodakase/subscription", "url": "https://api.github.com/repos/solnic/rodakase", "statuses_url": "https://api.github.com/repos/solnic/rodakase/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/rodakase/milestones{/number}", "svn_url": "https://github.com/solnic/rodakase", "events_url": "https://api.github.com/repos/solnic/rodakase/events", "updated_at": "2023-01-28T19:15:48Z", "created_at": "2019-01-16T09:44:42Z", "html_url": "https://github.com/solnic/rodakase", "archived": true, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/rodakase/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/rodakase/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/rodakase/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/rodakase/merges", "deployments_url": "https://api.github.com/repos/solnic/rodakase/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/rodakase/assignees{/user}", "git_url": "git://github.com/solnic/rodakase.git", "forks_url": "https://api.github.com/repos/solnic/rodakase/forks", "tags_url": "https://api.github.com/repos/solnic/rodakase/tags", "open_issues": 0, "size": 139, "pushed_at": "2019-01-16T09:48:52Z", "issues_url": "https://api.github.com/repos/solnic/rodakase/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/rodakase/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/rodakase/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/rodakase/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/rodakase/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/rodakase/branches{/branch}", "description": "The project that gave birth to dry-(system|view|web)", "subscribers_url": "https://api.github.com/repos/solnic/rodakase/subscribers", "stargazers_count": 2, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/rodakase/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/rodakase/git/refs{/sha}", "ssh_url": "**************:solnic/rodakase.git", "releases_url": "https://api.github.com/repos/solnic/rodakase/releases{/id}", "is_template": false, "name": "<PERSON><PERSON><PERSON>", "languages_url": "https://api.github.com/repos/solnic/rodakase/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/rodakase/contents/{+path}", "watchers": 2}, {"labels_url": "https://api.github.com/repos/solnic/rom-rails-demo/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/rom-rails-demo/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/rom-rails-demo/hooks", "id": 16298687, "teams_url": "https://api.github.com/repos/solnic/rom-rails-demo/teams", "full_name": "solnic/rom-rails-demo", "git_commits_url": "https://api.github.com/repos/solnic/rom-rails-demo/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/rom-rails-demo/downloads", "stargazers_url": "https://api.github.com/repos/solnic/rom-rails-demo/stargazers", "blobs_url": "https://api.github.com/repos/solnic/rom-rails-demo/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/rom-rails-demo/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxNjI5ODY4Nw==", "watchers_count": 10, "notifications_url": "https://api.github.com/repos/solnic/rom-rails-demo/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/rom-rails-demo/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/rom-rails-demo/git/trees{/sha}", "clone_url": "https://github.com/solnic/rom-rails-demo.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/rom-rails-demo/subscription", "url": "https://api.github.com/repos/solnic/rom-rails-demo", "statuses_url": "https://api.github.com/repos/solnic/rom-rails-demo/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/rom-rails-demo/milestones{/number}", "svn_url": "https://github.com/solnic/rom-rails-demo", "events_url": "https://api.github.com/repos/solnic/rom-rails-demo/events", "updated_at": "2020-10-30T12:33:13Z", "created_at": "2014-01-28T01:18:38Z", "html_url": "https://github.com/solnic/rom-rails-demo", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/rom-rails-demo/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/rom-rails-demo/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/rom-rails-demo/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/rom-rails-demo/merges", "deployments_url": "https://api.github.com/repos/solnic/rom-rails-demo/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/rom-rails-demo/assignees{/user}", "git_url": "git://github.com/solnic/rom-rails-demo.git", "forks_url": "https://api.github.com/repos/solnic/rom-rails-demo/forks", "tags_url": "https://api.github.com/repos/solnic/rom-rails-demo/tags", "open_issues": 0, "size": 132, "pushed_at": "2014-01-28T01:21:15Z", "issues_url": "https://api.github.com/repos/solnic/rom-rails-demo/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/rom-rails-demo/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/rom-rails-demo/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/rom-rails-demo/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/rom-rails-demo/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/rom-rails-demo/branches{/branch}", "description": "Experimental rails app demo using ROM", "subscribers_url": "https://api.github.com/repos/solnic/rom-rails-demo/subscribers", "stargazers_count": 10, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/rom-rails-demo/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/rom-rails-demo/git/refs{/sha}", "ssh_url": "**************:solnic/rom-rails-demo.git", "releases_url": "https://api.github.com/repos/solnic/rom-rails-demo/releases{/id}", "is_template": false, "name": "rom-rails-demo", "languages_url": "https://api.github.com/repos/solnic/rom-rails-demo/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/rom-rails-demo/contents/{+path}", "watchers": 10}, {"labels_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/hooks", "id": 42480468, "teams_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/teams", "full_name": "solnic/rom-rails-skeleton", "git_commits_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/downloads", "stargazers_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/stargazers", "blobs_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk0MjQ4MDQ2OA==", "watchers_count": 35, "notifications_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/git/trees{/sha}", "clone_url": "https://github.com/solnic/rom-rails-skeleton.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/subscription", "url": "https://api.github.com/repos/solnic/rom-rails-skeleton", "statuses_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/milestones{/number}", "svn_url": "https://github.com/solnic/rom-rails-skeleton", "events_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/events", "updated_at": "2022-04-08T16:16:27Z", "created_at": "2015-09-14T22:24:14Z", "html_url": "https://github.com/solnic/rom-rails-skeleton", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/issues/events{/number}", "forks": 8, "merges_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/merges", "deployments_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/assignees{/user}", "git_url": "git://github.com/solnic/rom-rails-skeleton.git", "forks_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/forks", "tags_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/tags", "open_issues": 1, "size": 132, "pushed_at": "2016-04-30T00:45:35Z", "issues_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 8, "git_tags_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/branches{/branch}", "description": "My Rails app skeleton with ROM and other goodies", "subscribers_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/subscribers", "stargazers_count": 35, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/git/refs{/sha}", "ssh_url": "**************:solnic/rom-rails-skeleton.git", "releases_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/releases{/id}", "is_template": false, "name": "rom-rails-skeleton", "languages_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/languages", "open_issues_count": 1, "contents_url": "https://api.github.com/repos/solnic/rom-rails-skeleton/contents/{+path}", "watchers": 35}, {"labels_url": "https://api.github.com/repos/solnic/rom-relation/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/rom-relation/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/rom-relation/hooks", "id": 3426679, "teams_url": "https://api.github.com/repos/solnic/rom-relation/teams", "full_name": "solnic/rom-relation", "git_commits_url": "https://api.github.com/repos/solnic/rom-relation/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/rom-relation/downloads", "stargazers_url": "https://api.github.com/repos/solnic/rom-relation/stargazers", "blobs_url": "https://api.github.com/repos/solnic/rom-relation/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/rom-relation/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzNDI2Njc5", "watchers_count": 362, "notifications_url": "https://api.github.com/repos/solnic/rom-relation/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/rom-relation/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/rom-relation/git/trees{/sha}", "clone_url": "https://github.com/solnic/rom-relation.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/rom-relation/subscription", "url": "https://api.github.com/repos/solnic/rom-relation", "statuses_url": "https://api.github.com/repos/solnic/rom-relation/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/rom-relation/milestones{/number}", "svn_url": "https://github.com/solnic/rom-relation", "events_url": "https://api.github.com/repos/solnic/rom-relation/events", "updated_at": "2023-10-31T07:30:28Z", "created_at": "2012-02-13T02:53:49Z", "html_url": "https://github.com/solnic/rom-relation", "archived": true, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/rom-relation/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/rom-relation/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/rom-relation/issues/events{/number}", "forks": 19, "merges_url": "https://api.github.com/repos/solnic/rom-relation/merges", "deployments_url": "https://api.github.com/repos/solnic/rom-relation/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/rom-relation/assignees{/user}", "git_url": "git://github.com/solnic/rom-relation.git", "forks_url": "https://api.github.com/repos/solnic/rom-relation/forks", "tags_url": "https://api.github.com/repos/solnic/rom-relation/tags", "open_issues": 2, "size": 4355, "pushed_at": "2014-01-24T22:04:29Z", "issues_url": "https://api.github.com/repos/solnic/rom-relation/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 19, "git_tags_url": "https://api.github.com/repos/solnic/rom-relation/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/rom-relation/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/rom-relation/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/rom-relation/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/rom-relation/branches{/branch}", "description": "This is an old ROM prototype that's no longer developed.", "subscribers_url": "https://api.github.com/repos/solnic/rom-relation/subscribers", "stargazers_count": 362, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/rom-relation/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/rom-relation/git/refs{/sha}", "ssh_url": "**************:solnic/rom-relation.git", "releases_url": "https://api.github.com/repos/solnic/rom-relation/releases{/id}", "is_template": false, "name": "rom-relation", "languages_url": "https://api.github.com/repos/solnic/rom-relation/languages", "open_issues_count": 2, "contents_url": "https://api.github.com/repos/solnic/rom-relation/contents/{+path}", "watchers": 362}, {"labels_url": "https://api.github.com/repos/solnic/rom-session/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/rom-session/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/rom-session/hooks", "id": 3329277, "teams_url": "https://api.github.com/repos/solnic/rom-session/teams", "full_name": "solnic/rom-session", "git_commits_url": "https://api.github.com/repos/solnic/rom-session/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/rom-session/downloads", "stargazers_url": "https://api.github.com/repos/solnic/rom-session/stargazers", "blobs_url": "https://api.github.com/repos/solnic/rom-session/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/rom-session/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzMzI5Mjc3", "watchers_count": 33, "notifications_url": "https://api.github.com/repos/solnic/rom-session/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/rom-session/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/rom-session/git/trees{/sha}", "clone_url": "https://github.com/solnic/rom-session.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/rom-session/subscription", "url": "https://api.github.com/repos/solnic/rom-session", "statuses_url": "https://api.github.com/repos/solnic/rom-session/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/rom-session/milestones{/number}", "svn_url": "https://github.com/solnic/rom-session", "events_url": "https://api.github.com/repos/solnic/rom-session/events", "updated_at": "2022-11-28T16:11:24Z", "created_at": "2012-02-01T21:36:07Z", "html_url": "https://github.com/solnic/rom-session", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/rom-session/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/rom-session/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/rom-session/issues/events{/number}", "forks": 5, "merges_url": "https://api.github.com/repos/solnic/rom-session/merges", "deployments_url": "https://api.github.com/repos/solnic/rom-session/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/rom-session/assignees{/user}", "git_url": "git://github.com/solnic/rom-session.git", "forks_url": "https://api.github.com/repos/solnic/rom-session/forks", "tags_url": "https://api.github.com/repos/solnic/rom-session/tags", "open_issues": 1, "size": 1238, "pushed_at": "2014-01-24T22:04:46Z", "issues_url": "https://api.github.com/repos/solnic/rom-session/issues{/number}", "homepage": "http://rom-rb.org", "private": false, "disabled": false, "forks_count": 5, "git_tags_url": "https://api.github.com/repos/solnic/rom-session/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/rom-session/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/rom-session/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/rom-session/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/rom-session/branches{/branch}", "description": "DEPRECATED / DEAD / MOVE ON", "subscribers_url": "https://api.github.com/repos/solnic/rom-session/subscribers", "stargazers_count": 33, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/rom-session/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/rom-session/git/refs{/sha}", "ssh_url": "**************:solnic/rom-session.git", "releases_url": "https://api.github.com/repos/solnic/rom-session/releases{/id}", "is_template": false, "name": "rom-session", "languages_url": "https://api.github.com/repos/solnic/rom-session/languages", "open_issues_count": 1, "contents_url": "https://api.github.com/repos/solnic/rom-session/contents/{+path}", "watchers": 33}, {"labels_url": "https://api.github.com/repos/solnic/rom-workshop/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/rom-workshop/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/rom-workshop/hooks", "id": 186145488, "teams_url": "https://api.github.com/repos/solnic/rom-workshop/teams", "full_name": "solnic/rom-workshop", "git_commits_url": "https://api.github.com/repos/solnic/rom-workshop/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/rom-workshop/downloads", "stargazers_url": "https://api.github.com/repos/solnic/rom-workshop/stargazers", "blobs_url": "https://api.github.com/repos/solnic/rom-workshop/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/rom-workshop/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxODYxNDU0ODg=", "watchers_count": 5, "notifications_url": "https://api.github.com/repos/solnic/rom-workshop/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/rom-workshop/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/rom-workshop/git/trees{/sha}", "clone_url": "https://github.com/solnic/rom-workshop.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/rom-workshop/subscription", "url": "https://api.github.com/repos/solnic/rom-workshop", "statuses_url": "https://api.github.com/repos/solnic/rom-workshop/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/rom-workshop/milestones{/number}", "svn_url": "https://github.com/solnic/rom-workshop", "events_url": "https://api.github.com/repos/solnic/rom-workshop/events", "updated_at": "2020-09-18T08:22:28Z", "created_at": "2019-05-11T14:50:32Z", "html_url": "https://github.com/solnic/rom-workshop", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/rom-workshop/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/rom-workshop/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/rom-workshop/issues/events{/number}", "forks": 7, "merges_url": "https://api.github.com/repos/solnic/rom-workshop/merges", "deployments_url": "https://api.github.com/repos/solnic/rom-workshop/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/rom-workshop/assignees{/user}", "git_url": "git://github.com/solnic/rom-workshop.git", "forks_url": "https://api.github.com/repos/solnic/rom-workshop/forks", "tags_url": "https://api.github.com/repos/solnic/rom-workshop/tags", "open_issues": 0, "size": 28, "pushed_at": "2019-05-17T18:32:36Z", "issues_url": "https://api.github.com/repos/solnic/rom-workshop/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 7, "git_tags_url": "https://api.github.com/repos/solnic/rom-workshop/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/rom-workshop/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/rom-workshop/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/rom-workshop/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/rom-workshop/branches{/branch}", "description": null, "subscribers_url": "https://api.github.com/repos/solnic/rom-workshop/subscribers", "stargazers_count": 5, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/rom-workshop/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/rom-workshop/git/refs{/sha}", "ssh_url": "**************:solnic/rom-workshop.git", "releases_url": "https://api.github.com/repos/solnic/rom-workshop/releases{/id}", "is_template": false, "name": "rom-workshop", "languages_url": "https://api.github.com/repos/solnic/rom-workshop/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/rom-workshop/contents/{+path}", "watchers": 5}, {"labels_url": "https://api.github.com/repos/solnic/rom_factory/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/rom_factory/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/rom_factory/hooks", "id": 79927733, "teams_url": "https://api.github.com/repos/solnic/rom_factory/teams", "full_name": "solnic/rom_factory", "git_commits_url": "https://api.github.com/repos/solnic/rom_factory/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/rom_factory/downloads", "stargazers_url": "https://api.github.com/repos/solnic/rom_factory/stargazers", "blobs_url": "https://api.github.com/repos/solnic/rom_factory/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/rom_factory/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk3OTkyNzczMw==", "watchers_count": 1, "notifications_url": "https://api.github.com/repos/solnic/rom_factory/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/rom_factory/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/rom_factory/git/trees{/sha}", "clone_url": "https://github.com/solnic/rom_factory.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/rom_factory/subscription", "url": "https://api.github.com/repos/solnic/rom_factory", "statuses_url": "https://api.github.com/repos/solnic/rom_factory/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/rom_factory/milestones{/number}", "svn_url": "https://github.com/solnic/rom_factory", "events_url": "https://api.github.com/repos/solnic/rom_factory/events", "updated_at": "2017-01-24T16:10:05Z", "created_at": "2017-01-24T16:07:11Z", "html_url": "https://github.com/solnic/rom_factory", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/rom_factory/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/rom_factory/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/rom_factory/issues/events{/number}", "forks": 1, "merges_url": "https://api.github.com/repos/solnic/rom_factory/merges", "deployments_url": "https://api.github.com/repos/solnic/rom_factory/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/rom_factory/assignees{/user}", "git_url": "git://github.com/solnic/rom_factory.git", "forks_url": "https://api.github.com/repos/solnic/rom_factory/forks", "tags_url": "https://api.github.com/repos/solnic/rom_factory/tags", "open_issues": 0, "size": 30, "pushed_at": "2017-01-24T16:10:44Z", "issues_url": "https://api.github.com/repos/solnic/rom_factory/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 1, "git_tags_url": "https://api.github.com/repos/solnic/rom_factory/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/rom_factory/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/rom_factory/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/rom_factory/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/rom_factory/branches{/branch}", "description": null, "subscribers_url": "https://api.github.com/repos/solnic/rom_factory/subscribers", "stargazers_count": 1, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/rom_factory/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/rom_factory/git/refs{/sha}", "ssh_url": "**************:solnic/rom_factory.git", "releases_url": "https://api.github.com/repos/solnic/rom_factory/releases{/id}", "is_template": false, "name": "rom_factory", "languages_url": "https://api.github.com/repos/solnic/rom_factory/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/rom_factory/contents/{+path}", "watchers": 1}, {"labels_url": "https://api.github.com/repos/solnic/ruby-devel-containers/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/ruby-devel-containers/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/ruby-devel-containers/hooks", "id": 320861681, "teams_url": "https://api.github.com/repos/solnic/ruby-devel-containers/teams", "full_name": "solnic/ruby-devel-containers", "git_commits_url": "https://api.github.com/repos/solnic/ruby-devel-containers/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/ruby-devel-containers/downloads", "stargazers_url": "https://api.github.com/repos/solnic/ruby-devel-containers/stargazers", "blobs_url": "https://api.github.com/repos/solnic/ruby-devel-containers/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/ruby-devel-containers/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzMjA4NjE2ODE=", "watchers_count": 1, "notifications_url": "https://api.github.com/repos/solnic/ruby-devel-containers/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/ruby-devel-containers/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/ruby-devel-containers/git/trees{/sha}", "clone_url": "https://github.com/solnic/ruby-devel-containers.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/ruby-devel-containers/subscription", "url": "https://api.github.com/repos/solnic/ruby-devel-containers", "statuses_url": "https://api.github.com/repos/solnic/ruby-devel-containers/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/ruby-devel-containers/milestones{/number}", "svn_url": "https://github.com/solnic/ruby-devel-containers", "events_url": "https://api.github.com/repos/solnic/ruby-devel-containers/events", "updated_at": "2021-01-21T18:25:29Z", "created_at": "2020-12-12T15:28:36Z", "html_url": "https://github.com/solnic/ruby-devel-containers", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/ruby-devel-containers/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/ruby-devel-containers/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/ruby-devel-containers/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/ruby-devel-containers/merges", "deployments_url": "https://api.github.com/repos/solnic/ruby-devel-containers/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/ruby-devel-containers/assignees{/user}", "git_url": "git://github.com/solnic/ruby-devel-containers.git", "forks_url": "https://api.github.com/repos/solnic/ruby-devel-containers/forks", "tags_url": "https://api.github.com/repos/solnic/ruby-devel-containers/tags", "open_issues": 0, "size": 10, "pushed_at": "2020-12-12T15:28:46Z", "issues_url": "https://api.github.com/repos/solnic/ruby-devel-containers/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/ruby-devel-containers/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/ruby-devel-containers/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/ruby-devel-containers/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/ruby-devel-containers/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/ruby-devel-containers/branches{/branch}", "description": "WIP", "subscribers_url": "https://api.github.com/repos/solnic/ruby-devel-containers/subscribers", "stargazers_count": 1, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/ruby-devel-containers/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/ruby-devel-containers/git/refs{/sha}", "ssh_url": "**************:solnic/ruby-devel-containers.git", "releases_url": "https://api.github.com/repos/solnic/ruby-devel-containers/releases{/id}", "is_template": false, "name": "ruby-devel-containers", "languages_url": "https://api.github.com/repos/solnic/ruby-devel-containers/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/ruby-devel-containers/contents/{+path}", "watchers": 1}, {"labels_url": "https://api.github.com/repos/solnic/sequel/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/sequel/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/sequel/hooks", "id": 97934988, "teams_url": "https://api.github.com/repos/solnic/sequel/teams", "full_name": "solnic/sequel", "git_commits_url": "https://api.github.com/repos/solnic/sequel/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/sequel/downloads", "stargazers_url": "https://api.github.com/repos/solnic/sequel/stargazers", "blobs_url": "https://api.github.com/repos/solnic/sequel/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/sequel/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk5NzkzNDk4OA==", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/sequel/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/sequel/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/sequel/git/trees{/sha}", "clone_url": "https://github.com/solnic/sequel.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/sequel/subscription", "url": "https://api.github.com/repos/solnic/sequel", "statuses_url": "https://api.github.com/repos/solnic/sequel/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/sequel/milestones{/number}", "svn_url": "https://github.com/solnic/sequel", "events_url": "https://api.github.com/repos/solnic/sequel/events", "updated_at": "2017-07-21T10:14:30Z", "created_at": "2017-07-21T10:14:26Z", "html_url": "https://github.com/solnic/sequel", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/sequel/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/sequel/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/sequel/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/sequel/merges", "deployments_url": "https://api.github.com/repos/solnic/sequel/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/sequel/assignees{/user}", "git_url": "git://github.com/solnic/sequel.git", "forks_url": "https://api.github.com/repos/solnic/sequel/forks", "tags_url": "https://api.github.com/repos/solnic/sequel/tags", "open_issues": 1, "size": 36390, "pushed_at": "2017-07-21T10:16:18Z", "issues_url": "https://api.github.com/repos/solnic/sequel/issues{/number}", "homepage": "http://sequel.jeremyevans.net", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/sequel/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/sequel/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/sequel/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/sequel/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/sequel/branches{/branch}", "description": "Sequel: The Database Toolkit for Ruby", "subscribers_url": "https://api.github.com/repos/solnic/sequel/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "other", "name": "Other", "node_id": "MDc6TGljZW5zZTA=", "spdx_id": "NOASSERTION", "url": null}, "commits_url": "https://api.github.com/repos/solnic/sequel/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/sequel/git/refs{/sha}", "ssh_url": "**************:solnic/sequel.git", "releases_url": "https://api.github.com/repos/solnic/sequel/releases{/id}", "is_template": false, "name": "sequel", "languages_url": "https://api.github.com/repos/solnic/sequel/languages", "open_issues_count": 1, "contents_url": "https://api.github.com/repos/solnic/sequel/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/solnic/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/solnic/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/solnic/hooks", "id": 318864928, "teams_url": "https://api.github.com/repos/solnic/solnic/teams", "full_name": "solnic/solnic", "git_commits_url": "https://api.github.com/repos/solnic/solnic/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/solnic/downloads", "stargazers_url": "https://api.github.com/repos/solnic/solnic/stargazers", "blobs_url": "https://api.github.com/repos/solnic/solnic/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/solnic/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzMTg4NjQ5Mjg=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/solnic/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/solnic/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/solnic/git/trees{/sha}", "clone_url": "https://github.com/solnic/solnic.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/solnic/subscription", "url": "https://api.github.com/repos/solnic/solnic", "statuses_url": "https://api.github.com/repos/solnic/solnic/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/solnic/milestones{/number}", "svn_url": "https://github.com/solnic/solnic", "events_url": "https://api.github.com/repos/solnic/solnic/events", "updated_at": "2022-01-02T09:34:19Z", "created_at": "2020-12-05T18:51:15Z", "html_url": "https://github.com/solnic/solnic", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/solnic/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": ["dry-rb", "hanami", "hugo-blog", "hugo-site", "open-source", "personal-site", "rom-rb", "ruby"], "language": "HTML", "contributors_url": "https://api.github.com/repos/solnic/solnic/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/solnic/issues/events{/number}", "forks": 3, "merges_url": "https://api.github.com/repos/solnic/solnic/merges", "deployments_url": "https://api.github.com/repos/solnic/solnic/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/solnic/assignees{/user}", "git_url": "git://github.com/solnic/solnic.git", "forks_url": "https://api.github.com/repos/solnic/solnic/forks", "tags_url": "https://api.github.com/repos/solnic/solnic/tags", "open_issues": 0, "size": 2054, "pushed_at": "2023-11-21T11:34:23Z", "issues_url": "https://api.github.com/repos/solnic/solnic/issues{/number}", "homepage": "https://solnic.codes", "private": false, "disabled": false, "forks_count": 3, "git_tags_url": "https://api.github.com/repos/solnic/solnic/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/solnic/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/solnic/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/solnic/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/solnic/branches{/branch}", "description": "solnic.codes website + my GitHub README.md in one repo", "subscribers_url": "https://api.github.com/repos/solnic/solnic/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/solnic/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/solnic/git/refs{/sha}", "ssh_url": "**************:solnic/solnic.git", "releases_url": "https://api.github.com/repos/solnic/solnic/releases{/id}", "is_template": false, "name": "solnic", "languages_url": "https://api.github.com/repos/solnic/solnic/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/solnic/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/spark/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/spark/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/spark/hooks", "id": 58131882, "teams_url": "https://api.github.com/repos/solnic/spark/teams", "full_name": "solnic/spark", "git_commits_url": "https://api.github.com/repos/solnic/spark/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/spark/downloads", "stargazers_url": "https://api.github.com/repos/solnic/spark/stargazers", "blobs_url": "https://api.github.com/repos/solnic/spark/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/spark/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk1ODEzMTg4Mg==", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/spark/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/spark/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/spark/git/trees{/sha}", "clone_url": "https://github.com/solnic/spark.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/spark/subscription", "url": "https://api.github.com/repos/solnic/spark", "statuses_url": "https://api.github.com/repos/solnic/spark/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/spark/milestones{/number}", "svn_url": "https://github.com/solnic/spark", "events_url": "https://api.github.com/repos/solnic/spark/events", "updated_at": "2016-05-05T13:14:56Z", "created_at": "2016-05-05T13:14:34Z", "html_url": "https://github.com/solnic/spark", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/spark/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": "Scala", "contributors_url": "https://api.github.com/repos/solnic/spark/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/spark/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/spark/merges", "deployments_url": "https://api.github.com/repos/solnic/spark/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/spark/assignees{/user}", "git_url": "git://github.com/solnic/spark.git", "forks_url": "https://api.github.com/repos/solnic/spark/forks", "tags_url": "https://api.github.com/repos/solnic/spark/tags", "open_issues": 0, "size": 170825, "pushed_at": "2016-05-05T13:25:02Z", "issues_url": "https://api.github.com/repos/solnic/spark/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/spark/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/spark/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/spark/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/spark/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/spark/branches{/branch}", "description": "Mirror of Apache Spark", "subscribers_url": "https://api.github.com/repos/solnic/spark/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "apache-2.0", "name": "Apache License 2.0", "node_id": "MDc6TGljZW5zZTI=", "spdx_id": "Apache-2.0", "url": "https://api.github.com/licenses/apache-2.0"}, "commits_url": "https://api.github.com/repos/solnic/spark/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/spark/git/refs{/sha}", "ssh_url": "**************:solnic/spark.git", "releases_url": "https://api.github.com/repos/solnic/spark/releases{/id}", "is_template": false, "name": "spark", "languages_url": "https://api.github.com/repos/solnic/spark/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/spark/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/sql/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/sql/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/sql/hooks", "id": 19219467, "teams_url": "https://api.github.com/repos/solnic/sql/teams", "full_name": "solnic/sql", "git_commits_url": "https://api.github.com/repos/solnic/sql/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/sql/downloads", "stargazers_url": "https://api.github.com/repos/solnic/sql/stargazers", "blobs_url": "https://api.github.com/repos/solnic/sql/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/sql/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxOTIxOTQ2Nw==", "watchers_count": 1, "notifications_url": "https://api.github.com/repos/solnic/sql/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/sql/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/sql/git/trees{/sha}", "clone_url": "https://github.com/solnic/sql.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/sql/subscription", "url": "https://api.github.com/repos/solnic/sql", "statuses_url": "https://api.github.com/repos/solnic/sql/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/sql/milestones{/number}", "svn_url": "https://github.com/solnic/sql", "events_url": "https://api.github.com/repos/solnic/sql/events", "updated_at": "2015-12-05T15:31:01Z", "created_at": "2014-04-27T22:15:24Z", "html_url": "https://github.com/solnic/sql", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/sql/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/sql/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/sql/issues/events{/number}", "forks": 1, "merges_url": "https://api.github.com/repos/solnic/sql/merges", "deployments_url": "https://api.github.com/repos/solnic/sql/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/sql/assignees{/user}", "git_url": "git://github.com/solnic/sql.git", "forks_url": "https://api.github.com/repos/solnic/sql/forks", "tags_url": "https://api.github.com/repos/solnic/sql/tags", "open_issues": 0, "size": 471, "pushed_at": "2014-05-16T10:59:08Z", "issues_url": "https://api.github.com/repos/solnic/sql/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 1, "git_tags_url": "https://api.github.com/repos/solnic/sql/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/sql/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/sql/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/sql/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/sql/branches{/branch}", "description": "SQL Parser and Generator", "subscribers_url": "https://api.github.com/repos/solnic/sql/subscribers", "stargazers_count": 1, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/sql/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/sql/git/refs{/sha}", "ssh_url": "**************:solnic/sql.git", "releases_url": "https://api.github.com/repos/solnic/sql/releases{/id}", "is_template": false, "name": "sql", "languages_url": "https://api.github.com/repos/solnic/sql/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/sql/contents/{+path}", "watchers": 1}, {"labels_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/hooks", "id": 371279262, "teams_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/teams", "full_name": "solnic/stop_active_support_anywhere", "git_commits_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/downloads", "stargazers_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/stargazers", "blobs_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzNzEyNzkyNjI=", "watchers_count": 1, "notifications_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/git/trees{/sha}", "clone_url": "https://github.com/solnic/stop_active_support_anywhere.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/subscription", "url": "https://api.github.com/repos/solnic/stop_active_support_anywhere", "statuses_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/milestones{/number}", "svn_url": "https://github.com/solnic/stop_active_support_anywhere", "events_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/events", "updated_at": "2021-05-27T07:34:46Z", "created_at": "2021-05-27T07:08:43Z", "html_url": "https://github.com/solnic/stop_active_support_anywhere", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/merges", "deployments_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/assignees{/user}", "git_url": "git://github.com/solnic/stop_active_support_anywhere.git", "forks_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/forks", "tags_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/tags", "open_issues": 0, "size": 1013, "pushed_at": "2021-05-27T07:11:01Z", "issues_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/branches{/branch}", "description": "Stop use Active Support in not rails related projects", "subscribers_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/subscribers", "stargazers_count": 1, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/git/refs{/sha}", "ssh_url": "**************:solnic/stop_active_support_anywhere.git", "releases_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/releases{/id}", "is_template": false, "name": "stop_active_support_anywhere", "languages_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/stop_active_support_anywhere/contents/{+path}", "watchers": 1}, {"labels_url": "https://api.github.com/repos/solnic/surveyor-rom/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/surveyor-rom/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/surveyor-rom/hooks", "id": 113976910, "teams_url": "https://api.github.com/repos/solnic/surveyor-rom/teams", "full_name": "solnic/surveyor-rom", "git_commits_url": "https://api.github.com/repos/solnic/surveyor-rom/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/surveyor-rom/downloads", "stargazers_url": "https://api.github.com/repos/solnic/surveyor-rom/stargazers", "blobs_url": "https://api.github.com/repos/solnic/surveyor-rom/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/surveyor-rom/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxMTM5NzY5MTA=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/surveyor-rom/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/surveyor-rom/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/surveyor-rom/git/trees{/sha}", "clone_url": "https://github.com/solnic/surveyor-rom.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/surveyor-rom/subscription", "url": "https://api.github.com/repos/solnic/surveyor-rom", "statuses_url": "https://api.github.com/repos/solnic/surveyor-rom/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/surveyor-rom/milestones{/number}", "svn_url": "https://github.com/solnic/surveyor-rom", "events_url": "https://api.github.com/repos/solnic/surveyor-rom/events", "updated_at": "2017-12-12T10:31:32Z", "created_at": "2017-12-12T10:31:30Z", "html_url": "https://github.com/solnic/surveyor-rom", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/surveyor-rom/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/surveyor-rom/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/surveyor-rom/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/surveyor-rom/merges", "deployments_url": "https://api.github.com/repos/solnic/surveyor-rom/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/surveyor-rom/assignees{/user}", "git_url": "git://github.com/solnic/surveyor-rom.git", "forks_url": "https://api.github.com/repos/solnic/surveyor-rom/forks", "tags_url": "https://api.github.com/repos/solnic/surveyor-rom/tags", "open_issues": 0, "size": 12, "pushed_at": "2017-12-12T10:32:16Z", "issues_url": "https://api.github.com/repos/solnic/surveyor-rom/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/surveyor-rom/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/surveyor-rom/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/surveyor-rom/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/surveyor-rom/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/surveyor-rom/branches{/branch}", "description": null, "subscribers_url": "https://api.github.com/repos/solnic/surveyor-rom/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/surveyor-rom/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/surveyor-rom/git/refs{/sha}", "ssh_url": "**************:solnic/surveyor-rom.git", "releases_url": "https://api.github.com/repos/solnic/surveyor-rom/releases{/id}", "is_template": false, "name": "surveyor-rom", "languages_url": "https://api.github.com/repos/solnic/surveyor-rom/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/surveyor-rom/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/talks/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/talks/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/talks/hooks", "id": 36932530, "teams_url": "https://api.github.com/repos/solnic/talks/teams", "full_name": "solnic/talks", "git_commits_url": "https://api.github.com/repos/solnic/talks/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/talks/downloads", "stargazers_url": "https://api.github.com/repos/solnic/talks/stargazers", "blobs_url": "https://api.github.com/repos/solnic/talks/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/talks/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzNjkzMjUzMA==", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/talks/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/talks/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/talks/git/trees{/sha}", "clone_url": "https://github.com/solnic/talks.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/talks/subscription", "url": "https://api.github.com/repos/solnic/talks", "statuses_url": "https://api.github.com/repos/solnic/talks/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/talks/milestones{/number}", "svn_url": "https://github.com/solnic/talks", "events_url": "https://api.github.com/repos/solnic/talks/events", "updated_at": "2021-04-08T08:08:47Z", "created_at": "2015-06-05T12:52:33Z", "html_url": "https://github.com/solnic/talks", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/talks/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/talks/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/talks/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/talks/merges", "deployments_url": "https://api.github.com/repos/solnic/talks/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/talks/assignees{/user}", "git_url": "git://github.com/solnic/talks.git", "forks_url": "https://api.github.com/repos/solnic/talks/forks", "tags_url": "https://api.github.com/repos/solnic/talks/tags", "open_issues": 0, "size": 104, "pushed_at": "2015-06-05T12:53:43Z", "issues_url": "https://api.github.com/repos/solnic/talks/issues{/number}", "homepage": null, "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/talks/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/talks/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/talks/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/talks/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/talks/branches{/branch}", "description": "Just stuff for my talks", "subscribers_url": "https://api.github.com/repos/solnic/talks/subscribers", "stargazers_count": 0, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/talks/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/talks/git/refs{/sha}", "ssh_url": "**************:solnic/talks.git", "releases_url": "https://api.github.com/repos/solnic/talks/releases{/id}", "is_template": false, "name": "talks", "languages_url": "https://api.github.com/repos/solnic/talks/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/talks/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/testing/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/testing/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/testing/hooks", "id": 357514178, "teams_url": "https://api.github.com/repos/solnic/testing/teams", "full_name": "solnic/testing", "git_commits_url": "https://api.github.com/repos/solnic/testing/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/testing/downloads", "stargazers_url": "https://api.github.com/repos/solnic/testing/stargazers", "blobs_url": "https://api.github.com/repos/solnic/testing/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/testing/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzNTc1MTQxNzg=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/testing/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/testing/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/testing/git/trees{/sha}", "clone_url": "https://github.com/solnic/testing.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/testing/subscription", "url": "https://api.github.com/repos/solnic/testing", "statuses_url": "https://api.github.com/repos/solnic/testing/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/testing/milestones{/number}", "svn_url": "https://github.com/solnic/testing", "events_url": "https://api.github.com/repos/solnic/testing/events", "updated_at": "2021-06-11T12:05:32Z", "created_at": "2021-04-13T10:33:10Z", "html_url": "https://github.com/solnic/testing", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/testing/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/testing/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/testing/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/testing/merges", "deployments_url": "https://api.github.com/repos/solnic/testing/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/testing/assignees{/user}", "git_url": "git://github.com/solnic/testing.git", "forks_url": "https://api.github.com/repos/solnic/testing/forks", "tags_url": "https://api.github.com/repos/solnic/testing/tags", "open_issues": 0, "size": 262, "pushed_at": "2021-06-11T12:05:30Z", "issues_url": "https://api.github.com/repos/solnic/testing/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/testing/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/testing/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/testing/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/testing/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/testing/branches{/branch}", "description": "Test repository. Nothing to see here, move along.", "subscribers_url": "https://api.github.com/repos/solnic/testing/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/testing/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/testing/git/refs{/sha}", "ssh_url": "**************:solnic/testing.git", "releases_url": "https://api.github.com/repos/solnic/testing/releases{/id}", "is_template": false, "name": "testing", "languages_url": "https://api.github.com/repos/solnic/testing/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/testing/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/trailblazer/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/trailblazer/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/trailblazer/hooks", "id": 74835621, "teams_url": "https://api.github.com/repos/solnic/trailblazer/teams", "full_name": "solnic/trailblazer", "git_commits_url": "https://api.github.com/repos/solnic/trailblazer/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/trailblazer/downloads", "stargazers_url": "https://api.github.com/repos/solnic/trailblazer/stargazers", "blobs_url": "https://api.github.com/repos/solnic/trailblazer/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/trailblazer/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk3NDgzNTYyMQ==", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/trailblazer/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/trailblazer/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/trailblazer/git/trees{/sha}", "clone_url": "https://github.com/solnic/trailblazer.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/trailblazer/subscription", "url": "https://api.github.com/repos/solnic/trailblazer", "statuses_url": "https://api.github.com/repos/solnic/trailblazer/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/trailblazer/milestones{/number}", "svn_url": "https://github.com/solnic/trailblazer", "events_url": "https://api.github.com/repos/solnic/trailblazer/events", "updated_at": "2016-11-26T16:02:09Z", "created_at": "2016-11-26T16:02:07Z", "html_url": "https://github.com/solnic/trailblazer", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/trailblazer/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/trailblazer/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/trailblazer/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/trailblazer/merges", "deployments_url": "https://api.github.com/repos/solnic/trailblazer/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/trailblazer/assignees{/user}", "git_url": "git://github.com/solnic/trailblazer.git", "forks_url": "https://api.github.com/repos/solnic/trailblazer/forks", "tags_url": "https://api.github.com/repos/solnic/trailblazer/tags", "open_issues": 0, "size": 1074, "pushed_at": "2016-11-26T14:18:51Z", "issues_url": "https://api.github.com/repos/solnic/trailblazer/issues{/number}", "homepage": "http://trailblazer.to", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/trailblazer/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/trailblazer/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/trailblazer/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/trailblazer/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/trailblazer/branches{/branch}", "description": "A high-level architecture for the web.", "subscribers_url": "https://api.github.com/repos/solnic/trailblazer/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "other", "name": "Other", "node_id": "MDc6TGljZW5zZTA=", "spdx_id": "NOASSERTION", "url": null}, "commits_url": "https://api.github.com/repos/solnic/trailblazer/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/trailblazer/git/refs{/sha}", "ssh_url": "**************:solnic/trailblazer.git", "releases_url": "https://api.github.com/repos/solnic/trailblazer/releases{/id}", "is_template": false, "name": "trailblazer", "languages_url": "https://api.github.com/repos/solnic/trailblazer/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/trailblazer/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/transflow/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/transflow/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/transflow/hooks", "id": 40811565, "teams_url": "https://api.github.com/repos/solnic/transflow/teams", "full_name": "solnic/transflow", "git_commits_url": "https://api.github.com/repos/solnic/transflow/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/transflow/downloads", "stargazers_url": "https://api.github.com/repos/solnic/transflow/stargazers", "blobs_url": "https://api.github.com/repos/solnic/transflow/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/transflow/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnk0MDgxMTU2NQ==", "watchers_count": 101, "notifications_url": "https://api.github.com/repos/solnic/transflow/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/transflow/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/transflow/git/trees{/sha}", "clone_url": "https://github.com/solnic/transflow.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/transflow/subscription", "url": "https://api.github.com/repos/solnic/transflow", "statuses_url": "https://api.github.com/repos/solnic/transflow/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/transflow/milestones{/number}", "svn_url": "https://github.com/solnic/transflow", "events_url": "https://api.github.com/repos/solnic/transflow/events", "updated_at": "2022-04-02T10:37:00Z", "created_at": "2015-08-16T10:54:24Z", "html_url": "https://github.com/solnic/transflow", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/transflow/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/transflow/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/transflow/issues/events{/number}", "forks": 2, "merges_url": "https://api.github.com/repos/solnic/transflow/merges", "deployments_url": "https://api.github.com/repos/solnic/transflow/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/transflow/assignees{/user}", "git_url": "git://github.com/solnic/transflow.git", "forks_url": "https://api.github.com/repos/solnic/transflow/forks", "tags_url": "https://api.github.com/repos/solnic/transflow/tags", "open_issues": 4, "size": 53, "pushed_at": "2016-03-28T13:32:45Z", "issues_url": "https://api.github.com/repos/solnic/transflow/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 2, "git_tags_url": "https://api.github.com/repos/solnic/transflow/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/transflow/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/transflow/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/transflow/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/transflow/branches{/branch}", "description": "[DISCONTINUED] Business transaction flow DSL", "subscribers_url": "https://api.github.com/repos/solnic/transflow/subscribers", "stargazers_count": 101, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/transflow/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/transflow/git/refs{/sha}", "ssh_url": "**************:solnic/transflow.git", "releases_url": "https://api.github.com/repos/solnic/transflow/releases{/id}", "is_template": false, "name": "transflow", "languages_url": "https://api.github.com/repos/solnic/transflow/languages", "open_issues_count": 4, "contents_url": "https://api.github.com/repos/solnic/transflow/contents/{+path}", "watchers": 101}, {"labels_url": "https://api.github.com/repos/solnic/transproc/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/transproc/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/transproc/hooks", "id": 28452477, "teams_url": "https://api.github.com/repos/solnic/transproc/teams", "full_name": "solnic/transproc", "git_commits_url": "https://api.github.com/repos/solnic/transproc/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/transproc/downloads", "stargazers_url": "https://api.github.com/repos/solnic/transproc/stargazers", "blobs_url": "https://api.github.com/repos/solnic/transproc/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/transproc/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkyODQ1MjQ3Nw==", "watchers_count": 410, "notifications_url": "https://api.github.com/repos/solnic/transproc/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/transproc/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/transproc/git/trees{/sha}", "clone_url": "https://github.com/solnic/transproc.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/transproc/subscription", "url": "https://api.github.com/repos/solnic/transproc", "statuses_url": "https://api.github.com/repos/solnic/transproc/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/transproc/milestones{/number}", "svn_url": "https://github.com/solnic/transproc", "events_url": "https://api.github.com/repos/solnic/transproc/events", "updated_at": "2024-05-11T09:29:30Z", "created_at": "2014-12-24T15:15:27Z", "html_url": "https://github.com/solnic/transproc", "archived": true, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/transproc/pulls{/number}", "mirror_url": null, "has_projects": false, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/transproc/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/transproc/issues/events{/number}", "forks": 29, "merges_url": "https://api.github.com/repos/solnic/transproc/merges", "deployments_url": "https://api.github.com/repos/solnic/transproc/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/transproc/assignees{/user}", "git_url": "git://github.com/solnic/transproc.git", "forks_url": "https://api.github.com/repos/solnic/transproc/forks", "tags_url": "https://api.github.com/repos/solnic/transproc/tags", "open_issues": 0, "size": 456, "pushed_at": "2019-12-28T12:29:22Z", "issues_url": "https://api.github.com/repos/solnic/transproc/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 29, "git_tags_url": "https://api.github.com/repos/solnic/transproc/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/transproc/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/transproc/comments{/number}", "has_pages": true, "issue_comment_url": "https://api.github.com/repos/solnic/transproc/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/transproc/branches{/branch}", "description": "The project was ported to dry-rb/dry-transformer", "subscribers_url": "https://api.github.com/repos/solnic/transproc/subscribers", "stargazers_count": 410, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/transproc/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/transproc/git/refs{/sha}", "ssh_url": "**************:solnic/transproc.git", "releases_url": "https://api.github.com/repos/solnic/transproc/releases{/id}", "is_template": false, "name": "transproc", "languages_url": "https://api.github.com/repos/solnic/transproc/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/transproc/contents/{+path}", "watchers": 410}, {"labels_url": "https://api.github.com/repos/solnic/virtus/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/virtus/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/virtus/hooks", "id": 1559931, "teams_url": "https://api.github.com/repos/solnic/virtus/teams", "full_name": "solnic/virtus", "git_commits_url": "https://api.github.com/repos/solnic/virtus/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/virtus/downloads", "stargazers_url": "https://api.github.com/repos/solnic/virtus/stargazers", "blobs_url": "https://api.github.com/repos/solnic/virtus/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/virtus/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxNTU5OTMx", "watchers_count": 3773, "notifications_url": "https://api.github.com/repos/solnic/virtus/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/virtus/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/virtus/git/trees{/sha}", "clone_url": "https://github.com/solnic/virtus.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/virtus/subscription", "url": "https://api.github.com/repos/solnic/virtus", "statuses_url": "https://api.github.com/repos/solnic/virtus/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/virtus/milestones{/number}", "svn_url": "https://github.com/solnic/virtus", "events_url": "https://api.github.com/repos/solnic/virtus/events", "updated_at": "2024-05-25T21:19:04Z", "created_at": "2011-04-02T16:23:50Z", "html_url": "https://github.com/solnic/virtus", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/virtus/pulls{/number}", "mirror_url": null, "has_projects": false, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/virtus/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/virtus/issues/events{/number}", "forks": 228, "merges_url": "https://api.github.com/repos/solnic/virtus/merges", "deployments_url": "https://api.github.com/repos/solnic/virtus/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/virtus/assignees{/user}", "git_url": "git://github.com/solnic/virtus.git", "forks_url": "https://api.github.com/repos/solnic/virtus/forks", "tags_url": "https://api.github.com/repos/solnic/virtus/tags", "open_issues": 71, "size": 1853, "pushed_at": "2021-08-10T13:39:34Z", "issues_url": "https://api.github.com/repos/solnic/virtus/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 228, "git_tags_url": "https://api.github.com/repos/solnic/virtus/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/virtus/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/virtus/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/virtus/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/virtus/branches{/branch}", "description": "[DISCONTINUED ] Attributes on Steroids for Plain Old Ruby Objects", "subscribers_url": "https://api.github.com/repos/solnic/virtus/subscribers", "stargazers_count": 3773, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/virtus/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/virtus/git/refs{/sha}", "ssh_url": "**************:solnic/virtus.git", "releases_url": "https://api.github.com/repos/solnic/virtus/releases{/id}", "is_template": false, "name": "virtus", "languages_url": "https://api.github.com/repos/solnic/virtus/languages", "open_issues_count": 71, "contents_url": "https://api.github.com/repos/solnic/virtus/contents/{+path}", "watchers": 3773}, {"labels_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/hooks", "id": 1846654, "teams_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/teams", "full_name": "solnic/virtus-dirty_tracking", "git_commits_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/downloads", "stargazers_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/stargazers", "blobs_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxODQ2NjU0", "watchers_count": 6, "notifications_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/git/trees{/sha}", "clone_url": "https://github.com/solnic/virtus-dirty_tracking.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/subscription", "url": "https://api.github.com/repos/solnic/virtus-dirty_tracking", "statuses_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/milestones{/number}", "svn_url": "https://github.com/solnic/virtus-dirty_tracking", "events_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/events", "updated_at": "2022-09-17T10:56:13Z", "created_at": "2011-06-04T12:30:16Z", "html_url": "https://github.com/solnic/virtus-dirty_tracking", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/issues/events{/number}", "forks": 19, "merges_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/merges", "deployments_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/assignees{/user}", "git_url": "git://github.com/solnic/virtus-dirty_tracking.git", "forks_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/forks", "tags_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/tags", "open_issues": 1, "size": 133, "pushed_at": "2011-10-13T14:50:36Z", "issues_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/issues{/number}", "homepage": "https://github.com/solnic/virtus-dirty_tracking", "private": false, "disabled": false, "forks_count": 19, "git_tags_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/branches{/branch}", "description": "ABANDONED - MOVE ON", "subscribers_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/subscribers", "stargazers_count": 6, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/git/refs{/sha}", "ssh_url": "**************:solnic/virtus-dirty_tracking.git", "releases_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/releases{/id}", "is_template": false, "name": "virtus-dirty_tracking", "languages_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/languages", "open_issues_count": 1, "contents_url": "https://api.github.com/repos/solnic/virtus-dirty_tracking/contents/{+path}", "watchers": 6}, {"labels_url": "https://api.github.com/repos/solnic/virtus-units/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/virtus-units/keys{/key_id}", "fork": false, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/virtus-units/hooks", "id": 2017562, "teams_url": "https://api.github.com/repos/solnic/virtus-units/teams", "full_name": "solnic/virtus-units", "git_commits_url": "https://api.github.com/repos/solnic/virtus-units/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/virtus-units/downloads", "stargazers_url": "https://api.github.com/repos/solnic/virtus-units/stargazers", "blobs_url": "https://api.github.com/repos/solnic/virtus-units/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/virtus-units/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkyMDE3NTYy", "watchers_count": 1, "notifications_url": "https://api.github.com/repos/solnic/virtus-units/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/virtus-units/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/virtus-units/git/trees{/sha}", "clone_url": "https://github.com/solnic/virtus-units.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/virtus-units/subscription", "url": "https://api.github.com/repos/solnic/virtus-units", "statuses_url": "https://api.github.com/repos/solnic/virtus-units/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/virtus-units/milestones{/number}", "svn_url": "https://github.com/solnic/virtus-units", "events_url": "https://api.github.com/repos/solnic/virtus-units/events", "updated_at": "2017-07-12T09:30:11Z", "created_at": "2011-07-08T11:46:38Z", "html_url": "https://github.com/solnic/virtus-units", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/virtus-units/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/virtus-units/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/virtus-units/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/virtus-units/merges", "deployments_url": "https://api.github.com/repos/solnic/virtus-units/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/virtus-units/assignees{/user}", "git_url": "git://github.com/solnic/virtus-units.git", "forks_url": "https://api.github.com/repos/solnic/virtus-units/forks", "tags_url": "https://api.github.com/repos/solnic/virtus-units/tags", "open_issues": 0, "size": 96, "pushed_at": "2011-07-08T12:00:19Z", "issues_url": "https://api.github.com/repos/solnic/virtus-units/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/virtus-units/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/virtus-units/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/virtus-units/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/virtus-units/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/virtus-units/branches{/branch}", "description": "ABANDONED - MOVE ON", "subscribers_url": "https://api.github.com/repos/solnic/virtus-units/subscribers", "stargazers_count": 1, "has_discussions": false, "license": null, "commits_url": "https://api.github.com/repos/solnic/virtus-units/commits{/sha}", "has_issues": true, "git_refs_url": "https://api.github.com/repos/solnic/virtus-units/git/refs{/sha}", "ssh_url": "**************:solnic/virtus-units.git", "releases_url": "https://api.github.com/repos/solnic/virtus-units/releases{/id}", "is_template": false, "name": "virtus-units", "languages_url": "https://api.github.com/repos/solnic/virtus-units/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/virtus-units/contents/{+path}", "watchers": 1}, {"labels_url": "https://api.github.com/repos/solnic/waterdrop/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/waterdrop/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/waterdrop/hooks", "id": 184903239, "teams_url": "https://api.github.com/repos/solnic/waterdrop/teams", "full_name": "solnic/waterdrop", "git_commits_url": "https://api.github.com/repos/solnic/waterdrop/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/waterdrop/downloads", "stargazers_url": "https://api.github.com/repos/solnic/waterdrop/stargazers", "blobs_url": "https://api.github.com/repos/solnic/waterdrop/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/waterdrop/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxODQ5MDMyMzk=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/waterdrop/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/waterdrop/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/waterdrop/git/trees{/sha}", "clone_url": "https://github.com/solnic/waterdrop.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/waterdrop/subscription", "url": "https://api.github.com/repos/solnic/waterdrop", "statuses_url": "https://api.github.com/repos/solnic/waterdrop/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/waterdrop/milestones{/number}", "svn_url": "https://github.com/solnic/waterdrop", "events_url": "https://api.github.com/repos/solnic/waterdrop/events", "updated_at": "2019-05-04T14:11:07Z", "created_at": "2019-05-04T14:11:04Z", "html_url": "https://github.com/solnic/waterdrop", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/waterdrop/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": "<PERSON>", "contributors_url": "https://api.github.com/repos/solnic/waterdrop/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/waterdrop/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/waterdrop/merges", "deployments_url": "https://api.github.com/repos/solnic/waterdrop/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/waterdrop/assignees{/user}", "git_url": "git://github.com/solnic/waterdrop.git", "forks_url": "https://api.github.com/repos/solnic/waterdrop/forks", "tags_url": "https://api.github.com/repos/solnic/waterdrop/tags", "open_issues": 0, "size": 324, "pushed_at": "2019-07-16T10:48:43Z", "issues_url": "https://api.github.com/repos/solnic/waterdrop/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/waterdrop/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/waterdrop/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/waterdrop/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/waterdrop/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/waterdrop/branches{/branch}", "description": "WaterDrop is a standalone Karafka component library for generating Kafka messages", "subscribers_url": "https://api.github.com/repos/solnic/waterdrop/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/waterdrop/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/waterdrop/git/refs{/sha}", "ssh_url": "**************:solnic/waterdrop.git", "releases_url": "https://api.github.com/repos/solnic/waterdrop/releases{/id}", "is_template": false, "name": "waterdrop", "languages_url": "https://api.github.com/repos/solnic/waterdrop/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/waterdrop/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/zeitwerk/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/zeitwerk/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/zeitwerk/hooks", "id": 376337930, "teams_url": "https://api.github.com/repos/solnic/zeitwerk/teams", "full_name": "solnic/zeitwerk", "git_commits_url": "https://api.github.com/repos/solnic/zeitwerk/git/commits{/sha}", "default_branch": "main", "downloads_url": "https://api.github.com/repos/solnic/zeitwerk/downloads", "stargazers_url": "https://api.github.com/repos/solnic/zeitwerk/stargazers", "blobs_url": "https://api.github.com/repos/solnic/zeitwerk/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/zeitwerk/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkzNzYzMzc5MzA=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/zeitwerk/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/zeitwerk/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/zeitwerk/git/trees{/sha}", "clone_url": "https://github.com/solnic/zeitwerk.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/zeitwerk/subscription", "url": "https://api.github.com/repos/solnic/zeitwerk", "statuses_url": "https://api.github.com/repos/solnic/zeitwerk/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/zeitwerk/milestones{/number}", "svn_url": "https://github.com/solnic/zeitwerk", "events_url": "https://api.github.com/repos/solnic/zeitwerk/events", "updated_at": "2021-06-12T16:36:20Z", "created_at": "2021-06-12T16:36:19Z", "html_url": "https://github.com/solnic/zeitwerk", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/zeitwerk/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": true, "topics": [], "language": null, "contributors_url": "https://api.github.com/repos/solnic/zeitwerk/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/zeitwerk/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/zeitwerk/merges", "deployments_url": "https://api.github.com/repos/solnic/zeitwerk/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/zeitwerk/assignees{/user}", "git_url": "git://github.com/solnic/zeitwerk.git", "forks_url": "https://api.github.com/repos/solnic/zeitwerk/forks", "tags_url": "https://api.github.com/repos/solnic/zeitwerk/tags", "open_issues": 0, "size": 580, "pushed_at": "2021-06-14T07:52:02Z", "issues_url": "https://api.github.com/repos/solnic/zeitwerk/issues{/number}", "homepage": "", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/zeitwerk/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/zeitwerk/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/zeitwerk/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/zeitwerk/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/zeitwerk/branches{/branch}", "description": "Efficient and thread-safe code loader for Ruby", "subscribers_url": "https://api.github.com/repos/solnic/zeitwerk/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "mit", "name": "MIT License", "node_id": "MDc6TGljZW5zZTEz", "spdx_id": "MIT", "url": "https://api.github.com/licenses/mit"}, "commits_url": "https://api.github.com/repos/solnic/zeitwerk/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/zeitwerk/git/refs{/sha}", "ssh_url": "**************:solnic/zeitwerk.git", "releases_url": "https://api.github.com/repos/solnic/zeitwerk/releases{/id}", "is_template": false, "name": "zeitwerk", "languages_url": "https://api.github.com/repos/solnic/zeitwerk/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/zeitwerk/contents/{+path}", "watchers": 0}, {"labels_url": "https://api.github.com/repos/solnic/zulip/labels{/name}", "keys_url": "https://api.github.com/repos/solnic/zulip/keys{/key_id}", "fork": true, "owner": {"avatar_url": "https://avatars.githubusercontent.com/u/1066?v=4", "events_url": "https://api.github.com/users/solnic/events{/privacy}", "followers_url": "https://api.github.com/users/solnic/followers", "following_url": "https://api.github.com/users/solnic/following{/other_user}", "gists_url": "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id": "", "html_url": "https://github.com/solnic", "id": 1066, "login": "solnic", "node_id": "MDQ6VXNlcjEwNjY=", "organizations_url": "https://api.github.com/users/solnic/orgs", "received_events_url": "https://api.github.com/users/solnic/received_events", "repos_url": "https://api.github.com/users/solnic/repos", "site_admin": false, "starred_url": "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/solnic/subscriptions", "type": "User", "url": "https://api.github.com/users/solnic"}, "hooks_url": "https://api.github.com/repos/solnic/zulip/hooks", "id": 178829599, "teams_url": "https://api.github.com/repos/solnic/zulip/teams", "full_name": "solnic/zulip", "git_commits_url": "https://api.github.com/repos/solnic/zulip/git/commits{/sha}", "default_branch": "master", "downloads_url": "https://api.github.com/repos/solnic/zulip/downloads", "stargazers_url": "https://api.github.com/repos/solnic/zulip/stargazers", "blobs_url": "https://api.github.com/repos/solnic/zulip/git/blobs{/sha}", "collaborators_url": "https://api.github.com/repos/solnic/zulip/collaborators{/collaborator}", "permissions": {"admin": true, "maintain": true, "pull": true, "push": true, "triage": true}, "node_id": "MDEwOlJlcG9zaXRvcnkxNzg4Mjk1OTk=", "watchers_count": 0, "notifications_url": "https://api.github.com/repos/solnic/zulip/notifications{?since,all,participating}", "compare_url": "https://api.github.com/repos/solnic/zulip/compare/{base}...{head}", "trees_url": "https://api.github.com/repos/solnic/zulip/git/trees{/sha}", "clone_url": "https://github.com/solnic/zulip.git", "has_downloads": true, "subscription_url": "https://api.github.com/repos/solnic/zulip/subscription", "url": "https://api.github.com/repos/solnic/zulip", "statuses_url": "https://api.github.com/repos/solnic/zulip/statuses/{sha}", "milestones_url": "https://api.github.com/repos/solnic/zulip/milestones{/number}", "svn_url": "https://github.com/solnic/zulip", "events_url": "https://api.github.com/repos/solnic/zulip/events", "updated_at": "2019-04-01T09:27:35Z", "created_at": "2019-04-01T09:27:23Z", "html_url": "https://github.com/solnic/zulip", "archived": false, "allow_forking": true, "pulls_url": "https://api.github.com/repos/solnic/zulip/pulls{/number}", "mirror_url": null, "has_projects": true, "has_wiki": false, "topics": [], "language": "Python", "contributors_url": "https://api.github.com/repos/solnic/zulip/contributors", "web_commit_signoff_required": false, "issue_events_url": "https://api.github.com/repos/solnic/zulip/issues/events{/number}", "forks": 0, "merges_url": "https://api.github.com/repos/solnic/zulip/merges", "deployments_url": "https://api.github.com/repos/solnic/zulip/deployments", "visibility": "public", "assignees_url": "https://api.github.com/repos/solnic/zulip/assignees{/user}", "git_url": "git://github.com/solnic/zulip.git", "forks_url": "https://api.github.com/repos/solnic/zulip/forks", "tags_url": "https://api.github.com/repos/solnic/zulip/tags", "open_issues": 0, "size": 233607, "pushed_at": "2019-04-01T06:14:58Z", "issues_url": "https://api.github.com/repos/solnic/zulip/issues{/number}", "homepage": "https://zulip.org/", "private": false, "disabled": false, "forks_count": 0, "git_tags_url": "https://api.github.com/repos/solnic/zulip/git/tags{/sha}", "archive_url": "https://api.github.com/repos/solnic/zulip/{archive_format}{/ref}", "comments_url": "https://api.github.com/repos/solnic/zulip/comments{/number}", "has_pages": false, "issue_comment_url": "https://api.github.com/repos/solnic/zulip/issues/comments{/number}", "branches_url": "https://api.github.com/repos/solnic/zulip/branches{/branch}", "description": "Zulip server - powerful open source team chat", "subscribers_url": "https://api.github.com/repos/solnic/zulip/subscribers", "stargazers_count": 0, "has_discussions": false, "license": {"key": "apache-2.0", "name": "Apache License 2.0", "node_id": "MDc6TGljZW5zZTI=", "spdx_id": "Apache-2.0", "url": "https://api.github.com/licenses/apache-2.0"}, "commits_url": "https://api.github.com/repos/solnic/zulip/commits{/sha}", "has_issues": false, "git_refs_url": "https://api.github.com/repos/solnic/zulip/git/refs{/sha}", "ssh_url": "**************:solnic/zulip.git", "releases_url": "https://api.github.com/repos/solnic/zulip/releases{/id}", "is_template": false, "name": "<PERSON><PERSON>", "languages_url": "https://api.github.com/repos/solnic/zulip/languages", "open_issues_count": 0, "contents_url": "https://api.github.com/repos/solnic/zulip/contents/{+path}", "watchers": 0}]