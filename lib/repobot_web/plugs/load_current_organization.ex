defmodule RepobotWeb.Plugs.LoadCurrentOrganization do
  import Plug.Conn
  alias Repobot.Repo
  alias Repobot.Accounts.Organization

  def init(opts), do: opts

  def call(conn, _opts) do
    cond do
      # If there's no current user, don't try to load an organization
      is_nil(conn.assigns[:current_user]) ->
        assign(conn, :current_organization, nil)

      # If there's a current_organization_id in the session, try to load that
      org_id = get_session(conn, :current_organization_id) ->
        case Repo.get(Organization, org_id) |> Repo.preload(:settings) do
          %Organization{} = org -> assign(conn, :current_organization, org)
          nil -> assign_default_organization(conn)
        end

      # Otherwise, use the user's default organization
      true ->
        assign_default_organization(conn)
    end
  end

  defp assign_default_organization(conn) do
    user = conn.assigns.current_user

    if user.default_organization_id do
      case Repo.get(Organization, user.default_organization_id) |> Repo.preload(:settings) do
        %Organization{} = org -> assign(conn, :current_organization, org)
        nil -> assign(conn, :current_organization, nil)
      end
    else
      assign(conn, :current_organization, nil)
    end
  end
end
