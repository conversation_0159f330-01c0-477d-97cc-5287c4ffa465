defmodule RepobotWeb.Plugs.LoadCurrentUser do
  import Plug.Conn
  alias Repobot.Repo
  alias Repobot.Accounts.User

  def init(opts), do: opts

  def call(conn, _opts) do
    if user_id = get_session(conn, :current_user_id) do
      case Repo.get(User, user_id) do
        %User{} = user -> assign(conn, :current_user, user)
        nil -> assign(conn, :current_user, nil)
      end
    else
      assign(conn, :current_user, nil)
    end
  end
end
