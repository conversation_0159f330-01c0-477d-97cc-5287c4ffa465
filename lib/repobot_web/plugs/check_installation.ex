defmodule RepobotWeb.Plugs.CheckInstallation do
  import Plug.Conn

  def init(opts), do: opts

  def call(conn, _opts) do
    org = conn.assigns[:current_organization]
    install_url = Application.get_env(:repobot, :github_app_install_url)

    cond do
      is_nil(org) ->
        assign(conn, :github_app_installation_required, false)

      is_nil(org.installation_id) ->
        conn
        |> assign(:github_app_installation_required, true)
        |> assign(:github_app_install_url, install_url)

      true ->
        assign(conn, :github_app_installation_required, false)
    end
  end
end
