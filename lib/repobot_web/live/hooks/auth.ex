defmodule RepobotWeb.Live.Hooks.Auth do
  import Phoenix.Component
  import Phoenix.LiveView

  alias <PERSON>obot.{Accounts, Repo}
  alias Repobot.Accounts.Organization

  def on_mount(:default, _params, session, socket) do
    if current_user =
         session["current_user_id"] && Repo.get(Repobot.Accounts.User, session["current_user_id"]) do
      current_organization =
        cond do
          org_id = session["current_organization_id"] ->
            # Try to load from session, but don't fail if not found
            Repo.get(Organization, org_id) |> Repo.preload(:settings)

          current_user.default_organization_id ->
            # Try to load from user default, but don't fail if not found
            Repo.get(Organization, current_user.default_organization_id)
            |> Repo.preload(:settings)

          true ->
            nil
        end

      # If we couldn't find an organization, try to get the first organization the user belongs to
      current_organization =
        if is_nil(current_organization) do
          # Get first organization from user organizations
          {:ok, orgs} = Accounts.get_user_organizations_from_db(current_user)

          case orgs do
            [first_org | _] -> first_org
            _ -> nil
          end
        else
          current_organization
        end

      github_app_install_url = Application.get_env(:repobot, :github_app_install_url, "#")

      socket =
        socket
        |> assign(:current_user, current_user)
        |> assign(:current_organization, current_organization)
        |> assign(:github_app_install_url, github_app_install_url)

      {:cont, socket}
    else
      socket =
        socket
        |> put_flash(:error, "You must be logged in to access this page")
        |> redirect(to: "/")

      {:halt, socket}
    end
  end
end
