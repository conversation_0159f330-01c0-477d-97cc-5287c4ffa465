defmodule RepobotWeb.Live.SyncMap.Index do
  use RepobotWeb, :live_view

  require Logger

  alias <PERSON><PERSON>ot.Repositories
  alias Repobot.SourceFiles

  @impl true
  def mount(_params, _session, socket) do
    {:ok, assign(socket, :page_title, "Sync Map")}
  end

  @impl true
  def handle_params(_params, _uri, socket) do
    # Fetch repositories and source files for the current organization
    organization = socket.assigns.current_organization
    repos = Repositories.list_repositories_for_organization(organization)
    source_files = SourceFiles.list_source_files_for_organization(organization)

    Logger.info(
      "Sync Map: Fetched #{length(repos)} repos and #{length(source_files)} source files for org #{organization.id}"
    )

    # Prepare data for the graph
    {nodes, edges} = build_graph_data(repos, source_files)

    # Push data to the frontend hook
    payload = %{nodes: nodes, edges: edges}
    Logger.info("Sync Map: Pushing payload: #{inspect(payload)}")
    socket = push_event(socket, "render_sync_map", payload)

    {:noreply, assign(socket, nodes: nodes, edges: edges)}
  end

  defp build_graph_data(_repos_ignored, source_files) do
    # Process source files to define connections and identify necessary nodes
    {edges, connected_repo_ids, connected_sf_ids} =
      Enum.reduce(source_files, {[], MapSet.new(), MapSet.new()}, fn sf,
                                                                     {acc_edges, acc_repo_ids,
                                                                      acc_sf_ids} ->
        if sf.source_repository && !Enum.empty?(sf.repositories) do
          template_repo = sf.source_repository
          target_repos = sf.repositories

          # Create edges: Template -> SourceFile -> TargetRepo
          edges_to_sf = [%{from: template_repo.id, to: sf.id, arrows: "to"}]

          edges_from_sf =
            Enum.map(target_repos, fn target_repo ->
              %{from: sf.id, to: target_repo.id, arrows: "to"}
            end)

          new_edges = edges_to_sf ++ edges_from_sf

          # Collect involved IDs
          new_repo_ids = MapSet.put(MapSet.new(Enum.map(target_repos, & &1.id)), template_repo.id)
          new_sf_ids = MapSet.new([sf.id])

          {acc_edges ++ new_edges, MapSet.union(acc_repo_ids, new_repo_ids),
           MapSet.union(acc_sf_ids, new_sf_ids)}
        else
          Logger.info("Sync Map: Skipping SF #{sf.id} due to missing source or targets.")
          {acc_edges, acc_repo_ids, acc_sf_ids}
        end
      end)

    Logger.info("Sync Map: Generated #{length(edges)} edges")

    Logger.info(
      "Sync Map: Found #{MapSet.size(connected_repo_ids)} repo IDs and #{MapSet.size(connected_sf_ids)} SF IDs"
    )

    # Fetch details for connected repositories and source files
    connected_repos = Repositories.list_repositories_by_ids(MapSet.to_list(connected_repo_ids))
    connected_sfs = SourceFiles.list_source_files_by_ids(MapSet.to_list(connected_sf_ids))

    Logger.info(
      "Sync Map: Fetched #{length(connected_repos)} repos and #{length(connected_sfs)} SFs for nodes"
    )

    # Create nodes for repositories
    repo_nodes =
      Enum.map(connected_repos, fn repo ->
        %{
          data: %{
            id: repo.id,
            label: repo.full_name,
            title: "Language: #{repo.language || "N/A"}"
          },
          classes: if(repo.template, do: "template_repo", else: "target_repo")
        }
      end)

    # Create nodes for source files
    sf_nodes =
      Enum.map(connected_sfs, fn sf ->
        %{
          data: %{
            id: sf.id,
            label: sf.name,
            title: "Source File: #{sf.name}"
          },
          classes: "source_file"
        }
      end)

    nodes = repo_nodes ++ sf_nodes
    Logger.info("Sync Map: Generated #{length(nodes)} total nodes for Cytoscape")

    # Create edges (same structure, but Cytoscape uses source/target in data key)
    cy_edges =
      Enum.map(edges, fn edge ->
        %{data: %{id: Ecto.UUID.generate(), source: edge.from, target: edge.to}}
      end)

    Logger.info("Sync Map: Generated #{length(cy_edges)} edges for Cytoscape")

    {nodes, cy_edges}
  end
end
