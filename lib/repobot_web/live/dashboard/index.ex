defmodule RepobotWeb.Live.Dashboard.Index do
  use RepobotWeb, :live_view

  alias Repobot.{Repo, Repository, SourceFile, PullRequest, Events.Event}
  alias Repobot.Accounts.User
  import Ecto.Query

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # Set up a timer to periodically refresh the dashboard data
      :timer.send_interval(30_000, self(), :refresh_data)
    end

    socket =
      socket
      |> assign_dashboard_data()
      |> assign(:active_tab, "overview")

    {:ok, socket}
  end

  @impl true
  def handle_event("sync_all", _params, socket) do
    # Handle sync all repositories event
    # This is a placeholder for the actual sync logic
    # You would trigger a background job here

    {:noreply, put_flash(socket, :info, "Sync started for all repositories")}
  end

  @impl true
  def handle_event("sync_repository", %{"id" => repo_id}, socket) do
    # Handle syncing a specific repository
    # This is a placeholder for the actual sync logic

    {:noreply, put_flash(socket, :info, "Sync started for repository #{repo_id}")}
  end

  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    # Handle switching tabs in the insights panel
    {:noreply, assign(socket, :active_tab, tab)}
  end

  @impl true
  def handle_info(:refresh_data, socket) do
    # Refresh the dashboard data periodically
    {:noreply, assign_dashboard_data(socket)}
  end

  defp assign_dashboard_data(socket) do
    socket
    |> assign(:repositories, get_repositories(socket))
    |> assign(:stats, get_stats(socket))
    |> assign(:recent_activity, get_recent_activity(socket))
    |> assign(:contribution_data, get_contribution_data(socket))
    |> assign(:pull_requests, get_recent_pull_requests(socket))
    |> assign(:sync_progress, calculate_sync_progress(socket))
    |> assign(:repo_insights, get_repository_insights(socket))
  end

  defp get_repositories(socket) do
    org_id = socket.assigns.current_organization.id

    # Get repositories with their latest sync events, sorted by the latest event timestamp
    query =
      from r in Repository,
        join: e in Event,
        on: r.id == e.repository_id,
        where:
          r.organization_id == ^org_id and
            e.type in ["repobot.sync", "github.push", "github.pull_request"],
        group_by: r.id,
        order_by: [desc: max(e.inserted_at)],
        limit: 5,
        select: %{
          id: r.id,
          name: r.full_name,
          template: r.template,
          private: r.private,
          sync_mode: r.sync_mode,
          updated_at: r.updated_at
        }

    repositories = Repo.all(query)

    # Get latest sync events for each repository
    repo_ids = Enum.map(repositories, & &1.id)

    latest_sync_events =
      from(e in Event,
        where:
          e.repository_id in ^repo_ids and
            e.type in ["repobot.sync", "github.push", "github.pull_request"],
        order_by: [desc: e.inserted_at],
        select: %{
          repository_id: e.repository_id,
          type: e.type,
          status: e.status,
          payload: e.payload,
          inserted_at: e.inserted_at
        }
      )
      |> Repo.all()
      |> Enum.group_by(& &1.repository_id)

    # Combine repository data with sync status
    repositories
    |> Enum.map(fn repo ->
      sync_events = Map.get(latest_sync_events, repo.id, [])
      latest_sync = List.first(sync_events)

      sync_status =
        case latest_sync do
          %{type: "repobot.sync", status: "success"} -> "synced"
          %{type: "repobot.sync", status: "completed"} -> "synced"
          %{type: "repobot.sync", status: "in_progress"} -> "syncing"
          %{type: "repobot.sync", status: "failed"} -> "failed"
          %{type: "github.push"} -> "pushed"
          %{type: "github.pull_request"} -> "pr-updated"
          _ -> "unknown"
        end

      event_details =
        if latest_sync && latest_sync.payload do
          case latest_sync.type do
            "repobot.sync" ->
              case latest_sync.payload do
                %{"commit_sha" => commit_sha, "file_ids" => file_ids} ->
                  "Sync: #{length(file_ids)} files (#{String.slice(commit_sha || "", 0, 7)})"

                _ ->
                  "Sync event"
              end

            "github.push" ->
              commit_count =
                if is_list(latest_sync.payload["commits"]),
                  do: length(latest_sync.payload["commits"]),
                  else: 1

              ref = latest_sync.payload["ref"]
              branch = if ref, do: String.replace_prefix(ref, "refs/heads/", ""), else: "unknown"

              "Push: #{commit_count} commit(s) to #{branch}"

            "github.pull_request" ->
              action = latest_sync.payload["action"]

              pr_number =
                latest_sync.payload["number"] || latest_sync.payload["pull_request"]["number"]

              "PR ##{pr_number}: #{action}"

            _ ->
              "Event: #{latest_sync.type}"
          end
        else
          nil
        end

      last_sync_time = relative_time(latest_sync.inserted_at)

      Map.merge(repo, %{
        sync_status: sync_status,
        event_details: event_details,
        last_sync: last_sync_time
      })
    end)
  end

  defp get_stats(socket) do
    org_id = socket.assigns.current_organization.id

    # Get basic stats for the dashboard
    repos_count =
      from(r in Repository,
        where: r.organization_id == ^org_id,
        select: count(r.id)
      )
      |> Repo.one()

    source_files_count =
      from(s in SourceFile,
        where: s.organization_id == ^org_id,
        select: count(s.id)
      )
      |> Repo.one()

    # Join with source_files to get organization scoping through relationship
    pull_requests_count =
      from(p in PullRequest,
        join: s in SourceFile,
        on: p.source_file_id == s.id,
        where: p.status == "open" and s.organization_id == ^org_id,
        select: count(p.id)
      )
      |> Repo.one()

    # Get count of events in the last month
    one_month_ago = DateTime.utc_now() |> DateTime.add(-30 * 24 * 60 * 60, :second)

    recent_activity_count =
      from(e in Event,
        where: e.inserted_at > ^one_month_ago and e.organization_id == ^org_id,
        select: count(e.id)
      )
      |> Repo.one()

    [
      %{
        name: "Total Repositories",
        value: repos_count || 0,
        change: calculate_change(:repositories, socket),
        icon: "hero-code-bracket-square-solid"
      },
      %{
        name: "Source Files",
        value: source_files_count || 0,
        change: calculate_change(:source_files, socket),
        icon: "hero-document-duplicate-solid"
      },
      %{
        name: "Open Pull Requests",
        value: pull_requests_count || 0,
        change: calculate_change(:pull_requests, socket),
        icon: "hero-arrow-path-solid"
      },
      %{
        name: "Recent Activity",
        value: recent_activity_count || 0,
        change: calculate_change(:activity, socket),
        icon: "hero-bolt-solid"
      }
    ]
  end

  defp get_recent_activity(socket) do
    org_id = socket.assigns.current_organization.id

    # Get recent push events specifically for template repositories
    query =
      from e in Event,
        join: r in Repository,
        on: e.repository_id == r.id,
        left_join: u in User,
        on: e.user_id == u.id,
        where:
          fragment("? LIKE ?", e.type, "github.push%") and r.template == true and
            r.organization_id == ^org_id and e.organization_id == ^org_id,
        order_by: [desc: e.inserted_at],
        limit: 10,
        select: %{
          id: e.id,
          type: e.type,
          payload: e.payload,
          inserted_at: e.inserted_at,
          user: %{
            id: u.id,
            login: u.login,
            avatar_url: fragment("?->>'avatar_url'", u.info)
          },
          repository: %{
            id: r.id,
            name: r.full_name
          }
        }

    Repo.all(query)
    |> Enum.map(fn event ->
      # Extract commit information from payload when available
      commit_message =
        case event.payload do
          %{"commits" => [%{"message" => message} | _]} -> message
          %{"head_commit" => %{"message" => message}} -> message
          _ -> nil
        end

      # Extract modified files from payload
      modified_files = extract_modified_files(event.payload)

      # Extract user information from payload if needed
      payload_user = extract_user_from_payload(event.payload)

      # Ensure we have valid user data, prioritizing payload data if user is nil
      user =
        if is_nil(event.user.login) do
          payload_user
        else
          ensure_valid_user(event.user)
        end

      repository = ensure_valid_repository(event.repository)

      %{
        id: event.id,
        user: user,
        action: "pushed to template repository",
        commit_message: commit_message,
        modified_files: modified_files,
        date: relative_time(event.inserted_at),
        repository: repository.name
      }
    end)
  end

  # Extract modified files from GitHub webhook payload
  defp extract_modified_files(payload) do
    cond do
      # Get modified files from head_commit
      is_map(payload) && is_map(payload["head_commit"]) &&
          is_list(payload["head_commit"]["modified"]) ->
        payload["head_commit"]["modified"]

      # Get modified files from first commit
      is_map(payload) && is_list(payload["commits"]) && length(payload["commits"]) > 0 ->
        commit = Enum.at(payload["commits"], 0)

        if is_map(commit) && is_list(commit["modified"]) do
          commit["modified"]
        else
          []
        end

      # No modified files found
      true ->
        []
    end
  end

  # Extract user information from GitHub webhook payload
  defp extract_user_from_payload(payload) do
    cond do
      # Try to get info from sender
      is_map(payload) && is_map(payload["sender"]) && is_binary(payload["sender"]["login"]) ->
        %{
          id: payload["sender"]["id"],
          login: payload["sender"]["login"],
          avatar_url: payload["sender"]["avatar_url"],
          initials: String.first(payload["sender"]["login"])
        }

      # Try to get info from pusher
      is_map(payload) && is_map(payload["pusher"]) && is_binary(payload["pusher"]["name"]) ->
        %{
          id: nil,
          login: payload["pusher"]["name"],
          avatar_url: nil,
          initials: String.first(payload["pusher"]["name"])
        }

      # Try to get info from head_commit author
      is_map(payload) && is_map(payload["head_commit"]) &&
          is_map(payload["head_commit"]["author"]) ->
        %{
          id: nil,
          login:
            payload["head_commit"]["author"]["username"] ||
              payload["head_commit"]["author"]["name"],
          avatar_url: nil,
          initials:
            String.first(
              payload["head_commit"]["author"]["username"] ||
                payload["head_commit"]["author"]["name"]
            )
        }

      # Fallback to anonymous
      true ->
        %{
          id: nil,
          login: "anonymous",
          avatar_url: nil,
          initials: "A"
        }
    end
  end

  # Ensure we have valid user data to avoid nil errors with String.first/1
  defp ensure_valid_user(user) do
    login = user.login || "anonymous"

    %{
      id: user.id,
      login: login,
      avatar_url: user.avatar_url,
      # Add initials for avatar fallback
      initials: String.first(login)
    }
  end

  # Ensure we have valid repository data
  defp ensure_valid_repository(repo) do
    %{
      id: repo.id,
      name: repo.name || "Unknown Repository"
    }
  end

  defp get_recent_pull_requests(socket) do
    org_id = socket.assigns.current_organization.id

    # Get recent pull requests for the current organization
    query =
      from p in PullRequest,
        join: s in SourceFile,
        on: p.source_file_id == s.id,
        where: s.organization_id == ^org_id,
        order_by: [desc: p.inserted_at],
        limit: 10

    Repo.all(query)
  end

  defp calculate_sync_progress(socket) do
    org_id = socket.assigns.current_organization.id

    # Calculate actual sync progress based on events
    # Get total number of repositories
    total_repos =
      from(r in Repository,
        where: r.organization_id == ^org_id,
        select: count(r.id)
      )
      |> Repo.one() || 1

    # Get number of successfully synced repositories in the last day
    one_day_ago = DateTime.utc_now() |> DateTime.add(-24 * 60 * 60, :second)

    synced_repos =
      from(e in Event,
        join: r in Repository,
        on: e.repository_id == r.id,
        where:
          e.type == "repobot.sync" and e.status == "success" and
            e.inserted_at > ^one_day_ago and
            r.organization_id == ^org_id and e.organization_id == ^org_id,
        select: count(fragment("DISTINCT ?", e.repository_id))
      )
      |> Repo.one() || 0

    # Calculate percentage of synced repositories
    if total_repos == 0 do
      0
    else
      round(synced_repos / total_repos * 100)
    end
  end

  defp get_contribution_data(socket) do
    org_id = socket.assigns.current_organization.id

    # Get GitHub activity data grouped by day of week
    # Focus on github.push and github.pull_request.* events

    # Note: Not filtering by date since our test data may have dates in the future
    day_of_week_query =
      from e in Event,
        where:
          (fragment("? LIKE ?", e.type, "github.push%") or
             fragment("? LIKE ?", e.type, "github.pull_request%")) and
            e.organization_id == ^org_id,
        group_by: fragment("EXTRACT(DOW FROM ?)", e.inserted_at),
        order_by: fragment("EXTRACT(DOW FROM ?)", e.inserted_at),
        select: %{
          day: fragment("EXTRACT(DOW FROM ?)", e.inserted_at),
          count: count(e.id)
        }

    result = Repo.all(day_of_week_query)

    # Map the results to days of the week
    days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

    if Enum.empty?(result) do
      # Return fallback data if no events
      [
        %{name: "Mon", value: 0},
        %{name: "Tue", value: 0},
        %{name: "Wed", value: 0},
        %{name: "Thu", value: 0},
        %{name: "Fri", value: 0},
        %{name: "Sat", value: 0},
        %{name: "Sun", value: 0}
      ]
    else
      # Map database results to named days
      Enum.map(0..6, fn day_num ->
        day_data = Enum.find(result, fn r -> Decimal.to_integer(r.day) == day_num end)

        %{
          name: Enum.at(days, day_num),
          value:
            if day_data do
              day_data.count
            else
              0
            end
        }
      end)
    end
  end

  defp calculate_change(type, socket) do
    # Calculate real change percentages by comparing current month to previous month
    org_id = socket.assigns.current_organization.id

    # Get start of current and previous months
    now = DateTime.utc_now()
    current_month_start = %{now | day: 1, hour: 0, minute: 0, second: 0}

    previous_month_start =
      if now.month == 1 do
        %{now | year: now.year - 1, month: 12, day: 1, hour: 0, minute: 0, second: 0}
      else
        %{now | month: now.month - 1, day: 1, hour: 0, minute: 0, second: 0}
      end

    # Metrics for current and previous months
    case type do
      :repositories ->
        calculate_month_over_month_change(
          Repository,
          [organization_id: org_id],
          current_month_start,
          previous_month_start
        )

      :source_files ->
        calculate_month_over_month_change(
          SourceFile,
          [organization_id: org_id],
          current_month_start,
          previous_month_start
        )

      :pull_requests ->
        calculate_month_over_month_change(
          PullRequest,
          [source_file: [organization_id: org_id]],
          current_month_start,
          previous_month_start
        )

      :activity ->
        calculate_month_over_month_change(
          Event,
          [organization_id: org_id],
          current_month_start,
          previous_month_start
        )

      _ ->
        0
    end
  end

  defp calculate_month_over_month_change(
         schema,
         conditions,
         current_month_start,
         previous_month_start
       ) do
    # Calculate change percentage between current and previous month

    # Query for current month
    current_month_query =
      build_scoped_query(schema, conditions)
      |> where([s], s.inserted_at >= ^current_month_start)
      |> select([s], count(s.id))

    # Query for previous month
    previous_month_query =
      build_scoped_query(schema, conditions)
      |> where(
        [s],
        s.inserted_at >= ^previous_month_start and s.inserted_at < ^current_month_start
      )
      |> select([s], count(s.id))

    current_month_count = Repo.one(current_month_query) || 0
    previous_month_count = Repo.one(previous_month_query) || 0

    # Calculate percentage change
    change_percentage =
      if previous_month_count == 0 do
        if current_month_count == 0 do
          # No change if both months are zero
          0
        else
          # 100% increase if only current month has data
          100
        end
      else
        round((current_month_count - previous_month_count) / previous_month_count * 100)
      end

    # Cap extremes for better display
    cond do
      change_percentage > 1000 -> 999
      change_percentage < -1000 -> -999
      true -> change_percentage
    end
  end

  # Helper to build queries with nested conditions
  defp build_scoped_query(schema, conditions) do
    Enum.reduce(conditions, from(s in schema), fn
      {field, [{nested_field, value}]}, query ->
        from s in query,
          join: j in assoc(s, ^field),
          where: field(j, ^nested_field) == ^value

      {field, value}, query ->
        from s in query,
          where: field(s, ^field) == ^value
    end)
  end

  defp relative_time(datetime) do
    # Calculate a human-readable relative time from a datetime
    # E.g., "2 minutes ago", "3 hours ago", "yesterday", etc.
    now = DateTime.utc_now()
    diff = DateTime.diff(now, datetime)

    cond do
      diff < 60 -> "just now"
      diff < 60 * 60 -> "#{div(diff, 60)} minutes ago"
      diff < 60 * 60 * 24 -> "#{div(diff, 60 * 60)} hours ago"
      diff < 60 * 60 * 24 * 2 -> "yesterday"
      diff < 60 * 60 * 24 * 7 -> "#{div(diff, 60 * 60 * 24)} days ago"
      diff < 60 * 60 * 24 * 30 -> "#{div(diff, 60 * 60 * 24 * 7)} weeks ago"
      true -> "#{div(diff, 60 * 60 * 24 * 30)} months ago"
    end
  end

  # Get repository insights metrics
  defp get_repository_insights(socket) do
    org_id = socket.assigns.current_organization.id

    # Get repositories and aggregate their data
    repositories =
      from(r in Repository,
        where: r.organization_id == ^org_id,
        select: r
      )
      |> Repo.all()

    # Initialize counters
    total_repos = length(repositories)
    template_repos = Enum.count(repositories, & &1.template)
    private_repos = Enum.count(repositories, & &1.private)

    # Extract language data
    languages =
      repositories
      |> Enum.map(fn repo ->
        case repo.data do
          %{"language" => language} when not is_nil(language) -> language
          _ -> nil
        end
      end)
      |> Enum.reject(&is_nil/1)
      |> Enum.group_by(& &1)
      |> Enum.map(fn {lang, occurrences} -> {lang, length(occurrences)} end)
      |> Enum.sort_by(fn {_lang, count} -> count end, :desc)
      |> Enum.take(5)

    # Get pull request stats from our database
    total_prs =
      from(p in PullRequest,
        join: s in SourceFile,
        on: p.source_file_id == s.id,
        where: s.organization_id == ^org_id,
        select: count(p.id)
      )
      |> Repo.one() || 0

    # Get open PRs
    open_prs =
      from(p in PullRequest,
        join: s in SourceFile,
        on: p.source_file_id == s.id,
        where: p.status == "open" and s.organization_id == ^org_id,
        select: count(p.id)
      )
      |> Repo.one() || 0

    # Get merged PRs
    merged_prs =
      from(p in PullRequest,
        join: s in SourceFile,
        on: p.source_file_id == s.id,
        where: p.status == "merged" and s.organization_id == ^org_id,
        select: count(p.id)
      )
      |> Repo.one() || 0

    # Get closed (but not merged) PRs
    closed_prs =
      from(p in PullRequest,
        join: s in SourceFile,
        on: p.source_file_id == s.id,
        where: p.status == "closed" and s.organization_id == ^org_id,
        select: count(p.id)
      )
      |> Repo.one() || 0

    # Get sync counts
    total_syncs =
      from(e in Event,
        where: e.type == "repobot.sync" and e.organization_id == ^org_id,
        select: count(e.id)
      )
      |> Repo.one() || 0

    successful_syncs =
      from(e in Event,
        where:
          e.type == "repobot.sync" and e.status == "success" and e.organization_id == ^org_id,
        select: count(e.id)
      )
      |> Repo.one() || 0

    failed_syncs =
      from(e in Event,
        where: e.type == "repobot.sync" and e.status == "failed" and e.organization_id == ^org_id,
        select: count(e.id)
      )
      |> Repo.one() || 0

    # Calculate sync success rate
    sync_success_rate =
      if total_syncs > 0 do
        round(successful_syncs / total_syncs * 100)
      else
        0
      end

    %{
      total_repos: total_repos,
      template_repos: template_repos,
      private_repos: private_repos,
      total_prs: total_prs,
      open_prs: open_prs,
      merged_prs: merged_prs,
      closed_prs: closed_prs,
      total_syncs: total_syncs,
      successful_syncs: successful_syncs,
      failed_syncs: failed_syncs,
      sync_success_rate: sync_success_rate,
      languages: languages
    }
  end

  defp language_color(language) do
    color =
      case String.downcase(language) do
        "javascript" -> "bg-yellow-400"
        "typescript" -> "bg-blue-500"
        "python" -> "bg-green-500"
        "ruby" -> "bg-red-600"
        "go" -> "bg-cyan-500"
        "rust" -> "bg-orange-600"
        "java" -> "bg-red-500"
        "c#" -> "bg-purple-600"
        "c++" -> "bg-pink-500"
        "php" -> "bg-indigo-400"
        "html" -> "bg-orange-500"
        "css" -> "bg-blue-400"
        "elixir" -> "bg-purple-500"
        "shell" -> "bg-gray-600"
        "swift" -> "bg-orange-500"
        "kotlin" -> "bg-purple-400"
        "dart" -> "bg-teal-500"
        _ -> "bg-gray-500"
      end

    "w-2 h-2 rounded-full #{color}"
  end
end
