defmodule RepobotWeb.Live.Onboarding.Step do
  defmacro __using__(_opts) do
    quote do
      use RepobotWeb, :live_component

      import RepobotWeb.Live.Onboarding.Step

      alias Repobot.Repo
      alias Repobot.{Folders, Repositories}
      alias Repobot.Workers.RepositoryLoaderWorker

      def assign_repositories(%{assigns: %{repositories: repos}} = socket) do
        socket
        |> assign(
          :folders,
          Folders.list_folders(
            socket.assigns.current_user,
            nil,
            socket.assigns.current_organization.id
          )
        )
        |> assign(:unorganized_repos, Enum.filter(repos, &is_nil(&1.folder_id)))
        |> assign(:repos_by_folder, Enum.group_by(repos, & &1.folder_id))
      end

      def maybe_load_repositories(socket) do
        if connected?(socket) do
          if Map.has_key?(socket.assigns, :repositories) do
            socket
          else
            organization_id = socket.assigns.current_organization.id
            user = socket.assigns.current_user

            repositories = Repositories.user_repositories(user, false, organization_id)

            {repositories, needs_refresh} =
              if Enum.empty?(repositories) do
                {Repositories.user_repositories(user, :refresh, organization_id), true}
              else
                {repositories, false}
              end

            socket
            |> assign(:repositories, repositories)
            |> assign(:refreshing, needs_refresh)
          end
        else
          socket |> assign(:repositories, [])
        end
      end

      @doc """
      Loads repositories in the background using an Oban worker.

      This function provides a non-blocking alternative to maybe_load_repositories
      that uses background job processing with progress updates.
      """
      def maybe_load_repositories_async(socket) do
        if connected?(socket) do
          if Map.has_key?(socket.assigns, :repositories) do
            socket
          else
            organization_id = socket.assigns.current_organization.id
            user = socket.assigns.current_user

            # Check for cached repositories first
            repositories = Repositories.user_repositories(user, false, organization_id)

            if Enum.empty?(repositories) do
              # No cached repositories, start background loading
              topic = "repository_loading:#{user.id}"

              # Subscribe to repository loading events via Oban.Notifier
              channel = String.to_atom(topic)
              Oban.Notifier.listen([channel])

              case RepositoryLoaderWorker.enqueue_repository_loading(
                     user.id,
                     organization_id,
                     topic
                   ) do
                {:ok, _job} ->
                  socket
                  |> assign(:repositories, [])
                  |> assign(:loading_repositories, true)
                  |> assign(:loading_progress, 0)
                  |> assign(:loading_message, "Starting repository loading...")

                {:error, reason} ->
                  # Fall back to synchronous loading on error
                  require Logger
                  Logger.warning("Failed to enqueue repository loading job: #{inspect(reason)}")
                  maybe_load_repositories(socket)
              end
            else
              # Use cached repositories
              socket
              |> assign(:repositories, repositories)
              |> assign(:loading_repositories, false)
            end
          end
        else
          socket |> assign(:repositories, [])
        end
      end
    end
  end

  def finalize(name, state) do
    send(self(), {:step_completed, {name, state}})
  end
end
