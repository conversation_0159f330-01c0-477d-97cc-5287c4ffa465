defmodule RepobotWeb.Live.Onboarding.Steps.TemplateRepository.Create do
  use RepobotWeb.Live.Onboarding.Step

  alias Repobot.Repositories

  def update(%{repository_created: repo_data} = _assigns, socket) do
    # Handle repository creation event from parent component
    expected_full_name =
      "#{socket.assigns.current_user.login}/#{socket.assigns.template_repo_name}"

    if socket.assigns.waiting_for_repo and repo_data.full_name == expected_full_name do
      # Find the repository in the database
      case Repositories.get_repository_by(full_name: repo_data.full_name) do
        nil ->
          # Repository not yet in database, wait a bit more
          {:ok, socket}

        repository ->
          # Repository found, finalize the step
          finalize(:template_repository, %{
            template_repo: repository,
            template_repo_name: socket.assigns.template_repo_name,
            is_private: socket.assigns.is_private,
            waiting_for_repo: false
          })

          socket =
            socket
            |> assign(:template_repo, repository)
            |> assign(:waiting_for_repo, false)

          {:ok, socket}
      end
    else
      # Not the repository we're waiting for
      {:ok, socket}
    end
  end

  def update(assigns, socket) do
    # Get state from parent if available
    state = assigns[:state] || %{}

    socket =
      socket
      |> assign(assigns)
      |> assign_new(:template_repo_name, fn -> state[:template_repo_name] || "" end)
      |> assign_new(:template_repo_url, fn -> "" end)
      |> assign_new(:is_private, fn -> state[:is_private] || false end)
      |> assign_new(:template_repo, fn -> state[:template_repo] || nil end)
      |> assign_new(:waiting_for_repo, fn -> state[:waiting_for_repo] || false end)

    # Update the URL if we have a name
    socket =
      if socket.assigns.template_repo_name != "" do
        update_template_url(socket, socket.assigns.template_repo_name, socket.assigns.is_private)
      else
        socket
      end

    {:ok, socket}
  end

  def render(assigns) do
    ~H"""
    <div id={@id} class="space-y-4">
      <%= if @waiting_for_repo do %>
        <!-- Loading state while waiting for repository creation -->
        <div class="text-center py-8">
          <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-indigo-500">
            <svg
              class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
              </circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              >
              </path>
            </svg>
            Waiting for repository creation...
          </div>
          <p class="mt-4 text-sm text-slate-600">
            Please complete the repository creation in the GitHub window that opened.
            We'll automatically detect when your repository is ready.
          </p>
        </div>
      <% else %>
        <!-- Repository creation form -->
        <fieldset class="fieldset">
          <label for="template_name" class="label">
            Template Repository Name
          </label>
          <input
            type="text"
            name="template_repo_name"
            id="template_repo_name"
            class="input"
            placeholder="e.g., my-project-template"
            value={@template_repo_name}
            phx-keyup="update_template_name"
            phx-debounce="300"
            phx-target={@myself}
          />
        </fieldset>

        <p class="mt-2 text-sm text-slate-500">
          Choose a descriptive name for your template repository. This will help others understand its purpose.
        </p>

        <div :if={@private_repos} class="flex items-center">
          <button
            type="button"
            class={[
              "relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2",
              @is_private && "bg-indigo-600",
              !@is_private && "bg-slate-200"
            ]}
            role="switch"
            aria-checked={@is_private}
            phx-click="toggle_visibility"
            phx-target={@myself}
          >
            <span
              aria-hidden="true"
              class={[
                "pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out",
                @is_private && "translate-x-5",
                !@is_private && "translate-x-0"
              ]}
            >
            </span>
          </button>
          <span class="ml-3 text-sm">
            <span class="font-medium text-slate-900">Private Repository</span>
          </span>
        </div>
        
    <!-- Create Repository Button -->
        <div class="mt-6">
          <button
            :if={@template_repo_name != ""}
            type="button"
            phx-click="create_repository"
            phx-target={@myself}
            class="btn btn-primary"
          >
            <svg
              class="-ml-1 mr-2 h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Create Repository on GitHub
          </button>
          <p :if={@template_repo_name == ""} class="text-sm text-slate-500">
            Please enter a repository name to continue.
          </p>
        </div>
      <% end %>
    </div>
    """
  end

  def handle_event("toggle_visibility", _params, socket) do
    socket =
      update_template_url(socket, socket.assigns.template_repo_name, !socket.assigns.is_private)

    {:noreply, socket}
  end

  def handle_event("update_template_name", %{"value" => name}, socket) do
    socket = update_template_url(socket, name, socket.assigns.is_private)
    {:noreply, socket}
  end

  def handle_event("create_repository", _params, socket) do
    # Open GitHub repository creation page in a new window
    socket =
      socket
      |> assign(:waiting_for_repo, true)
      |> push_event("open_url", %{url: socket.assigns.template_repo_url})

    {:noreply, socket}
  end

  defp update_template_url(socket, name, is_private) do
    template_url =
      "https://github.com/new?" <>
        URI.encode_query(%{
          "name" => name,
          "owner" => socket.assigns.current_user.login,
          "description" =>
            "A template repository for standardizing project structure and best practices.",
          "visibility" => if(is_private, do: "private", else: "public")
        })

    socket
    |> assign(:template_repo_name, name)
    |> assign(:template_repo_url, template_url)
    |> assign(:is_private, is_private)
  end
end
