defmodule RepobotWeb.Live.Onboarding.Steps.Welcome do
  use RepobotWeb.Live.Onboarding.Step

  @doc """
  Renders the welcome step of the onboarding process.
  """
  def render(assigns) do
    ~H"""
    <div>
      <h2 class="text-2xl font-semibold text-slate-900 mb-4">Welcome to RepoBot!</h2>
      <p class="text-slate-600 mb-6">
        Let's set up your first template repository to help you manage code standards and patterns across your projects.
        Here's what we'll do:
      </p>
      <div class="space-y-4 text-slate-600">
        <div class="flex items-start gap-3">
          <div class="flex-shrink-0 w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center">
            <span class="text-sm font-medium text-indigo-600">1</span>
          </div>
          <p>
            Create or select a template repository that will be the source of truth for your shared files
          </p>
        </div>
        <div class="flex items-start gap-3">
          <div class="flex-shrink-0 w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center">
            <span class="text-sm font-medium text-indigo-600">2</span>
          </div>
          <p>Select the repositories you want to manage with this template</p>
        </div>
        <div class="flex items-start gap-3">
          <div class="flex-shrink-0 w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center">
            <span class="text-sm font-medium text-indigo-600">3</span>
          </div>
          <p>
            Choose which files to include in the template from the common files across your repositories
          </p>
        </div>
        <div class="flex items-start gap-3">
          <div class="flex-shrink-0 w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center">
            <span class="text-sm font-medium text-indigo-600">4</span>
          </div>
          <p>
            Review and complete the setup to start managing your repositories with RepoBot
          </p>
        </div>
      </div>
    </div>
    """
  end
end
