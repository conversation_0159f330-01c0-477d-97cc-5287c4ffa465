defmodule RepobotWeb.Live.Onboarding.Index do
  use RepobotWeb, :live_view

  alias RepobotWeb.Live.Onboarding.Steps

  require Logger

  @steps [
    welcome: Steps.Welcome,
    template_repository: Steps.TemplateRepository,
    repository_sync: Steps.RepositorySync,
    template_files: Steps.TemplateFiles,
    summary: Steps.Summary
  ]

  @step_headers [
    welcome: "Welcome",
    template_repository: "Template Repository",
    repository_sync: "Repository Sync",
    template_files: "Template Files",
    summary: "Summary"
  ]

  defmodule Step do
    defstruct [:name, :component, :state]
  end

  def mount(_params, _session, socket) do
    steps = build_steps()

    # Subscribe to repository events if connected
    if connected?(socket) do
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_events")
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_files")

      # Note: Repository loading events are now handled via Oban.Notifier
      # in the individual step components, not at the LiveView level
    end

    socket =
      socket
      |> assign(:current_step, get_step(:welcome, steps))
      |> assign(:steps, steps)
      |> assign(:state, fresh_state(steps))
      |> assign(:page_title, "Welcome to RepoBot")

    {:ok, socket}
  end

  def update(assigns, socket) do
    {:ok, assign(socket, assigns)}
  end

  attr :step_headers, :map, default: @step_headers

  def render(assigns) do
    ~H"""
    <div class="max-w-6xl mx-auto py-12">
      <div class="mb-8">
        <ul class="steps steps-horizontal w-full">
          <%= for {step, index} <- Enum.with_index(@steps) do %>
            <li
              class={[
                "step",
                if step.name == @current_step.name do
                  "step-primary"
                else
                  if index < Enum.find_index(@steps, &(&1.name == @current_step.name)) do
                    "step-primary"
                  else
                    ""
                  end
                end
              ]}
              data-step={step.name}
              data-testid={"onboarding-step-#{step.name}"}
              data-content={index + 1}
            >
              {@step_headers[step.name]}
            </li>
          <% end %>
        </ul>
      </div>

      <div class="p-8">
        <.live_component
          module={@current_step.component}
          id={@current_step.name}
          current_user={@current_user}
          current_organization={@current_organization}
          state={@state}
        />

        <div class="mt-8 flex justify-between">
          <button
            :if={@current_step.name != :welcome}
            phx-click="previous_step"
            class="btn btn-outline"
          >
            Previous
          </button>
          <div></div>
          <button :if={@current_step.name != :summary} phx-click="next_step" class="btn btn-primary">
            Next
          </button>
        </div>
      </div>
    </div>
    """
  end

  def handle_event("next_step", _params, socket) do
    current_step = socket.assigns.current_step
    current_index = Enum.find_index(socket.assigns.steps, &(&1.name == current_step.name))
    next_step = Enum.at(socket.assigns.steps, current_index + 1)

    # Finalize the current step before proceeding
    case current_step.name do
      :template_files ->
        # Send finalize message to the template files component
        send_update(Steps.TemplateFiles, id: :template_files, finalize_step: true)
        {:noreply, socket}

      _ ->
        # For other steps, proceed directly
        {:noreply, assign(socket, :current_step, next_step)}
    end
  end

  def handle_event("previous_step", _params, socket) do
    current_step = socket.assigns.current_step
    current_index = Enum.find_index(socket.assigns.steps, &(&1.name == current_step.name))
    previous_step = Enum.at(socket.assigns.steps, current_index - 1)

    {:noreply, assign(socket, :current_step, previous_step)}
  end

  def handle_info({:step_completed, {name, state}}, socket) do
    socket =
      socket
      |> refresh_steps(name, state)
      |> refresh_state()

    # Automatically proceed to next step for certain conditions
    socket =
      cond do
        # Auto-progress for template repository creation (not selection)
        name == :template_repository and Map.has_key?(state, :template_repo_name) ->
          current_index = Enum.find_index(socket.assigns.steps, &(&1.name == name))
          next_step = Enum.at(socket.assigns.steps, current_index + 1)
          assign(socket, :current_step, next_step)

        # Auto-progress for template files when finalized via "Next" button
        name == :template_files ->
          current_index = Enum.find_index(socket.assigns.steps, &(&1.name == name))
          next_step = Enum.at(socket.assigns.steps, current_index + 1)
          assign(socket, :current_step, next_step)

        true ->
          socket
      end

    {:noreply, socket}
  end

  def handle_info({:repo_tree, component_name, message}, socket) do
    send_update(component_module(component_name), id: component_name, repo_tree_message: message)
    {:noreply, socket}
  end

  def handle_info({:files, component_name, message}, socket) do
    send_update(component_module(component_name), id: component_name, files_message: message)
    {:noreply, socket}
  end

  def handle_info({:create_source_files, selected_files, template_repo}, socket) do
    send_update(Steps.Summary,
      id: :summary,
      repo_tree_message: {:create_source_files, selected_files, template_repo}
    )

    {:noreply, socket}
  end

  def handle_info({:push_to_github, source_files, template_repo}, socket) do
    send_update(Steps.Summary,
      id: :summary,
      repo_tree_message: {:push_to_github, source_files, template_repo}
    )

    {:noreply, socket}
  end

  def handle_info({:github_commit_result, status, source_files, template_repo}, socket) do
    if socket.assigns.current_step.name == :summary do
      send_update(Steps.Summary,
        id: socket.assigns.current_step.name,
        github_commit_result: {status, source_files, template_repo}
      )
    else
      Logger.warning(
        "Received :github_commit_result when current step is not Summary: #{inspect(socket.assigns.current_step.name)} - Status: #{inspect(status)}"
      )
    end

    {:noreply, socket}
  end

  def handle_info({:repository_created, repo_data}, socket) do
    # Forward repository creation events to the template repository step if it's active
    if socket.assigns.current_step.name == :template_repository do
      send_update(Steps.TemplateRepository,
        id: :template_repository,
        repository_created: repo_data
      )
    end

    {:noreply, socket}
  end

  def handle_info({:repository_files_updated, repository_id, metadata}, socket) do
    # Forward repository files updated events to the template files step if it's active
    if socket.assigns.current_step.name == :template_files do
      send_update(Steps.TemplateFiles,
        id: :template_files,
        repository_files_updated: {repository_id, metadata}
      )
    end

    {:noreply, socket}
  end

  # Handle Oban.Notifier messages for repository loading
  def handle_info(
        {:notification, channel,
         %{"event" => "repository_loading_progress", "progress" => progress, "message" => message}},
        socket
      )
      when is_atom(channel) do
    # Check if this is a repository loading channel
    if Atom.to_string(channel) |> String.starts_with?("repository_loading:") do
      # Forward repository loading progress to the template repository step if it's active
      if socket.assigns.current_step.name == :template_repository do
        send_update(Steps.TemplateRepository,
          id: :template_repository,
          repository_loading_progress: {progress, message}
        )
      end
    end

    {:noreply, socket}
  end

  def handle_info(
        {:notification, channel, %{"event" => "repository_loading_complete", "result" => result}},
        socket
      )
      when is_atom(channel) do
    # Check if this is a repository loading channel
    if Atom.to_string(channel) |> String.starts_with?("repository_loading:") do
      # Forward repository loading completion to the template repository step if it's active
      if socket.assigns.current_step.name == :template_repository do
        send_update(Steps.TemplateRepository,
          id: :template_repository,
          repository_loading_complete: result
        )
      end
    end

    {:noreply, socket}
  end

  # Handle Oban.Notifier messages for repository files loading
  def handle_info(
        {:notification, channel,
         %{"event" => "repository_files_progress", "progress" => progress, "message" => message}},
        socket
      )
      when is_atom(channel) do
    # Check if this is a repository files channel
    if Atom.to_string(channel) |> String.starts_with?("repository_files:") do
      # Forward repository files progress to the template files step if it's active
      if socket.assigns.current_step.name == :template_files do
        send_update(Steps.TemplateFiles,
          id: :template_files,
          repository_files_progress: {progress, message}
        )
      end
    end

    {:noreply, socket}
  end

  def handle_info(
        {:notification, channel, %{"event" => "repository_files_complete", "result" => result}},
        socket
      )
      when is_atom(channel) do
    # Check if this is a repository files channel
    if Atom.to_string(channel) |> String.starts_with?("repository_files:") do
      # Forward repository files completion to the template files step if it's active
      if socket.assigns.current_step.name == :template_files do
        send_update(Steps.TemplateFiles,
          id: :template_files,
          repository_files_complete: result
        )
      end
    end

    {:noreply, socket}
  end

  defp component_module(component_name) do
    Keyword.get(@steps, component_name)
  end

  defp refresh_steps(socket, name, state) do
    finishing_step = get_step(name, socket.assigns.steps)
    current_index = Enum.find_index(socket.assigns.steps, &(&1.name == name))

    updated_steps =
      List.replace_at(socket.assigns.steps, current_index, %{finishing_step | state: state})

    assign(socket, :steps, updated_steps)
  end

  defp refresh_state(socket) do
    assign(socket, :state, fresh_state(socket.assigns.steps))
  end

  defp get_step(name, steps) do
    Enum.find(steps, fn step -> step.name == name end)
  end

  defp build_steps do
    Enum.map(@steps, fn {name, component} ->
      %Step{name: name, component: component, state: %{}}
    end)
  end

  defp fresh_state(steps) do
    Enum.reduce(steps, %{}, fn step, acc -> Map.merge(acc, step.state) end)
  end
end
