defmodule RepobotWeb.Admin.RepositoryController do
  use RepobotWeb, :controller

  alias <PERSON>obot.{Repo, Repository}
  alias Repobot.Accounts.Organization
  import Ecto.Query

  plug :put_root_layout, html: {RepobotWeb.Layouts, :admin_root}
  plug :put_layout, html: {RepobotWeb.Layouts, :admin}

  def index(conn, params) do
    # Extract and prepare filters
    filters = %{
      organization_id: params["organization_id"]
    }

    # Build the query with filters and sorting
    query =
      from r in Repository,
        join: o in assoc(r, :organization),
        order_by: [asc: o.name, asc: r.name],
        preload: [organization: o]

    # Apply organization filter if present
    query =
      case filters.organization_id do
        nil -> query
        "" -> query
        org_id -> from [r, o] in query, where: r.organization_id == ^org_id
      end

    # Get organizations for the filter dropdown
    organizations =
      Organization
      |> select([:id, :name])
      |> order_by([o], o.name)
      |> Repo.all()

    # Paginate results
    page = Repo.paginate(query, params)

    render(conn, :index,
      repositories: page.entries,
      page: page,
      filters: filters,
      organizations: organizations
    )
  end

  def show(conn, %{"id" => id}) do
    repository =
      Repository
      |> Repo.get!(id)
      |> Repo.preload([:organization, :files])

    render(conn, :show, repository: repository)
  end

  def edit(conn, %{"id" => id}) do
    repository =
      Repository
      |> Repo.get!(id)
      |> Repo.preload([:organization])

    changeset = Repository.changeset(repository, %{})
    render(conn, :edit, repository: repository, changeset: changeset)
  end

  def update(conn, %{"id" => id, "repository" => repository_params}) do
    repository =
      Repository
      |> Repo.get!(id)
      |> Repo.preload([:organization])

    case repository
         |> Repository.changeset(repository_params)
         |> Repo.update() do
      {:ok, repository} ->
        conn
        |> put_flash(:info, "Repository updated successfully.")
        |> redirect(to: ~p"/admin/repositories/#{repository}")

      {:error, %Ecto.Changeset{} = changeset} ->
        render(conn, :edit, repository: repository, changeset: changeset)
    end
  end

  def delete(conn, %{"id" => id}) do
    repository = Repo.get!(Repository, id)
    {:ok, _repository} = Repo.delete(repository)

    conn
    |> put_flash(:info, "Repository deleted successfully.")
    |> redirect(to: ~p"/admin/repositories")
  end

  def preview_file(conn, %{"repository_id" => repository_id, "file_id" => file_id}) do
    repository =
      Repository
      |> Repo.get!(repository_id)
      |> Repo.preload([:organization])

    file = Repo.get!(Repobot.RepositoryFile, file_id)

    render(conn, :preview_file, repository: repository, file: file)
  end

  def fetch_file(conn, %{"repository_id" => repository_id, "file_id" => file_id}) do
    _repository =
      Repository
      |> Repo.get!(repository_id)
      |> Repo.preload([:organization])

    file = Repo.get!(Repobot.RepositoryFile, file_id)

    case Repobot.RepositoryFiles.fetch_file_content(file, conn.assigns.current_user) do
      {:ok, file} ->
        conn
        |> put_flash(:info, "File content fetched successfully.")
        |> redirect(to: ~p"/admin/repositories/#{repository_id}/files/#{file.id}")

      {:error, reason} ->
        conn
        |> put_flash(:error, "Failed to fetch file content: #{inspect(reason)}")
        |> redirect(to: ~p"/admin/repositories/#{repository_id}")
    end
  end
end
