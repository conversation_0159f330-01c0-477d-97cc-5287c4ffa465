defmodule RepobotWeb.Admin.UserController do
  use RepobotWeb, :controller

  alias <PERSON>obot.{Repo, Accounts}
  alias Accounts.User
  import Ecto.Query

  plug :put_root_layout, html: {RepobotWeb.Layouts, :admin_root}
  plug :put_layout, html: {RepobotWeb.Layouts, :admin}

  def index(conn, params) do
    page =
      User
      |> join(:left, [u], o in assoc(u, :default_organization))
      |> preload([u, o], default_organization: o)
      |> Repo.paginate(params)

    render(conn, :index, users: page.entries, page: page)
  end

  def show(conn, %{"id" => id}) do
    user =
      User
      |> Repo.get!(id)
      |> Repo.preload(:default_organization)

    render(conn, :show, user: user)
  end

  def edit(conn, %{"id" => id}) do
    user = Repo.get!(User, id) |> Repo.preload(:default_organization)
    changeset = Accounts.change_user(user)
    all_organizations = Accounts.list_organizations()

    render(conn, :edit, user: user, changeset: changeset, all_organizations: all_organizations)
  end

  def update(conn, %{"id" => id, "user" => user_params}) do
    user =
      User
      |> Repo.get!(id)
      |> Repo.preload(:default_organization)

    case Accounts.update_user(user, user_params) do
      {:ok, user} ->
        conn
        |> put_flash(:info, "User updated successfully.")
        |> redirect(to: ~p"/admin/users/#{user}")

      {:error, %Ecto.Changeset{} = changeset} ->
        render(conn, :edit, user: user, changeset: changeset)
    end
  end

  def delete(conn, %{"id" => id}) do
    user =
      User
      |> Repo.get!(id)
      |> Repo.preload(:default_organization)

    result =
      Repo.transaction(fn ->
        if org = user.default_organization do
          Repo.delete!(org)
        end

        Repo.delete!(user)
      end)

    case result do
      {:ok, _} ->
        conn
        |> put_flash(:info, "User and their default organization deleted successfully.")
        |> redirect(to: ~p"/admin/users")

      {:error, _reason} ->
        conn
        |> put_flash(:error, "Failed to delete user and their default organization.")
        # Redirect back to the user show page or index page if deletion fails
        |> redirect(to: ~p"/admin/users/#{user}")
    end
  end
end
