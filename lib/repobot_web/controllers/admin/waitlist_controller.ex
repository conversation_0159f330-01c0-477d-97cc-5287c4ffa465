defmodule RepobotWeb.Admin.WaitlistController do
  use RepobotWeb, :controller

  alias <PERSON>obot.Waitlist

  plug :put_root_layout, html: {RepobotWeb.Layouts, :admin_root}
  plug :put_layout, html: {RepobotWeb.Layouts, :admin}

  def index(conn, _params) do
    entries = Waitlist.list_entries()
    render(conn, :index, entries: entries)
  end

  def generate_code(conn, %{"waitlist_id" => id}) do
    entry = Waitlist.get_entry!(id)

    case Waitlist.generate_invitation_code(entry) do
      {:ok, updated_entry} ->
        invite_url = Waitlist.get_invite_url(updated_entry)

        conn
        |> put_flash(:info, "Invitation code generated. Invite URL: #{invite_url}")
        |> redirect(to: ~p"/admin/waitlist")

      {:error, _changeset} ->
        conn
        |> put_flash(:error, "Failed to generate invitation code")
        |> redirect(to: ~p"/admin/waitlist")
    end
  end

  def delete(conn, %{"id" => id}) do
    entry = Waitlist.get_entry!(id)

    case Waitlist.delete_entry(entry) do
      {:ok, _entry} ->
        conn
        |> put_flash(:info, "Waitlist entry deleted successfully.")
        |> redirect(to: ~p"/admin/waitlist")

      {:error, _changeset} ->
        conn
        |> put_flash(:error, "Failed to delete waitlist entry.")
        |> redirect(to: ~p"/admin/waitlist")
    end
  end
end
