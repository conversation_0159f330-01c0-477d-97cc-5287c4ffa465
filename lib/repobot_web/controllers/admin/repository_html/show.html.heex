<div class="p-6">
  <nav class="mb-8">
    <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
      <li>
        <.link navigate={~p"/admin/repositories"} class="hover:text-indigo-600">
          Repositories
        </.link>
      </li>
      <li>•</li>
      <li class="font-medium text-slate-900">{@repository.name}</li>
    </ol>
  </nav>

  <div class="mb-8 flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-semibold text-slate-900">{@repository.name}</h1>
      <p class="mt-2 text-sm text-slate-600">Repository details and information.</p>
    </div>
    <div class="flex gap-2">
      <.btn
        href={~p"/admin/repositories/#{@repository}/edit"}
        data-phx-link="redirect"
        data-phx-link-state="push"
        variant="primary"
        class="bg-indigo-600 hover:bg-indigo-700"
      >
        Edit Repository
      </.btn>
      <.btn
        href={~p"/admin/repositories/#{@repository}"}
        method="delete"
        data-confirm="Are you sure you want to delete this repository?"
        variant="error"
        class="bg-red-600 hover:bg-red-700"
      >
        Delete Repository
      </.btn>
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">Basic Information</h2>
      <dl class="space-y-4">
        <div>
          <dt class="text-sm font-medium text-slate-500">Name</dt>
          <dd class="mt-1 text-sm text-slate-900">{@repository.name}</dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Full Name</dt>
          <dd class="mt-1 text-sm text-slate-900">{@repository.full_name}</dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Owner</dt>
          <dd class="mt-1 text-sm text-slate-900">{@repository.owner}</dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Language</dt>
          <dd class="mt-1 text-sm text-slate-900">{@repository.language}</dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Private</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <%= if @repository.private do %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Yes
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                No
              </span>
            <% end %>
          </dd>
        </div>
      </dl>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">Additional Information</h2>
      <dl class="space-y-4">
        <div>
          <dt class="text-sm font-medium text-slate-500">Organization</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <.link
              navigate={~p"/admin/organizations/#{@repository.organization_id}"}
              class="text-indigo-600 hover:text-indigo-900"
            >
              {@repository.organization.name}
            </.link>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Fork</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <%= if @repository.fork do %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Yes
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                No
              </span>
            <% end %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Template</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <%= if @repository.template do %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Yes
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                No
              </span>
            <% end %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Created</dt>
          <dd class="mt-1 text-sm text-slate-900">
            {Calendar.strftime(@repository.inserted_at, "%Y-%m-%d %H:%M:%S")}
          </dd>
        </div>
      </dl>
    </div>
  </div>

  <div class="mt-8">
    <h2 class="text-lg font-medium text-slate-900 mb-4">Repository Files</h2>
    <div class="overflow-hidden rounded-lg border border-slate-200">
      <table class="min-w-full divide-y divide-slate-200">
        <thead>
          <tr class="bg-slate-50">
            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
              Name
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
              Path
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
              Type
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
              Size
            </th>
            <th class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-slate-200">
          <%= for file <- @repository.files do %>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-slate-900">
                  {file.name}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-slate-900">
                  {file.path}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-slate-900">
                  {file.type}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-slate-900">
                  {file.size}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end gap-2">
                  <%= if file.content do %>
                    <.link
                      navigate={~p"/admin/repositories/#{@repository.id}/files/#{file.id}"}
                      class="text-indigo-600 hover:text-indigo-900"
                    >
                      Preview
                    </.link>
                  <% else %>
                    <.link
                      href={~p"/admin/repositories/#{@repository.id}/files/#{file.id}/fetch"}
                      class="text-indigo-600 hover:text-indigo-900"
                    >
                      Fetch Content
                    </.link>
                  <% end %>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>
</div>
