<div class="p-6">
  <nav class="mb-8">
    <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
      <li>
        <.link navigate={~p"/admin/repositories"} class="hover:text-indigo-600">
          Repositories
        </.link>
      </li>
      <li>•</li>
      <li>
        <.link navigate={~p"/admin/repositories/#{@repository.id}"} class="hover:text-indigo-600">
          {@repository.name}
        </.link>
      </li>
      <li>•</li>
      <li class="font-medium text-slate-900">{@file.name}</li>
    </ol>
  </nav>

  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-slate-900">File Preview</h1>
    <p class="mt-2 text-sm text-slate-600">
      Viewing content of {String.trim_leading(@file.path, "/")}
    </p>
  </div>

  <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
    <div class="flex justify-between items-center mb-4">
      <div>
        <h2 class="text-lg font-medium text-slate-900">{@file.name}</h2>
        <p class="text-sm text-slate-500">{@file.size} bytes • {String.upcase(@file.type)}</p>
      </div>
      <.btn
        href={~p"/admin/repositories/#{@repository.id}"}
        data-phx-link="redirect"
        data-phx-link-state="push"
        variant="outline"
        class="border-slate-300 text-slate-700 bg-white hover:bg-slate-50"
      >
        Back to Repository
      </.btn>
    </div>

    <%= if @file.content do %>
      <div class="mt-4">
        <pre class="bg-slate-50 p-4 rounded-md overflow-x-auto text-sm font-mono"><%= @file.content %></pre>
      </div>
    <% else %>
      <div class="mt-4 text-center py-12 text-slate-500">
        <p>No content available.</p>
        <.link
          href={~p"/admin/repositories/#{@repository.id}/files/#{@file.id}/fetch"}
          class="inline-flex items-center px-4 py-2 mt-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
        >
          Fetch Content
        </.link>
      </div>
    <% end %>
  </div>
</div>
