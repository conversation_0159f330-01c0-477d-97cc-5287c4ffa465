defmodule RepobotWeb.Admin.OrganizationController do
  use RepobotWeb, :controller

  alias <PERSON>obot.{Repo, Accounts}
  alias Accounts.Organization
  import Ecto.Query

  plug :put_root_layout, html: {RepobotWeb.Layouts, :admin_root}
  plug :put_layout, html: {RepobotWeb.Layouts, :admin}

  def index(conn, params) do
    page =
      Organization
      |> preload([:settings])
      |> Repo.paginate(params)

    render(conn, :index, organizations: page.entries, page: page)
  end

  def show(conn, %{"id" => id}) do
    organization =
      Organization
      |> Repo.get!(id)
      |> Repo.preload([
        :settings,
        :default_users,
        :users
      ])

    # Fetch all users
    all_users = Accounts.list_users()

    render(conn, :show, organization: organization, all_users: all_users)
  end

  def edit(conn, %{"id" => id}) do
    organization =
      Organization
      |> Repo.get!(id)
      |> Repo.preload([:settings])

    changeset = Accounts.change_organization(organization)
    render(conn, :edit, organization: organization, changeset: changeset)
  end

  def update(conn, %{"id" => id, "organization" => organization_params}) do
    organization =
      Organization
      |> Repo.get!(id)
      |> Repo.preload([:settings])

    case Accounts.update_organization(organization, organization_params) do
      {:ok, organization} ->
        conn
        |> put_flash(:info, "Organization updated successfully.")
        |> redirect(to: ~p"/admin/organizations/#{organization}")

      {:error, %Ecto.Changeset{} = changeset} ->
        render(conn, :edit, organization: organization, changeset: changeset)
    end
  end

  def delete(conn, %{"id" => id}) do
    organization = Repo.get!(Organization, id)
    {:ok, _organization} = Repo.delete(organization)

    conn
    |> put_flash(:info, "Organization deleted successfully.")
    |> redirect(to: ~p"/admin/organizations")
  end

  def add_member(conn, %{
        "organization_id" => organization_id,
        "member" => %{"user_id" => user_id, "role" => role}
      }) do
    organization = Repo.get!(Organization, organization_id)
    user = Repo.get!(Accounts.User, user_id)

    case Accounts.add_user_to_organization(user, organization, role) do
      {:ok, _user_organization} ->
        conn
        |> put_flash(:info, "Member added successfully.")
        |> redirect(to: ~p"/admin/organizations/#{organization}")

      {:error, changeset} ->
        # Handle potential errors, e.g., user already exists
        # Use traverse_errors to check for the specific unique constraint message
        is_duplicate =
          Ecto.Changeset.traverse_errors(changeset, fn {msg, _opts} ->
            String.contains?(msg, "has already been taken")
          end)
          |> Enum.any?()

        error_message =
          if is_duplicate do
            "User is already a member of this organization."
          else
            "Failed to add member: #{inspect(changeset.errors)}"
          end

        conn
        |> put_flash(:error, error_message)
        |> redirect(to: ~p"/admin/organizations/#{organization}")
    end
  rescue
    Ecto.NoResultsError ->
      conn
      |> put_flash(:error, "User or Organization not found.")
      |> redirect(to: ~p"/admin/organizations/#{organization_id}")
  end

  def remove_member(conn, %{"organization_id" => organization_id, "user_id" => user_id}) do
    organization = Repo.get!(Organization, organization_id)
    user = Repo.get!(Accounts.User, user_id)

    case Accounts.remove_user_from_organization(user, organization) do
      {:ok, _user_organization} ->
        conn
        |> put_flash(:info, "Member removed successfully.")
        |> redirect(to: ~p"/admin/organizations/#{organization}")

      {:error, :not_found} ->
        conn
        |> put_flash(:error, "Member not found in this organization.")
        |> redirect(to: ~p"/admin/organizations/#{organization}")

      # Catch potential delete errors
      {:error, _changeset} ->
        conn
        |> put_flash(:error, "Failed to remove member.")
        |> redirect(to: ~p"/admin/organizations/#{organization}")
    end
  rescue
    Ecto.NoResultsError ->
      conn
      |> put_flash(:error, "User or Organization not found.")
      |> redirect(to: ~p"/admin/organizations/#{organization_id}")
  end
end
