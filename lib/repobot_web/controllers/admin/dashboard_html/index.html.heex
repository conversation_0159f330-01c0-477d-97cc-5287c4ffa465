<div class="p-6">
  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-slate-900">Dashboard</h1>
    <p class="mt-2 text-sm text-slate-600">Overview of your Repobot instance.</p>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <div class="text-sm font-medium text-slate-500">Total Users</div>
      <div class="mt-2 text-3xl font-semibold text-slate-900">{@stats.total_users}</div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <div class="text-sm font-medium text-slate-500">Total Organizations</div>
      <div class="mt-2 text-3xl font-semibold text-slate-900">{@stats.total_organizations}</div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <div class="text-sm font-medium text-slate-500">Total Repositories</div>
      <div class="mt-2 text-3xl font-semibold text-slate-900">{@stats.total_repositories}</div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <div class="text-sm font-medium text-slate-500">Total Source Files</div>
      <div class="mt-2 text-3xl font-semibold text-slate-900">{@stats.total_source_files}</div>
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">Recent Users</h2>
      <div class="divide-y divide-slate-200">
        <%= for user <- @stats.recent_users do %>
          <div class="py-3 flex items-center gap-3">
            <img src={user.info.avatar_url} alt={user.login} class="h-8 w-8 rounded-full" />
            <div>
              <div class="font-medium text-slate-900">{user.info.name || user.login}</div>
              <div class="text-sm text-slate-500">@{user.login}</div>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">Recent Organizations</h2>
      <div class="divide-y divide-slate-200">
        <%= for org <- @stats.recent_organizations do %>
          <div class="py-3">
            <div class="font-medium text-slate-900">{org.name}</div>
            <div class="text-sm text-slate-500">
              <%= if org.private_repos do %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-slate-100 text-slate-800">
                  Private Repos
                </span>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
