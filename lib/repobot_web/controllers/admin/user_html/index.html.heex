<div class="p-6">
  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-slate-900">Users</h1>
    <p class="mt-2 text-sm text-slate-600">Manage users of your Repobot instance.</p>
  </div>

  <div class="overflow-hidden rounded-lg border border-slate-200">
    <table class="min-w-full divide-y divide-slate-200">
      <thead>
        <tr class="bg-slate-50">
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            User
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Email
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Default Organization
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Joined
          </th>
          <th class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-slate-200">
        <%= for user <- @users do %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="h-10 w-10 flex-shrink-0">
                  <img class="h-10 w-10 rounded-full" src={user.info.avatar_url} alt={user.login} />
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-slate-900">
                    {user.info.name || user.login}
                  </div>
                  <div class="text-sm text-slate-500">
                    @{user.login}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-slate-900">{user.info.email}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-slate-900">
                <%= if user.default_organization do %>
                  {user.default_organization.name}
                <% else %>
                  <span class="text-slate-500">None</span>
                <% end %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
              {Calendar.strftime(user.inserted_at, "%Y-%m-%d")}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end gap-2">
                <.link
                  navigate={~p"/admin/users/#{user}"}
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  View
                </.link>
                <.link
                  navigate={~p"/admin/users/#{user}/edit"}
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  Edit
                </.link>
                <.link
                  href={~p"/admin/users/#{user}"}
                  method="delete"
                  data-confirm="Are you sure you want to delete this user?"
                  class="text-red-600 hover:text-red-900"
                >
                  Delete
                </.link>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <div class="mt-6 flex items-center justify-between">
    <div class="text-sm text-slate-500">
      Showing {length(@users)} of {@page.total_entries} users
    </div>
    <div class="flex gap-2">
      <%= if @page.page_number > 1 do %>
        <.link
          navigate={~p"/admin/users?page=#{@page.page_number - 1}"}
          class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50"
        >
          Previous
        </.link>
      <% end %>

      <%= if @page.page_number < @page.total_pages do %>
        <.link
          navigate={~p"/admin/users?page=#{@page.page_number + 1}"}
          class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50"
        >
          Next
        </.link>
      <% end %>
    </div>
  </div>
</div>
