defmodule RepobotWeb.Admin.EventController do
  use RepobotWeb, :controller

  alias Repobot.Events.Event
  alias Repobot.Repo

  import Ecto.Query

  plug :put_root_layout, html: {RepobotWeb.Layouts, :admin_root}
  plug :put_layout, html: {RepobotWeb.Layouts, :admin}

  @items_per_page 20

  def index(conn, params) do
    page = String.to_integer(params["page"] || "1")

    # Apply filters from params
    filters = %{
      type: params["type"],
      organization_id: params["organization_id"],
      repository_id: params["repository_id"]
    }

    # Get paginated events with filters
    {events, total_count} = fetch_events(page, filters)
    total_pages = max(ceil(total_count / @items_per_page), 1)

    # Prepare filter options
    filter_options = %{
      types: fetch_event_types(),
      organizations: fetch_organizations(),
      repositories: fetch_repositories()
    }

    render(conn, :index,
      events: events,
      total_events: total_count,
      page: page,
      total_pages: total_pages,
      filters: filters,
      filter_options: filter_options
    )
  end

  def show(conn, %{"id" => id}) do
    event =
      Event
      |> Repo.get(id)
      |> Repo.preload([:organization, :repository])

    render(conn, :show, event: event)
  end

  # Fetch events with pagination and filtering
  defp fetch_events(page, filters) do
    query =
      from(e in Event,
        order_by: [desc: e.inserted_at]
      )

    # Apply filters if provided
    query = apply_filters(query, filters)

    # Count total events with filters
    total_count = Repo.aggregate(query, :count, :id)

    # Apply pagination
    query =
      query
      |> offset(^((page - 1) * @items_per_page))
      |> limit(@items_per_page)
      |> preload([:organization, :repository])

    events = Repo.all(query)

    {events, total_count}
  end

  # Apply filters to query if they're not nil
  defp apply_filters(query, filters) do
    Enum.reduce(filters, query, fn
      {:type, nil}, query -> query
      {:type, ""}, query -> query
      {:type, type}, query -> from e in query, where: e.type == ^type
      {:organization_id, nil}, query -> query
      {:organization_id, ""}, query -> query
      {:organization_id, org_id}, query -> from e in query, where: e.organization_id == ^org_id
      {:repository_id, nil}, query -> query
      {:repository_id, ""}, query -> query
      {:repository_id, repo_id}, query -> from e in query, where: e.repository_id == ^repo_id
      _, query -> query
    end)
  end

  # Fetch unique event types for filtering
  defp fetch_event_types do
    Event
    |> distinct(true)
    |> select([:type])
    |> Repo.all()
    |> Enum.map(& &1.type)
    |> Enum.sort()
  end

  # Fetch organizations for filtering
  defp fetch_organizations do
    Repobot.Accounts.Organization
    |> select([:id, :name])
    |> Repo.all()
  end

  # Fetch repositories for filtering
  defp fetch_repositories do
    Repobot.Repository
    |> select([:id, :full_name])
    |> Repo.all()
  end
end
