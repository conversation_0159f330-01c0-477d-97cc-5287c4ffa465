<div class="p-6">
  <nav class="mb-8">
    <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
      <li>
        <.link navigate={~p"/admin/organizations"} class="hover:text-indigo-600">
          Organizations
        </.link>
      </li>
      <li>•</li>
      <li class="font-medium text-slate-900">{@organization.name}</li>
    </ol>
  </nav>

  <div class="mb-8 flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-semibold text-slate-900">{@organization.name}</h1>
      <p class="mt-2 text-sm text-slate-600">Organization details and information.</p>
    </div>
    <div class="flex gap-2">
      <.btn
        href={~p"/admin/organizations/#{@organization}/edit"}
        data-phx-link="redirect"
        data-phx-link-state="push"
        variant="primary"
      >
        Edit Organization
      </.btn>
      <.btn
        href={~p"/admin/organizations/#{@organization}"}
        method="delete"
        data-confirm="Are you sure you want to delete this organization?"
        variant="error"
      >
        Delete Organization
      </.btn>
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">Organization Information</h2>
      <div class="flex items-center gap-4 mb-6">
        <%= if @organization.settings.avatar_url do %>
          <img
            src={@organization.settings.avatar_url}
            alt={@organization.name}
            class="h-16 w-16 rounded-full"
          />
        <% else %>
          <div class="h-16 w-16 rounded-full bg-slate-200 flex items-center justify-center">
            <.icon name="hero-building-office" class="h-8 w-8 text-slate-500" />
          </div>
        <% end %>
        <div>
          <div class="font-medium text-slate-900">{@organization.name}</div>
          <%= if @organization.settings.html_url do %>
            <div class="text-sm text-slate-500">
              <a
                href={@organization.settings.html_url}
                target="_blank"
                class="hover:text-indigo-600"
              >
                View on GitHub
              </a>
            </div>
          <% end %>
        </div>
      </div>
      <dl class="space-y-4">
        <div>
          <dt class="text-sm font-medium text-slate-500">Installation ID</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <%= if @organization.installation_id do %>
              {@organization.installation_id}
            <% else %>
              <span class="text-slate-500">Not installed</span>
            <% end %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Private Repositories</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <%= if @organization.private_repos do %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Enabled
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                Disabled
              </span>
            <% end %>
          </dd>
        </div>
      </dl>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">API Settings</h2>
      <dl class="space-y-4">
        <div>
          <dt class="text-sm font-medium text-slate-500">Anthropic API Key</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <%= if @organization.settings.anthropic_api_key do %>
              <span class="text-green-600">Configured</span>
            <% else %>
              <span class="text-slate-500">Not configured</span>
            <% end %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">OpenAI API Key</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <%= if @organization.settings.openai_api_key do %>
              <span class="text-green-600">Configured</span>
            <% else %>
              <span class="text-slate-500">Not configured</span>
            <% end %>
          </dd>
        </div>
      </dl>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">Default Users</h2>
      <div class="divide-y divide-slate-200">
        <%= if Enum.any?(@organization.default_users) do %>
          <%= for user <- @organization.default_users do %>
            <div class="py-3 flex items-center gap-3">
              <img src={user.info.avatar_url} alt={user.login} class="h-8 w-8 rounded-full" />
              <div>
                <div class="font-medium text-slate-900">{user.info.name || user.login}</div>
                <div class="text-sm text-slate-500">@{user.login}</div>
              </div>
            </div>
          <% end %>
        <% else %>
          <div class="py-3 text-sm text-slate-500">
            No users have this organization set as their default
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <div class="mt-8">
    <div class="bg-white rounded-lg shadow-sm border border-slate-200">
      <div class="p-6">
        <h2 class="text-lg font-medium text-slate-900 mb-4">Organization Members</h2>
        <div class="overflow-hidden">
          <table class="min-w-full divide-y divide-slate-200">
            <thead>
              <tr class="bg-slate-50">
                <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  User
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Role
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Default Org
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-slate-200">
              <%= for user <- @organization.users do %>
                <% user_org =
                  Repobot.Repo.get_by(Repobot.Accounts.UserOrganization,
                    user_id: user.id,
                    organization_id: @organization.id
                  ) %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center gap-3">
                      <%= if user.info.avatar_url do %>
                        <img
                          src={user.info.avatar_url}
                          alt={user.login}
                          class="h-8 w-8 rounded-full"
                        />
                      <% else %>
                        <div class="h-8 w-8 rounded-full bg-slate-200 flex items-center justify-center">
                          <.icon name="hero-user" class="h-4 w-4 text-slate-500" />
                        </div>
                      <% end %>
                      <div>
                        <div class="font-medium text-slate-900">
                          {user.info.name || user.login}
                          <span class="text-xs text-slate-400 ml-1">(ID: {user.id})</span>
                        </div>
                        <div class="text-sm text-slate-500">@{user.login}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                    {if user_org, do: user_org.role, else: "N/A"}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <%= if user.default_organization_id == @organization.id do %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Yes
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                        No
                      </span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <.link
                      href={~p"/admin/organizations/#{@organization}/members/#{user.id}"}
                      method="delete"
                      data-confirm="Are you sure you want to remove this member?"
                      class="text-red-600 hover:text-red-900"
                    >
                      Remove
                    </.link>
                  </td>
                </tr>
              <% end %>
              <%= if Enum.empty?(@organization.users) do %>
                <tr>
                  <td colspan="4" class="px-6 py-4 text-sm text-slate-500 text-center">
                    No members found
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
      <div class="p-6 bg-slate-50 border-t border-slate-200">
        <h3 class="text-lg font-medium text-slate-900 mb-4">Add New Member</h3>
        <.form
          for={%{}}
          action={~p"/admin/organizations/#{@organization}/members"}
          method="post"
          class="flex items-end gap-4"
        >
          <div class="flex-grow">
            <% user_options =
              Enum.map(@all_users, fn u -> {"#{u.info.name || u.login} (@#{u.login})", u.id} end) %>
            <.input
              name="member[user_id]"
              type="select"
              label="User"
              options={user_options}
              value={hd(user_options) |> elem(1)}
              required
            />
          </div>
          <div>
            <.input
              name="member[role]"
              type="select"
              label="Role"
              options={["member", "admin"]}
              value="member"
              required
            />
          </div>
          <.btn type="submit" variant="primary">
            Add Member
          </.btn>
        </.form>
      </div>
    </div>
  </div>
</div>
