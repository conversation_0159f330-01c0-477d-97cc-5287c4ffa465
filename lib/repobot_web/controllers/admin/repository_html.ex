defmodule RepobotWeb.Admin.RepositoryHTML do
  use RepobotWeb, :html

  embed_templates "repository_html/*"

  @doc """
  Builds query params for pagination, preserving existing filters.
  """
  def build_pagination_params(filters, page_number) do
    params = %{"page" => page_number}

    params =
      if filters.organization_id && filters.organization_id != "" do
        Map.put(params, "organization_id", filters.organization_id)
      else
        params
      end

    URI.encode_query(params)
  end
end
