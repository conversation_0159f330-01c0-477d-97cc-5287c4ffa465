<div class="p-6">
  <nav class="mb-8">
    <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
      <li>
        <.link navigate={~p"/admin/source-files"} class="hover:text-indigo-600">
          Source Files
        </.link>
      </li>
      <li>•</li>
      <li class="font-medium text-slate-900">{@source_file.name}</li>
    </ol>
  </nav>

  <div class="mb-8 flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-semibold text-slate-900">{@source_file.name}</h1>
      <p class="mt-2 text-sm text-slate-600">Source file details and information.</p>
    </div>
    <div class="flex gap-2">
      <.btn
        href={~p"/admin/source-files/#{@source_file}/edit"}
        data-phx-link="redirect"
        data-phx-link-state="push"
        variant="primary"
      >
        Edit Source File
      </.btn>
      <.btn
        href={~p"/admin/source-files/#{@source_file}"}
        method="delete"
        data-confirm="Are you sure you want to delete this source file?"
        variant="error"
      >
        Delete Source File
      </.btn>
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">Basic Information</h2>
      <dl class="space-y-4">
        <div>
          <dt class="text-sm font-medium text-slate-500">Name</dt>
          <dd class="mt-1 text-sm text-slate-900">{@source_file.name}</dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Target Path</dt>
          <dd class="mt-1 text-sm text-slate-900">{@source_file.target_path}</dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Template</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <%= if @source_file.is_template do %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Yes
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                No
              </span>
            <% end %>
          </dd>
        </div>
      </dl>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">Additional Information</h2>
      <dl class="space-y-4">
        <%= if @source_file.organization.name do %>
          <div>
            <dt class="text-sm font-medium text-slate-500">Organization</dt>
            <dd class="mt-1 text-sm text-slate-900">
              <.link
                navigate={~p"/admin/organizations/#{@source_file.organization_id}"}
                class="text-indigo-600 hover:text-indigo-900"
              >
                {@source_file.organization.name}
              </.link>
            </dd>
          </div>
        <% end %>
        <%= if @source_file.source_repository_id do %>
          <div>
            <dt class="text-sm font-medium text-slate-500">Source Repository</dt>
            <dd class="mt-1 text-sm text-slate-900">
              <.link
                navigate={~p"/admin/repositories/#{@source_file.source_repository_id}"}
                class="text-indigo-600 hover:text-indigo-900"
              >
                View Repository
              </.link>
            </dd>
          </div>
        <% end %>
        <div>
          <dt class="text-sm font-medium text-slate-500">Created</dt>
          <dd class="mt-1 text-sm text-slate-900">
            {Calendar.strftime(@source_file.inserted_at, "%Y-%m-%d %H:%M:%S")}
          </dd>
        </div>
      </dl>
    </div>

    <div class="col-span-2 bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">Content</h2>
      <pre class="mt-2 p-4 bg-slate-50 rounded-lg overflow-x-auto text-sm text-slate-800">
        <code>{@source_file.content}</code>
      </pre>
    </div>
  </div>
</div>
