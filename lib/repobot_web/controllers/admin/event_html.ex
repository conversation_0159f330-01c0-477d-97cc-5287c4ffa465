defmodule RepobotWeb.Admin.EventHTML do
  use RepobotWeb, :html

  embed_templates "event_html/*"

  # Format timestamp for display
  def format_datetime(datetime) do
    case datetime do
      %DateTime{} ->
        datetime
        |> DateTime.truncate(:second)
        |> Calendar.strftime("%Y-%m-%d %H:%M:%S UTC")

      _ ->
        ""
    end
  end

  # Truncate the payload for display
  def truncate_payload(payload) when is_map(payload) do
    json = Jason.encode!(payload, pretty: false)

    if String.length(json) > 100 do
      String.slice(json, 0, 100) <> "..."
    else
      json
    end
  end

  def truncate_payload(_), do: ""

  # Format status with badge color
  def status_badge_color(status) do
    case status do
      "success" -> "bg-green-100 text-green-800"
      "failed" -> "bg-red-100 text-red-800"
      "pending" -> "bg-yellow-100 text-yellow-800"
      _ -> "bg-gray-100 text-gray-800"
    end
  end
end
