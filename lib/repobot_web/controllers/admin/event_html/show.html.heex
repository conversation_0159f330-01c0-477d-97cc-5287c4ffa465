<div class="p-6">
  <div class="mb-6 flex justify-between items-center">
    <div class="flex items-center">
      <a
        href={~p"/admin/events"}
        class="inline-flex items-center mr-4 text-sm text-indigo-600 hover:text-indigo-900"
      >
        <.icon name="hero-arrow-left" class="h-4 w-4 mr-1" /> Back to Events
      </a>
      <h1 class="text-2xl font-semibold text-gray-900">Event Details</h1>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow p-6">
    <!-- Event metadata -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      <div>
        <div class="mb-4">
          <h2 class="text-sm font-medium text-gray-500">ID</h2>
          <p class="mt-1 text-sm text-gray-900">{@event.id}</p>
        </div>

        <div class="mb-4">
          <h2 class="text-sm font-medium text-gray-500">Type</h2>
          <p class="mt-1 text-sm text-gray-900">{@event.type}</p>
        </div>

        <div class="mb-4">
          <h2 class="text-sm font-medium text-gray-500">Status</h2>
          <p class="mt-1">
            <%= if @event.status do %>
              <span class={"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium #{status_badge_color(@event.status)}"}>
                {@event.status}
              </span>
            <% else %>
              <span class="text-sm text-gray-500">-</span>
            <% end %>
          </p>
        </div>
      </div>

      <div>
        <div class="mb-4">
          <h2 class="text-sm font-medium text-gray-500">Organization</h2>
          <p class="mt-1 text-sm text-gray-900">
            <%= if @event.organization do %>
              {@event.organization.name}
            <% else %>
              <span class="text-gray-500">-</span>
            <% end %>
          </p>
        </div>

        <div class="mb-4">
          <h2 class="text-sm font-medium text-gray-500">Repository</h2>
          <p class="mt-1 text-sm text-gray-900">
            <%= if @event.repository do %>
              {@event.repository.full_name}
            <% else %>
              <span class="text-gray-500">-</span>
            <% end %>
          </p>
        </div>

        <div class="mb-4">
          <h2 class="text-sm font-medium text-gray-500">Timestamp</h2>
          <p class="mt-1 text-sm text-gray-900">{format_datetime(@event.inserted_at)}</p>
        </div>
      </div>
    </div>
    
<!-- Payload data -->
    <div>
      <h2 class="text-lg font-medium text-gray-900 mb-4">Payload</h2>
      <div class="bg-gray-50 p-4 rounded-lg overflow-x-auto">
        <pre class="text-sm text-gray-900"><%= Jason.encode!(@event.payload, pretty: true) %></pre>
      </div>
    </div>
  </div>
</div>
