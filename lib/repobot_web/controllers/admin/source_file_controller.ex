defmodule RepobotWeb.Admin.SourceFileController do
  use RepobotWeb, :controller

  alias <PERSON>obot.{Repo, SourceFile}
  import Ecto.Query

  plug :put_root_layout, html: {RepobotWeb.Layouts, :admin_root}
  plug :put_layout, html: {RepobotWeb.Layouts, :admin}

  def index(conn, params) do
    page =
      SourceFile
      |> preload([:organization])
      |> Repo.paginate(params)

    render(conn, :index, source_files: page.entries, page: page)
  end

  def show(conn, %{"id" => id}) do
    source_file =
      SourceFile
      |> Repo.get!(id)
      |> Repo.preload([:organization])

    render(conn, :show, source_file: source_file)
  end

  def edit(conn, %{"id" => id}) do
    source_file =
      SourceFile
      |> Repo.get!(id)
      |> Repo.preload([:organization])

    changeset = SourceFile.changeset(source_file, %{})
    render(conn, :edit, source_file: source_file, changeset: changeset)
  end

  def update(conn, %{"id" => id, "source_file" => source_file_params}) do
    source_file =
      SourceFile
      |> Repo.get!(id)
      |> Repo.preload([:organization])

    case source_file
         |> SourceFile.changeset(source_file_params)
         |> Repo.update() do
      {:ok, source_file} ->
        conn
        |> put_flash(:info, "Source file updated successfully.")
        |> redirect(to: ~p"/admin/source-files/#{source_file}")

      {:error, %Ecto.Changeset{} = changeset} ->
        render(conn, :edit, source_file: source_file, changeset: changeset)
    end
  end

  def delete(conn, %{"id" => id}) do
    source_file = Repo.get!(SourceFile, id)
    {:ok, _source_file} = Repo.delete(source_file)

    conn
    |> put_flash(:info, "Source file deleted successfully.")
    |> redirect(to: ~p"/admin/source-files")
  end
end
