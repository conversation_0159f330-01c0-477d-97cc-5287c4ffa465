defmodule RepobotWeb.Admin.DashboardController do
  use RepobotWeb, :controller

  alias <PERSON>obot.{Repo, Accounts, Repository, SourceFile}
  import Ecto.Query

  plug :put_root_layout, html: {RepobotWeb.Layouts, :admin_root}
  plug :put_layout, html: {RepobotWeb.Layouts, :admin}

  def index(conn, _params) do
    stats = %{
      total_users: Repo.aggregate(Accounts.User, :count),
      total_organizations: Repo.aggregate(Accounts.Organization, :count),
      total_repositories: Repo.aggregate(Repository, :count),
      total_source_files: Repo.aggregate(SourceFile, :count),
      recent_users:
        Accounts.User
        |> order_by(desc: :inserted_at)
        |> limit(5)
        |> Repo.all(),
      recent_organizations:
        Accounts.Organization
        |> order_by(desc: :inserted_at)
        |> limit(5)
        |> Repo.all()
    }

    render(conn, :index, stats: stats)
  end
end
