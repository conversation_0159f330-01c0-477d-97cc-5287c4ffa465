defmodule RepobotWeb.AuthController do
  use Repobot<PERSON>eb, :controller

  require Logger

  plug Ueberauth

  alias <PERSON><PERSON><PERSON>.Accounts
  alias Repobot.Accounts.User

  def callback(%{assigns: %{ueberauth_auth: auth}} = conn, _params) do
    github_login = auth.info.nickname

    Logger.debug("Processing GitHub callback",
      user_login: github_login,
      event: "auth.callback.start"
    )

    case Accounts.get_user_by_login(github_login) do
      # --- Existing User: Sign-in Flow ---
      %User{} = existing_user ->
        Logger.debug("Found existing user",
          user_id: existing_user.id,
          user_login: github_login,
          event: "auth.existing_user"
        )

        case Accounts.user_from_auth(auth) do
          {:ok, user} ->
            Logger.info("Successfully signed in user",
              user_id: user.id,
              user_login: user.login,
              event: "auth.signin.success",
              organization_id: user.default_organization_id
            )

            # Always redirect to dashboard for now as onboarding is not ready
            redirect_path = ~p"/dashboard"

            conn
            # Clear any stale code, just in case
            |> put_flash(:info, "Successfully signed in.")
            |> put_session(:current_user_id, user.id)
            |> configure_session(renew: true)
            |> redirect(to: redirect_path)

          {:error, reason} ->
            Logger.error("Failed to sign in user",
              user_login: github_login,
              event: "auth.signin.error",
              reason: reason
            )

            conn
            |> put_flash(:error, "Failed to sign in")
            |> redirect(to: "/")
        end

      # --- New User: Sign-up Flow ---
      nil ->
        Logger.debug("New user signup", user_login: github_login, event: "auth.new_user")

        with {:ok, user} <- Accounts.user_from_auth(auth) do
          Logger.info("Successfully signed up new user",
            user_id: user.id,
            user_login: user.login,
            event: "auth.signup.success",
            organization_id: user.default_organization_id
          )

          conn
          |> put_flash(:info, "Successfully signed up! Let's get you started.")
          |> put_session(:current_user_id, user.id)
          |> configure_session(renew: true)
          |> redirect(to: ~p"/onboarding")
        else
          {:error, reason} ->
            Logger.error("Failed to create account",
              user_login: github_login,
              event: "auth.signup.error",
              reason: reason
            )

            conn
            |> put_flash(:error, "Failed to create account.")
            |> redirect(to: "/")
        end
    end
  end

  def callback(%{assigns: %{ueberauth_failure: fails}} = conn, _params) do
    Logger.error("Authentication failure",
      event: "auth.failure",
      reason: inspect(Map.get(fails, :errors)),
      provider: "github"
    )

    conn
    |> put_flash(:error, "Failed to authenticate.")
    |> redirect(to: "/")
  end

  def delete(conn, _params) do
    user_id = get_session(conn, :current_user_id)

    if user_id do
      Logger.info("User logged out", user_id: user_id, event: "auth.logout")
    end

    conn
    |> clear_session()
    |> put_flash(:info, "Successfully logged out.")
    |> redirect(to: "/")
  end
end
