defmodule RepobotWeb.SessionController do
  use RepobotWeb, :controller

  alias <PERSON>obot.Accounts

  def switch_organization(conn, %{"id" => org_id}) do
    # Verify the organization exists and is accessible to the user
    with {:ok, uuid} <- Ecto.UUID.cast(org_id),
         {:ok, organizations} <-
           Accounts.get_user_organizations_from_db(conn.assigns.current_user),
         org when not is_nil(org) <- Enum.find(organizations, &(&1.id == uuid)) do
      conn
      |> put_session(:current_organization_id, uuid)
      |> configure_session(renew: true)
      |> put_flash(:info, "Organization switched successfully")
      |> redirect(to: ~p"/repositories")
    else
      :error ->
        conn
        |> put_flash(:error, "Invalid organization ID format")
        |> redirect(to: ~p"/repositories")

      nil ->
        conn
        |> put_flash(:error, "Invalid organization selected")
        |> redirect(to: ~p"/repositories")

      {:error, _reason} ->
        conn
        |> put_flash(:error, "Failed to switch organization")
        |> redirect(to: ~p"/repositories")
    end
  end
end
