defmodule RepobotWeb.PageController do
  use RepobotWeb, :controller

  alias <PERSON><PERSON><PERSON>.Waitlist

  def home(conn, _params) do
    # Used by the changeset form
    changeset = Waitlist.Entry.changeset(%Waitlist.Entry{}, %{})

    render(conn, :home, changeset: changeset)
  end

  def join_waitlist(conn, %{"entry" => entry_params, "confirm_email" => confirm_email}) do
    # Simple honeypot spam protection
    if confirm_email != "" do
      conn
      |> put_flash(:info, "Thank you for joining the waitlist!")
      |> redirect(to: ~p"/")
    else
      case Waitlist.create_entry(entry_params) do
        {:ok, _entry} ->
          conn
          |> put_flash(:info, "Thank you for joining the waitlist!")
          |> redirect(to: ~p"/")

        {:error, %Ecto.Changeset{} = changeset} ->
          conn
          |> put_flash(:error, "Could not join the waitlist. Please check the errors below.")
          |> render(:home, changeset: changeset)
      end
    end
  end
end
