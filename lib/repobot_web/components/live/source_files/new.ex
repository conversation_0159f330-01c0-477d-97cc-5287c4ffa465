defmodule RepobotWeb.Live.SourceFiles.New do
  use RepobotWeb, :live_view

  alias <PERSON>obot.SourceFile
  alias RepobotWeb.Live.SourceFiles.FormComponent

  def mount(_params, _session, socket) do
    {:ok,
     socket
     |> assign(:page_title, "New Source File")}
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="mb-8">
        <nav class="mb-6">
          <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
            <li>
              <.link navigate={~p"/source-files"} class="hover:text-indigo-600">
                Source Files
              </.link>
            </li>
            <li>•</li>
            <li class="font-medium text-slate-900">New Source File</li>
          </ol>
        </nav>
        <div>
          <h1 class="text-2xl font-semibold text-slate-900">New Source File</h1>
          <p class="mt-2 text-sm text-slate-600">
            Create a new source file that can be synced across repositories.
          </p>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-slate-200">
        <.live_component
          module={FormComponent}
          id="new-source-file"
          action={:new}
          source_file={%SourceFile{}}
          current_user={@current_user}
          current_organization={@current_organization}
        />
      </div>
    </div>
    """
  end
end
