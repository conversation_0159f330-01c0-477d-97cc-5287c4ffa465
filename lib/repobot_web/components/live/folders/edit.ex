defmodule RepobotWeb.Live.Folders.Edit do
  use RepobotWeb, :live_view

  alias Repobot.Folders

  def mount(%{"id" => id}, _session, socket) do
    folder = Folders.get_folder!(id)

    {:ok,
     socket
     |> assign(:folder, folder)
     |> assign(:page_title, "Edit #{folder.name}")
     |> assign(:changeset, Folders.change_folder(folder))}
  end

  def handle_event("save", %{"folder" => folder_params}, socket) do
    folder_params =
      case folder_params["settings"] do
        settings when is_binary(settings) ->
          case Jason.decode(settings) do
            {:ok, parsed_settings} ->
              Map.put(folder_params, "settings", parsed_settings)

            {:error, _} ->
              folder_params
          end

        _ ->
          folder_params
      end

    case Folders.update_folder(socket.assigns.folder, folder_params) do
      {:ok, folder} ->
        {:noreply,
         socket
         |> put_flash(:info, "Folder updated successfully")
         |> push_navigate(to: ~p"/folders/#{folder}")}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-2xl font-semibold text-slate-900">Edit <%= @folder.name %></h1>
        <.link navigate={~p"/folders/#{@folder}"} class="text-indigo-600 hover:text-indigo-900">
          Back to Folder
        </.link>
      </div>

      <div class="bg-white rounded-lg border border-slate-200 p-6">
        <.form :let={f} for={@changeset} phx-submit="save">
          <div class="space-y-6">
            <div>
              <.input field={f[:name]} type="text" label="Name" />
            </div>

            <div>
              <label class="block text-sm font-medium text-slate-700 mb-2">Settings</label>
              <div class="mt-1">
                <textarea
                  id="settings"
                  name="folder[settings]"
                  class="block w-full rounded-md border-slate-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm font-mono"
                  rows="10"
                ><%= Jason.encode_to_iodata!(@folder.settings, pretty: true) %></textarea>
              </div>
              <p class="mt-2 text-sm text-slate-500">
                Enter JSON settings for this folder. These settings will be inherited by repositories in this folder.
              </p>
            </div>

            <div class="flex justify-end">
              <.button>Save Changes</.button>
            </div>
          </div>
        </.form>
      </div>
    </div>
    """
  end
end
