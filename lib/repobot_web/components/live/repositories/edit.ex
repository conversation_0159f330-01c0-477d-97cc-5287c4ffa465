defmodule RepobotWeb.Live.Repositories.Edit do
  use RepobotWeb, :live_view

  alias Repobot.{Repositories, Folders}

  def mount(%{"id" => id}, _session, socket) do
    repository =
      Repositories.get_repository!(id) |> Repobot.Repo.preload([:folder, :template_folders])

    folders =
      Folders.list_folders(
        socket.assigns.current_user,
        nil,
        socket.assigns.current_organization.id
      )

    folder_settings =
      if repository.folder_id && !repository.template,
        do: Folders.get_folder!(repository.folder_id).settings,
        else: %{}

    {:ok,
     socket
     |> assign(:repository, repository)
     |> assign(:folders, folders)
     |> assign(:folder_settings, folder_settings)
     |> assign(:page_title, "Edit #{repository.full_name}")
     |> assign(:changeset, Repositories.change_repository(repository))}
  end

  def handle_event("save", %{"repository" => repository_params} = params, socket) do
    repository_params =
      case repository_params["settings"] do
        settings when is_binary(settings) ->
          case Jason.decode(settings) do
            {:ok, parsed_settings} ->
              Map.put(repository_params, "settings", parsed_settings)

            {:error, _} ->
              repository_params
          end

        _ ->
          repository_params
      end

    # Handle template folder selections if repository is a template
    template_folder_ids =
      (params["template_folders"] || %{})
      |> Map.keys()
      |> MapSet.new()

    # If becoming a template, ensure folder_id is cleared
    repository_params =
      if repository_params["template"] == "true" do
        Map.put(repository_params, "folder_id", nil)
      else
        repository_params
      end

    case Repositories.update_repository(socket.assigns.repository, repository_params) do
      {:ok, repository} ->
        # If repository is a template, update its template folders
        if repository.template do
          selected_folders =
            Enum.filter(socket.assigns.folders, &MapSet.member?(template_folder_ids, &1.id))

          # Update template folders
          repository
          |> Repobot.Repo.preload(:template_folders)
          |> Ecto.Changeset.change()
          |> Ecto.Changeset.put_assoc(:template_folders, selected_folders)
          |> Repobot.Repo.update()
        end

        {:noreply,
         socket
         |> put_flash(:info, "Repository updated successfully")
         |> push_navigate(to: ~p"/repositories/#{repository}")}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  defp folder_selected?(folder, repository) do
    (not repository.template && folder.id == repository.folder_id) ||
      Enum.any?(repository.template_folders, &(&1.id == folder.id))
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-2xl font-semibold text-slate-900">Edit {@repository.full_name}</h1>
        <.link
          navigate={~p"/repositories/#{@repository}"}
          class="text-indigo-600 hover:text-indigo-900"
        >
          Back to Repository
        </.link>
      </div>

      <div class="bg-white rounded-lg border border-slate-200 p-6">
        <.simple_form :let={f} for={@changeset} phx-submit="save">
          <div class="space-y-6">
            <div class="flex flex-col gap-4">
              <div class="flex items-center gap-2">
                <.input type="checkbox" field={f[:template]} label="Template Repository" />
                <div class="ml-2 relative group">
                  <.icon name="hero-information-circle" class="h-5 w-5 text-slate-400" />
                  <div class="hidden group-hover:block absolute bg-white p-2 rounded shadow-lg border border-slate-200 text-sm text-slate-600 max-w-xs z-10">
                    Template repositories can be used as a starting point for new repositories.
                    Note: Making this a template repository will remove its primary folder assignment.
                  </div>
                </div>
              </div>

              <%= if @repository.template do %>
                <div class="pl-6">
                  <label class="block text-sm font-medium text-slate-700 mb-2">
                    Template Folders
                  </label>
                  <div class="mt-1 space-y-2">
                    <%= for folder <- @folders do %>
                      <div class="flex items-center">
                        <input
                          type="checkbox"
                          name={"template_folders[#{folder.id}]"}
                          value="true"
                          checked={folder_selected?(folder, @repository)}
                          class="h-4 w-4 rounded border-slate-300 text-indigo-600 focus:ring-indigo-500"
                        />
                        <label class="ml-2 text-sm text-slate-700">
                          {folder.name}
                        </label>
                      </div>
                    <% end %>
                    <p class="mt-2 text-sm text-slate-500">
                      Select the folders this template repository should be associated with.
                    </p>
                  </div>
                </div>
              <% end %>

              <div class="pl-6">
                <label class="block text-sm font-medium text-slate-700 mb-2">
                  Sync Mode
                </label>
                <div class="mt-1">
                  <.input
                    type="select"
                    field={f[:sync_mode]}
                    options={[
                      {"Direct Updates", :direct},
                      {"Pull Requests", :pr}
                    ]}
                  />
                  <p class="mt-2 text-sm text-slate-500">
                    Choose how changes should be synchronized to target repositories:
                    <ul class="mt-1 list-disc list-inside">
                      <li>Direct Updates - Changes are applied immediately</li>
                      <li>Pull Requests - Changes are proposed via pull requests</li>
                    </ul>
                  </p>
                </div>
              </div>
            </div>

            <%= if @repository.folder_id && !@repository.template do %>
              <div>
                <label class="block text-sm font-medium text-slate-700 mb-2">
                  Folder Settings (inherited)
                </label>
                <div class="mt-1">
                  <pre class="block w-full rounded-md border border-slate-300 bg-slate-50 p-4 text-sm font-mono text-slate-900"><%= Jason.encode_to_iodata!(@folder_settings, pretty: true) %></pre>
                </div>
                <p class="mt-2 text-sm text-slate-500">
                  These settings are inherited from the folder and can be overridden below.
                </p>
              </div>
            <% end %>

            <div>
              <label class="block text-sm font-medium text-slate-700 mb-2">Repository Settings</label>
              <div class="mt-1">
                <textarea
                  id="settings"
                  name="repository[settings]"
                  class="block w-full rounded-md border-slate-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm font-mono"
                  rows="10"
                ><%= Jason.encode_to_iodata!(@repository.settings, pretty: true) %></textarea>
              </div>
              <p class="mt-2 text-sm text-slate-500">
                Enter JSON settings for this repository. These settings will override any folder settings.
              </p>
            </div>

            <div class="flex justify-end">
              <.button>Save Changes</.button>
            </div>
          </div>
        </.simple_form>
      </div>
    </div>
    """
  end
end
