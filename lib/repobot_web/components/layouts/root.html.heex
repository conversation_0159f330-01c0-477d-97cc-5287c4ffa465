<!DOCTYPE html>
<html lang="en" class="h-full">
  <head>
    <title>{page_title()}</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content={get_csrf_token()} />

    <.meta_tags />

    <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link phx-track-static rel="stylesheet" href={~p"/assets/css/app.css"} />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/js/app.js"}>
    </script>
    <script>
      window.openImageModal = (imageSrc) => {
        const modal = document.getElementById('image-modal');
        const modalImage = document.getElementById('modal-image');
        modalImage.src = imageSrc;
        modal.classList.remove('hidden');
      }

      window.closeImageModal = () => {
        const modal = document.getElementById('image-modal');
        modal.classList.add('hidden');
      }

      // Close modal when clicking outside the image
      window.addEventListener('click', (e) => {
        const modal = document.getElementById('image-modal');
        if (e.target === modal) {
          closeImageModal();
        }
      });

      // Close modal on escape key
      window.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          closeImageModal();
        }
      });
    </script>
    <%= if Application.get_env(:repobot, :analytics) do %>
      <script defer data-domain="repobot.app" src="https://plausible.io/js/script.js">
      </script>
    <% end %>
  </head>
  <body class="flex flex-col h-full bg-white">
    <!-- Image Modal -->
    <div
      id="image-modal"
      class="hidden fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4"
    >
      <img
        id="modal-image"
        class="max-h-[90vh] max-w-[90vw] object-contain rounded-lg"
        src=""
        alt="Enlarged view"
      />
      <button
        onclick="closeImageModal()"
        class="btn btn-ghost btn-circle absolute top-4 right-4 text-white"
        aria-label="Close modal"
      >
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>
    
<!-- Flash Messages Container -->
    <div class="fixed top-4 right-4 z-50 space-y-4">
      <div
        :if={@flash["info"]}
        id="info-flash"
        class="rounded-md bg-green-50 px-6 py-4 text-base shadow-lg border border-green-200"
      >
        <div class="flex items-center justify-between gap-3">
          <div class="flex items-center gap-3">
            <.icon name="hero-check-circle" class="h-6 w-6 text-green-500" />
            <p class="font-medium text-green-800">{@flash["info"]}</p>
          </div>
          <button
            type="button"
            onclick="this.closest('#info-flash').remove()"
            class="btn btn-ghost btn-sm btn-square text-green-500"
          >
            <.icon name="hero-x-mark" class="h-5 w-5" />
          </button>
        </div>
      </div>
      <div
        :if={@flash["error"]}
        id="error-flash"
        class="rounded-md bg-red-50 px-6 py-4 text-base shadow-lg border border-red-200"
      >
        <div class="flex items-center justify-between gap-3">
          <div class="flex items-center gap-3">
            <.icon name="hero-x-circle" class="h-6 w-6 text-red-500" />
            <p class="font-medium text-red-800">{@flash["error"]}</p>
          </div>
          <button
            type="button"
            onclick="this.closest('#error-flash').remove()"
            class="text-red-500 hover:text-red-600 focus:outline-none"
          >
            <.icon name="hero-x-mark" class="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
    
<!-- Navigation -->
    <nav class="bg-white border-b border-slate-200 sticky top-0 z-40 backdrop-blur-sm bg-white/90">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center">
            <img src={~p"/images/repobot.png"} alt="RepoBot" class="h-12" />
          </div>
          <div class="flex items-center gap-4">
            <.btn href="/auth/github" variant="primary">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path
                  fill-rule="evenodd"
                  d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                  clip-rule="evenodd"
                />
              </svg>
              Sign in with GitHub
            </.btn>
          </div>
        </div>
      </div>
    </nav>

    <main class="flex-grow">
      <!-- Hero Section -->
      <div class="bg-gradient-to-b from-indigo-50 via-white to-indigo-50/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-28">
          <div class="text-center">
            <!-- Badge with "Introducing RepoBot" -->
            <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 mb-6 animate-fade-in">
              <span>✨ Introducing RepoBot</span>
            </div>

            <h1 class="text-5xl font-bold tracking-tight sm:text-6xl bg-clip-text text-transparent bg-gradient-to-r from-indigo-700 via-indigo-600 to-indigo-500">
              <span>AI-powered Maintenance Automation</span>
              <span class="block">for GitHub</span>
            </h1>
            <p class="mt-6 text-xl text-slate-600 max-w-2xl mx-auto">
              Keep your repositories in sync, automate maintenance tasks.
              <span class="font-bold bg-gradient-to-r from-amber-500 to-orange-500 bg-clip-text text-transparent">
                Save countless hours
              </span>
              of manual work.
            </p>
            <div class="mt-12">
              <div class="max-w-[90%] lg:max-w-[80%] mx-auto">
                <div class="rounded-xl bg-gradient-to-r from-indigo-100/50 via-white to-amber-100/50 p-1 shadow-xl">
                  <div class="rounded-lg overflow-hidden shadow-[0_0_50px_rgba(79,70,229,0.15)]">
                    <img
                      src="/images/hp-hero-01.png"
                      alt="App screenshot"
                      class="rounded-lg w-full aspect-[16/9] object-cover transition-all duration-500 hover:scale-[1.01]"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-16 -mt-8">
          <.form
            :let={f}
            for={@changeset}
            action={~p"/waitlist"}
            method="post"
            class="bg-white p-8 rounded-2xl shadow-xl border border-slate-100"
          >
            <div class="flex flex-col gap-6">
              <div class="text-center">
                <h3 class="text-2xl font-bold text-slate-800">
                  Join the Beta Waitlist!
                </h3>
                <p class="mt-2 text-slate-600">
                  Be among the first to experience RepoBot's powerful features.
                </p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <.input
                    field={f[:email]}
                    type="email"
                    placeholder="<EMAIL>"
                    required
                    class="w-full h-12"
                    label=""
                  />
                </div>

                <div>
                  <.input
                    field={f[:github_username]}
                    type="text"
                    placeholder="GitHub username"
                    required
                    class="w-full h-12"
                    label=""
                  />
                </div>
              </div>

              <div class="hidden">
                <.input
                  field={f[:confirm_email]}
                  type="email"
                  label="Confirm Email"
                  name="confirm_email"
                  id="confirm_email_hp"
                  value=""
                  tabindex="-1"
                  autocomplete="off"
                />
              </div>

              <div class="flex justify-center">
                <.button
                  type="submit"
                  class="px-8 h-12 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white text-base font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                  phx-disable-with="Submitting..."
                >
                  Join Waitlist
                </.button>
              </div>
            </div>
          </.form>
        </div>

        <div class="flex justify-center pb-8 -mt-6">
          <.btn
            href="#stats"
            class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-white/80 shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 group"
            aria-label="Scroll to stats"
            variant="ghost"
          >
            <svg
              class="w-6 h-6 text-indigo-600 animate-bounce group-hover:animate-none"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2.5"
                d="M19 14l-7 7m0 0l-7-7m7 7V3"
              />
            </svg>
          </.btn>
        </div>
      </div>
      <!-- Stats Section -->
      <div class="relative py-16 bg-white" id="stats">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-12">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-amber-100 text-amber-800 mb-4">
              Platform Metrics
            </span>
            <h2 class="mt-4 text-3xl font-bold tracking-tight text-slate-900 mb-3">
              Making
              <span class="bg-gradient-to-r from-indigo-600 via-amber-500 to-emerald-500 bg-clip-text text-transparent">
                REAL
              </span>
              impact
            </h2>
            <p class="mt-2 text-lg text-slate-600 max-w-2xl mx-auto">
              Real numbers from teams using RepoBot to streamline their development workflow
            </p>
          </div>

          <div class="relative bg-white rounded-2xl shadow-lg border border-slate-100 p-8">
            <.stats />
          </div>
        </div>
      </div>
      <!-- File Sync Section -->
      <div id="file-sync" class="py-24 bg-gradient-to-b from-white to-indigo-50/30">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 mb-4">
              Feature Highlight
            </span>
            <h2 class="text-3xl font-bold tracking-tight text-slate-900 sm:text-4xl mb-3">
              File Sync
            </h2>
            <p class="mt-4 text-lg text-slate-600 max-w-2xl mx-auto">
              Keep common files and configs up to date across all your repositories with a single click.
              No more copy-pasting or manual updates.
            </p>
          </div>

          <div class="mt-16 grid grid-cols-1 gap-12 sm:grid-cols-2 lg:grid-cols-3">
            <!-- Feature Card 1 -->
            <div class="group relative overflow-visible pb-4">
              <div
                class="rounded-xl bg-white p-1 ring-1 ring-slate-200 shadow-lg cursor-pointer transition-all duration-300 group-hover:shadow-xl group-hover:-translate-y-1"
                onclick="openImageModal('/images/file-sync-01.png')"
              >
                <div class="overflow-hidden rounded-lg">
                  <img
                    src="/images/file-sync-01.png"
                    alt="Feature screenshot 1"
                    class="rounded-lg w-full aspect-[16/9] object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                <div class="p-5">
                  <div class="flex items-center space-x-2 mb-2">
                    <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                      <svg
                        class="w-4 h-4 text-indigo-600"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-slate-900">Select Files</h3>
                  </div>
                  <p class="text-slate-600">
                    Choose which files to keep in sync across repositories with an intuitive file picker.
                  </p>
                </div>
              </div>
            </div>
            
<!-- Feature Card 2 -->
            <div class="group relative overflow-visible pb-4">
              <div
                class="rounded-xl bg-white p-1 ring-1 ring-slate-200 shadow-lg cursor-pointer transition-all duration-300 group-hover:shadow-xl group-hover:-translate-y-1"
                onclick="openImageModal('/images/file-sync-02.png')"
              >
                <div class="overflow-hidden rounded-lg">
                  <img
                    src="/images/file-sync-02.png"
                    alt="Feature screenshot 2"
                    class="rounded-lg w-full aspect-[16/9] object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                <div class="p-5">
                  <div class="flex items-center space-x-2 mb-2">
                    <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                      <svg
                        class="w-4 h-4 text-indigo-600"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                        />
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-slate-900">Configure Once</h3>
                  </div>
                  <p class="text-slate-600">
                    Set up your sync preferences with a clean interface and let RepoBot handle the rest.
                  </p>
                </div>
              </div>
            </div>
            
<!-- Feature Card 3 -->
            <div class="group relative overflow-visible pb-4">
              <div
                class="rounded-xl bg-white p-1 ring-1 ring-slate-200 shadow-lg cursor-pointer transition-all duration-300 group-hover:shadow-xl group-hover:-translate-y-1"
                onclick="openImageModal('/images/file-sync-03.png')"
              >
                <div class="overflow-hidden rounded-lg">
                  <img
                    src="/images/file-sync-03.png"
                    alt="Feature screenshot 3"
                    class="rounded-lg w-full aspect-[16/9] object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                <div class="p-5">
                  <div class="flex items-center space-x-2 mb-2">
                    <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                      <svg
                        class="w-4 h-4 text-indigo-600"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        />
                      </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-slate-900">Automatic Updates</h3>
                  </div>
                  <p class="text-slate-600">
                    Changes are automatically propagated to all linked repositories.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Template Repositories Section -->
      <div id="template-repos" class="py-24 bg-gradient-to-br from-white to-indigo-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 mb-4">
              Powerful Features
            </span>
            <h2 class="text-3xl font-bold tracking-tight text-slate-900 sm:text-4xl mb-3">
              Template Repositories
            </h2>
            <p class="mt-4 text-lg text-slate-600 max-w-2xl mx-auto">
              Streamline repository creation and management with template repositories. Choose between PR and direct push modes for maximum flexibility and control.
            </p>
          </div>

          <div class="mt-16 grid grid-cols-1 gap-12 sm:grid-cols-2 lg:grid-cols-3">
            <!-- Feature Card 1 -->
            <div class="group relative overflow-visible pb-4">
              <div
                class="rounded-xl bg-white p-1 ring-1 ring-slate-200 shadow-lg cursor-pointer transition-all duration-300 group-hover:shadow-xl group-hover:-translate-y-1"
                onclick="openImageModal('/images/template-repository-01.png')"
              >
                <div class="overflow-hidden rounded-lg">
                  <img
                    src="/images/template-repository-01.png"
                    alt="Template repository sync modes"
                    class="rounded-lg w-full aspect-[16/9] object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                <div class="p-5">
                  <div class="flex items-center space-x-2 mb-2">
                    <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                      <svg
                        class="w-4 h-4 text-purple-600"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                        />
                      </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-slate-900">Flexible Sync Modes</h3>
                  </div>
                  <p class="text-slate-600">
                    Choose between Pull Request or Direct Push modes to match your workflow needs and team requirements.
                  </p>
                </div>
              </div>
            </div>
            
<!-- Feature Card 2 -->
            <div class="group relative overflow-visible pb-4">
              <div
                class="rounded-xl bg-white p-1 ring-1 ring-slate-200 shadow-lg cursor-pointer transition-all duration-300 group-hover:shadow-xl group-hover:-translate-y-1"
                onclick="openImageModal('/images/template-repository-02.png')"
              >
                <div class="overflow-hidden rounded-lg">
                  <img
                    src="/images/template-repository-02.png"
                    alt="Template repository folder organization"
                    class="rounded-lg w-full aspect-[16/9] object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                <div class="p-5">
                  <div class="flex items-center space-x-2 mb-2">
                    <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                      <svg
                        class="w-4 h-4 text-purple-600"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
                        />
                      </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-slate-900">Organized Management</h3>
                  </div>
                  <p class="text-slate-600">
                    Add template repositories to folders alongside your sync targets for streamlined organization.
                  </p>
                </div>
              </div>
            </div>
            
<!-- Feature Card 3 -->
            <div class="group relative overflow-visible pb-4">
              <div
                class="rounded-xl bg-white p-1 ring-1 ring-slate-200 shadow-lg cursor-pointer transition-all duration-300 group-hover:shadow-xl group-hover:-translate-y-1"
                onclick="openImageModal('/images/template-repository-03.png')"
              >
                <div class="overflow-hidden rounded-lg">
                  <img
                    src="/images/template-repository-03.png"
                    alt="Automatic GitHub synchronization"
                    class="rounded-lg w-full aspect-[16/9] object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                <div class="p-5">
                  <div class="flex items-center space-x-2 mb-2">
                    <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                      <svg
                        class="w-4 h-4 text-purple-600"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
                        />
                      </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-slate-900">
                      Automatic Synchronization
                    </h3>
                  </div>
                  <p class="text-slate-600">
                    RepoBot automatically pushes changes to GitHub, keeping your repositories in perfect sync.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Open Source Section -->
      <div class="py-16 bg-gradient-to-br from-indigo-50 via-white to-amber-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center">
            <div class="inline-flex items-center justify-center p-8 rounded-2xl bg-white shadow-xl border border-slate-100 max-w-3xl mx-auto">
              <div class="flex flex-col md:flex-row items-center gap-6">
                <div class="w-16 h-16 rounded-full bg-amber-100 flex items-center justify-center">
                  <svg
                    class="w-8 h-8 text-amber-600"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
                <div>
                  <p class="text-2xl md:text-3xl text-slate-800">
                    <span class="font-bold bg-gradient-to-r from-indigo-600 to-amber-500 bg-clip-text text-transparent">
                      Free
                    </span>
                    for Open Source projects
                  </p>
                  <p class="mt-2 text-slate-600 max-w-xl">
                    We believe in giving back to the community. RepoBot is completely free for all public open source repositories.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
<!-- CTA Section -->
      <div class="py-24 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 md:p-12 shadow-2xl border border-white/20">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 class="text-3xl md:text-4xl font-bold tracking-tight text-white">
                  Ready to simplify repository maintenance?
                </h2>
                <p class="mt-4 text-lg text-indigo-100">
                  Join the RepoBot beta waitlist and start automating those repetitive GitHub tasks that slow down your development.
                </p>
                <ul class="mt-8 space-y-4">
                  <li class="flex items-start">
                    <svg
                      class="h-6 w-6 text-indigo-200 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span class="text-white">Save time on repository maintenance</span>
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="h-6 w-6 text-indigo-200 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span class="text-white">Avoid boring sync tasks and copy-pasting</span>
                  </li>
                  <li class="flex items-start">
                    <svg
                      class="h-6 w-6 text-indigo-200 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    <span class="text-white">Focus on coding instead of chores</span>
                  </li>
                </ul>
              </div>
              <div>
                <div class="bg-white rounded-xl shadow-lg p-6">
                  <h3 class="text-xl font-semibold text-slate-800 mb-4">
                    Join the Beta Waitlist
                  </h3>
                  <.form :let={f} for={@changeset} action={~p"/waitlist"} method="post">
                    <div class="space-y-4">
                      <div>
                        <label class="label">Email</label>
                        <.input
                          field={f[:email]}
                          type="email"
                          placeholder="<EMAIL>"
                          required
                          class="w-full h-12 rounded-lg"
                          label=""
                        />
                      </div>
                      <div>
                        <label class="label">
                          GitHub Username
                        </label>
                        <.input
                          field={f[:github_username]}
                          type="text"
                          placeholder="GitHub username"
                          required
                          class="w-full h-12 rounded-lg"
                          label=""
                        />
                      </div>

                      <div class="hidden">
                        <.input
                          field={f[:confirm_email]}
                          type="email"
                          label="Confirm Email"
                          name="confirm_email"
                          id="confirm_email_cta"
                          value=""
                          tabindex="-1"
                          autocomplete="off"
                        />
                      </div>

                      <div>
                        <.button
                          type="submit"
                          class="w-full h-12 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white text-base font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
                          phx-disable-with="Submitting..."
                        >
                          Get Early Access
                        </.button>
                      </div>
                    </div>
                  </.form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
<!-- Coming Soon Section -->
      <div class="py-24 bg-gradient-to-b from-white to-indigo-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center">
            <span class="inline-flex items-center rounded-full px-4 py-1 text-sm font-medium bg-indigo-100 text-indigo-700 ring-1 ring-inset ring-indigo-700/10 mb-4">
              Coming Soon
            </span>
            <h2 class="mt-4 text-3xl font-bold tracking-tight text-slate-900 sm:text-4xl mb-3">
              More Awesome Features
            </h2>
            <p class="mt-4 text-lg text-slate-600 max-w-2xl mx-auto">
              We're cooking up some exciting new features that will revolutionize how you manage your GitHub repositories.
              Stay tuned!
            </p>

            <div class="mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
              <!-- Coming Soon Feature 1 -->
              <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-slate-100 p-6 hover:shadow-xl transition-all duration-200 group">
                <div class="w-12 h-12 rounded-lg bg-indigo-100 flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-200">
                  <svg
                    class="w-6 h-6 text-indigo-600"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-slate-900 text-center mb-2">
                  Advanced Customization
                </h3>
                <p class="text-slate-600 text-center">
                  Granular control over which repositories get updated and how, with powerful filtering capabilities.
                </p>
              </div>
              
<!-- Coming Soon Feature 2 -->
              <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-slate-100 p-6 hover:shadow-xl transition-all duration-200 group">
                <div class="w-12 h-12 rounded-lg bg-indigo-100 flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-200">
                  <svg
                    class="w-6 h-6 text-indigo-600"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-slate-900 text-center mb-2">
                  Detailed Analytics
                </h3>
                <p class="text-slate-600 text-center">
                  Track your time savings, sync patterns, and repository health with comprehensive dashboards.
                </p>
              </div>
              
<!-- Coming Soon Feature 3 -->
              <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-slate-100 p-6 hover:shadow-xl transition-all duration-200 group">
                <div class="w-12 h-12 rounded-lg bg-indigo-100 flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-200">
                  <svg
                    class="w-6 h-6 text-indigo-600"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-slate-900 text-center mb-2">
                  AI-Powered Suggestions
                </h3>
                <p class="text-slate-600 text-center">
                  Smart recommendations for repository improvements based on patterns and best practices.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    
<!-- Footer -->
    <footer class="bg-slate-900">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="col-span-1 md:col-span-2">
            <div class="flex items-center mb-4">
              <img src={~p"/images/repobot.png"} alt="RepoBot" class="h-10 mr-3" />
              <span class="text-white text-2xl font-bold">RepoBot</span>
            </div>
            <p class="mt-2 text-slate-400 text-base max-w-md">
              Automating GitHub maintenance tasks so you can focus on what matters most - writing great code.
            </p>
          </div>

          <div>
            <h3 class="text-sm font-semibold text-white tracking-wider uppercase mb-4">
              Resources
            </h3>
            <ul role="list" class="space-y-3">
              <li>
                <a
                  href="#"
                  class="text-base text-slate-400 hover:text-white transition-colors duration-150"
                >
                  Documentation
                </a>
              </li>
              <li>
                <a
                  href="#"
                  class="text-base text-slate-400 hover:text-white transition-colors duration-150"
                >
                  API Reference
                </a>
              </li>
              <li>
                <a
                  href="#"
                  class="text-base text-slate-400 hover:text-white transition-colors duration-150"
                >
                  Guides
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-sm font-semibold text-white tracking-wider uppercase mb-4">Legal</h3>
            <ul role="list" class="space-y-3">
              <li>
                <a
                  href="#"
                  class="text-base text-slate-400 hover:text-white transition-colors duration-150"
                >
                  Privacy Policy
                </a>
              </li>
              <li>
                <a
                  href="#"
                  class="text-base text-slate-400 hover:text-white transition-colors duration-150"
                >
                  Terms of Service
                </a>
              </li>
              <li>
                <a
                  href="#"
                  class="text-base text-slate-400 hover:text-white transition-colors duration-150"
                >
                  Cookie Policy
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div class="mt-12 pt-8 border-t border-slate-700">
          <p class="text-base text-slate-400 text-center">
            &copy; {DateTime.utc_now().year} RepoBot. All rights reserved.
          </p>
          <p class="mt-4 text-sm text-slate-400 text-center">
            Built with <span class="text-purple-400">💜</span>
            by
            <a
              href="https://solnic.dev"
              class="text-indigo-400 hover:text-indigo-300 transition-colors duration-150"
            >
              @solnic.dev
            </a>
          </p>
        </div>
      </div>
    </footer>
  </body>
</html>
