defmodule RepobotWeb.Layouts do
  @moduledoc """
  This module holds different layouts used by your application.

  See the `layouts` directory for all templates available.
  The "root" layout is a skeleton rendered as part of the
  application router. The "app" layout is set as the default
  layout on both `use RepobotWeb, :controller` and
  `use RepobotWeb, :live_view`.
  """
  use RepobotWeb, :html
  import Phoenix.Controller, only: [get_csrf_token: 0]
  import RepobotWeb.UserMenuComponent
  import RepobotWeb.StatsComponent

  @doc """
  Returns a map containing all metadata for the application.
  This serves as the single source of truth for all meta tags.
  """
  def metadata do
    %{
      title: "RepoBot - AI-powered Maintenance Automation for GitHub",
      description:
        "AI-powered GitHub maintenance automation. Keep your repositories in sync, automate file syncs, and manage template repositories. Free for open source projects.",
      keywords:
        "github automation, repository management, file sync, template repositories, github maintenance, repository sync, github tools, devops automation",
      image: "repobot-og-image.png"
    }
  end

  @doc """
  Returns the page title.
  """
  def page_title, do: metadata().title

  @doc """
  Returns the full URL for the OG image.
  """
  def og_image_url(_assigns) do
    url(~p"/images/#{metadata().image}")
  end

  @doc """
  Generates primary meta tags for SEO.
  """
  def primary_meta_tags(assigns) do
    ~H"""
    <meta name="title" content={metadata().title} />
    <meta name="description" content={metadata().description} />
    <meta name="keywords" content={metadata().keywords} />
    """
  end

  @doc """
  Generates Open Graph meta tags.
  """
  def og_meta_tags(assigns) do
    ~H"""
    <meta property="og:type" content="website" />
    <meta property="og:url" content={url(~p"/")} />
    <meta property="og:title" content={metadata().title} />
    <meta property="og:description" content={metadata().description} />
    <meta property="og:image" content={og_image_url(assigns)} />
    """
  end

  @doc """
  Generates Twitter Card meta tags.
  """
  def twitter_meta_tags(assigns) do
    ~H"""
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={url(~p"/")} />
    <meta property="twitter:title" content={metadata().title} />
    <meta property="twitter:description" content={metadata().description} />
    <meta property="twitter:image" content={og_image_url(assigns)} />
    """
  end

  @doc """
  Generates all meta tags for SEO and social sharing.
  """
  def meta_tags(assigns) do
    ~H"""
    <!-- Primary Meta Tags -->
    <.primary_meta_tags />

    <!-- Open Graph / Facebook -->
    <.og_meta_tags />

    <!-- Twitter -->
    <.twitter_meta_tags />
    """
  end

  embed_templates "layouts/*"
end
