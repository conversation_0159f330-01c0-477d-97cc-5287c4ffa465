defmodule RepobotWeb.StatsComponent do
  @moduledoc """
  Component for displaying repository and file statistics on the home page.
  """
  use RepobotWeb, :html

  alias Repobot.{Repo, Repository, RepositorySourceFile}
  import Ecto.Query

  def stats(assigns) do
    ~H"""
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- Repositories Stats -->
      <div class="relative group">
        <div class="flex flex-col items-center p-6 bg-gradient-to-br from-indigo-50 to-white rounded-xl transition-transform duration-300 group-hover:scale-105">
          <div class="text-4xl font-bold text-indigo-600 mb-2">
            {repository_count()}
          </div>
          <div class="text-slate-600 text-center">
            <span class="block text-lg font-semibold">Repositories</span>
            <span class="text-sm">Actively Managed</span>
          </div>
          <div class="absolute top-4 right-4">
            <div class="animate-ping absolute h-4 w-4 rounded-full bg-indigo-400 opacity-75"></div>
            <div class="relative h-4 w-4 rounded-full bg-indigo-500"></div>
          </div>
        </div>
      </div>
      <!-- Files Stats -->
      <div class="relative group">
        <div class="flex flex-col items-center p-6 bg-gradient-to-br from-amber-50 to-white rounded-xl transition-transform duration-300 group-hover:scale-105">
          <div class="text-4xl font-bold text-amber-600 mb-2">
            {file_count()}
          </div>
          <div class="text-slate-600 text-center">
            <span class="block text-lg font-semibold">Files</span>
            <span class="text-sm">Kept in Sync</span>
          </div>
          <div class="absolute top-4 right-4">
            <svg
              class="h-6 w-6 text-amber-500 animate-pulse"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
              />
            </svg>
          </div>
        </div>
      </div>
      <!-- Time Saved Stats -->
      <div class="relative group">
        <div class="flex flex-col items-center p-6 bg-gradient-to-br from-emerald-50 to-white rounded-xl transition-transform duration-300 group-hover:scale-105">
          <div class="text-4xl font-bold text-emerald-600 mb-2">
            {hours_saved()}
          </div>
          <div class="text-slate-600 text-center">
            <span class="block text-lg font-semibold">Developer Hours</span>
            <span class="text-sm">Saved Monthly</span>
          </div>
          <div class="absolute top-4 right-4">
            <svg
              class="h-6 w-6 text-emerald-500 animate-spin-slow"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
    """
  end

  defp repository_count do
    count =
      Repository
      |> select([r], count(r.id))
      |> Repo.one()

    format_number(count)
  end

  defp file_count do
    count =
      RepositorySourceFile
      |> select([rsf], count(rsf.id))
      |> Repo.one()

    format_number(count)
  end

  defp hours_saved do
    count =
      RepositorySourceFile
      |> select([rsf], count(rsf.id))
      |> Repo.one()

    hours = ceil(count * 0.025)
    format_number(hours)
  end

  defp format_number(number) when number >= 1_000 do
    thousands = div(number, 1_000)
    remainder = rem(number, 1_000)

    if remainder == 0 do
      "#{thousands},000+"
    else
      "#{thousands},#{String.pad_leading(Integer.to_string(remainder), 3, "0")}+"
    end
  end

  defp format_number(number), do: "#{number}+"
end
