defmodule RepobotWeb.UI.Components do
  import RepobotWeb.CoreComponents

  alias Phoenix.LiveView.JS

  use Phoenix.Component
  use Gettext, backend: RepobotWeb.Gettext

  @doc """
  Renders a button using DaisyUI classes with comprehensive variant support.

  This helper extends the basic button functionality with DaisyUI styling
  and supports disabled state handling. When `href` is provided, renders
  an `<a>` element instead of a `<button>` element.

  ## Examples

      <.btn>Default</.btn>
      <.btn variant="primary">Primary</.btn>
      <.btn variant="secondary" size="sm">Small Secondary</.btn>
      <.btn variant="outline" disabled>Disabled Outline</.btn>
      <.btn variant="ghost" size="lg">Large Ghost</.btn>
      <.btn href="/path" variant="primary">Link Button</.btn>
  """
  attr :variant, :string,
    default: "default",
    values: ~w(default primary secondary accent info success warning error outline ghost soft)

  attr :size, :string,
    default: "md",
    values: ~w(xs sm md lg xl)

  attr :disabled, :boolean, default: false
  attr :wide, :boolean, default: false
  attr :block, :boolean, default: false
  attr :square, :boolean, default: false
  attr :circle, :boolean, default: false
  attr :href, :string, default: nil
  attr :class, :string, default: ""

  attr :rest, :global,
    include:
      ~w(type phx-click phx-value-* phx-target phx-disable-with data-phx-link data-phx-link-state method target rel)

  slot :inner_block, required: true

  def btn(assigns) do
    # Build DaisyUI button classes
    variant_class =
      case assigns.variant do
        "default" -> ""
        "primary" -> "btn-primary"
        "secondary" -> "btn-secondary"
        "accent" -> "btn-accent"
        "info" -> "btn-info"
        "success" -> "btn-success"
        "warning" -> "btn-warning"
        "error" -> "btn-error"
        "outline" -> "btn-outline"
        "ghost" -> "btn-ghost"
        "soft" -> "btn-soft"
      end

    size_class =
      case assigns.size do
        "xs" -> "btn-xs"
        "sm" -> "btn-sm"
        "md" -> ""
        "lg" -> "btn-lg"
        "xl" -> "btn-xl"
      end

    modifier_classes =
      [
        assigns.wide && "btn-wide",
        assigns.block && "btn-block",
        assigns.square && "btn-square",
        assigns.circle && "btn-circle",
        assigns.disabled && "btn-disabled"
      ]
      |> Enum.filter(& &1)

    button_classes = ["btn", variant_class, size_class] ++ modifier_classes ++ [assigns.class]
    assigns = assign(assigns, :button_classes, button_classes)

    if assigns.href do
      ~H"""
      <a href={@href} class={@button_classes} {@rest}>
        {render_slot(@inner_block)}
      </a>
      """
    else
      ~H"""
      <button class={@button_classes} disabled={@disabled} {@rest}>
        {render_slot(@inner_block)}
      </button>
      """
    end
  end

  @doc """
  Shows the flash group with standard titles and content.

  ## Examples

      <.flash_group flash={@flash} />
  """
  attr :flash, :map, required: true, doc: "the map of flash messages"
  attr :id, :string, default: "flash-group", doc: "the optional id of flash container"

  def flash_group(assigns) do
    ~H"""
    <div id={@id}>
      <.flash kind={:info} title={gettext("Success!")} flash={@flash} />
      <.flash kind={:error} title={gettext("Error!")} flash={@flash} />
      <.flash
        id="client-error"
        kind={:error}
        title={gettext("We can't find the internet")}
        phx-disconnected={show(".phx-client-error #client-error")}
        phx-connected={hide("#client-error")}
        hidden
      >
        {gettext("Attempting to reconnect")}
        <.icon name="hero-arrow-path" class="ml-1 h-3 w-3 animate-spin" />
      </.flash>

      <.flash
        id="server-error"
        kind={:error}
        title={gettext("Something went wrong!")}
        phx-disconnected={show(".phx-server-error #server-error")}
        phx-connected={hide("#server-error")}
        hidden
      >
        {gettext("Hang in there while we get back on track")}
        <.icon name="hero-arrow-path" class="ml-1 h-3 w-3 animate-spin" />
      </.flash>
    </div>
    """
  end

  @doc """
  Renders a simple form.

  ## Examples

      <.simple_form for={@form} phx-change="validate" phx-submit="save">
        <.input field={@form[:email]} label="Email"/>
        <.input field={@form[:username]} label="Username" />
        <:actions>
          <.button>Save</.button>
        </:actions>
      </.simple_form>
  """
  attr :for, :any, required: true, doc: "the datastructure for the form"
  attr :as, :any, default: nil, doc: "the server side parameter to collect all input under"

  attr :rest, :global,
    include: ~w(autocomplete name rel action enctype method novalidate target multipart),
    doc: "the arbitrary HTML attributes to apply to the form tag"

  slot :inner_block, required: true
  slot :actions, doc: "the slot for form actions, such as a submit button"

  def simple_form(assigns) do
    ~H"""
    <.form :let={f} for={@for} as={@as} {@rest}>
      <fieldset class="fieldset">
        {render_slot(@inner_block, f)}
        <div :for={action <- @actions} class="mt-2 flex items-center justify-between gap-6">
          {render_slot(action, f)}
        </div>
      </fieldset>
    </.form>
    """
  end

  @doc """
  Generates a generic error message.
  """
  slot :inner_block, required: true

  def error(assigns) do
    ~H"""
    <p class="validator-hint phx-no-feedback:hidden">
      <.icon name="hero-exclamation-circle-mini" class="mt-0.5 h-5 w-5 flex-none" />
      {render_slot(@inner_block)}
    </p>
    """
  end

  @doc """
  Renders a status badge using DaisyUI classes.

  ## Examples

      <.badge variant="success">Synced</.badge>
      <.badge variant="info">Syncing...</.badge>
      <.badge variant="error">Failed</.badge>
      <.badge variant="warning">Pending</.badge>

  """
  attr :variant, :string,
    default: "default",
    values: ~w(default primary secondary accent info success warning error)

  attr :size, :string,
    default: "sm",
    values: ~w(xs sm md lg)

  attr :class, :string, default: ""
  attr :rest, :global

  slot :inner_block, required: true

  def badge(assigns) do
    variant_class =
      case assigns.variant do
        "default" -> ""
        "primary" -> "badge-primary"
        "secondary" -> "badge-secondary"
        "accent" -> "badge-accent"
        "info" -> "badge-info"
        "success" -> "badge-success"
        "warning" -> "badge-warning"
        "error" -> "badge-error"
      end

    size_class =
      case assigns.size do
        "xs" -> "badge-xs"
        "sm" -> "badge-sm"
        "md" -> "badge-md"
        "lg" -> "badge-lg"
      end

    badge_classes = ["badge", variant_class, size_class, assigns.class]
    assigns = assign(assigns, :badge_classes, badge_classes)

    ~H"""
    <span class={@badge_classes} {@rest}>
      {render_slot(@inner_block)}
    </span>
    """
  end

  @doc """
  Renders a pull request badge.

  ## Examples

      <.pull_request_badge pull_request={pull_request} />

  """
  attr :pull_request, :map, required: true, doc: "The pull request to display"

  def pull_request_badge(assigns) do
    ~H"""
    <a
      href={@pull_request.pull_request_url}
      target="_blank"
      rel="noopener noreferrer"
      class="btn btn-success btn-xs"
    >
      <.icon name="hero-arrow-up-right" class="w-3 h-3 mr-1" />
      PR #{@pull_request.pull_request_number}
    </a>
    """
  end

  @doc """
  A masked input field that shows only part of the API key.
  Shows first 6 and last 4 characters, with asterisks in between.
  """
  attr :field, Phoenix.HTML.FormField, default: nil, doc: "a form field struct"
  attr :id, :string, default: nil, doc: "the id of the input field"
  attr :name, :string, default: nil, doc: "the name of the input field"
  attr :value, :string, default: nil, doc: "the value of the input field"
  attr :class, :string, default: nil, doc: "additional css classes"
  attr :placeholder, :string, default: nil, doc: "placeholder text"
  attr :rest, :global, doc: "additional HTML attributes"

  def masked_api_key_input(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    assigns
    |> assign(field: nil)
    |> assign(:errors, Enum.map(field.errors, &translate_error(&1)))
    |> assign(:name, field.name)
    |> assign(:value, field.value)
    |> assign(:id, field.id)
    |> masked_api_key_input()
  end

  def masked_api_key_input(assigns) do
    assigns =
      assigns
      |> assign_new(:errors, fn -> [] end)
      |> assign_new(:name, fn -> assigns[:id] end)
      |> assign(:displayed_value, if(assigns.value, do: mask_api_key(assigns.value), else: ""))

    ~H"""
    <div phx-feedback-for={@name}>
      <input
        type="text"
        name={"#{@name}_display"}
        id={"#{@id}_display"}
        value={@displayed_value}
        placeholder={@placeholder}
        class={[
          "input",
          @errors != [] && "input-error",
          @class
        ]}
        phx-change={JS.dispatch("input_changed", to: "##{@id}_hidden")}
      />
      <input type="hidden" name={@name} id={"#{@id}_hidden"} value={@value} phx-hook="UpdateApiKey" />
      <.error :for={msg <- @errors}>{msg}</.error>
    </div>
    """
  end

  defp mask_api_key(nil), do: nil
  defp mask_api_key(""), do: ""

  defp mask_api_key(value) when byte_size(value) <= 10,
    do: String.duplicate("*", byte_size(value))

  defp mask_api_key(value) do
    prefix = String.slice(value, 0..5)
    suffix = String.slice(value, -4..-1)
    "#{prefix}#{String.duplicate("*", byte_size(value) - 10)}#{suffix}"
  end

  @doc """
  Renders a GitHub app installation banner.

  ## Examples

      <.github_app_installation_banner github_app_installation_required={@github_app_installation_required} github_app_install_url={@github_app_install_url} />
  """
  attr :github_app_installation_required, :boolean, default: false
  attr :github_app_install_url, :string, default: nil

  def github_app_installation_banner(assigns) do
    ~H"""
    <div
      :if={@github_app_installation_required}
      class="w-full bg-yellow-50 border border-yellow-200 text-yellow-900 px-4 py-3 rounded-lg flex items-center justify-between gap-4 mb-4 shadow relative"
    >
      <div class="flex items-center gap-2">
        <.icon name="hero-exclamation-triangle" class="h-6 w-6 text-yellow-500" />
        <span>
          GitHub App is not installed for this organization. Some features will not work.
          <a
            href={@github_app_install_url <> "/installations/new"}
            class="underline font-semibold text-yellow-800 hover:text-yellow-700 ml-1"
          >
            Install the GitHub App
          </a>
        </span>
      </div>
      <button
        type="button"
        onclick="this.parentElement.style.display='none'"
        class="text-yellow-500 hover:text-yellow-700 focus:outline-none"
      >
        <.icon name="hero-x-mark" class="h-5 w-5" />
      </button>
    </div>
    """
  end

  @doc """
  Renders a content container with header, optional description, and body.

  This component encapsulates the common pattern used across the app for
  content sections with a header area and main content below.

  ## Examples

      <.content>
        <:header>Repository Files</:header>
        <:description>Files in this repository</:description>
        <:body>
          <div class="p-4">Content goes here</div>
        </:body>
      </.content>

      <.content>
        <:header>Simple Header</:header>
        <:actions>
          <.btn variant="soft">Action</.btn>
        </:actions>
        <:body>
          <div class="p-4">Content without description</div>
        </:body>
      </.content>

  """
  attr :class, :string, default: "", doc: "Additional CSS classes for the container"

  slot :header, required: true, doc: "The header content (title)"
  slot :description, doc: "Optional description text below the header"
  slot :actions, doc: "Optional actions to display on the right side of the header"
  slot :body, required: true, doc: "The main content body"

  def content(assigns) do
    ~H"""
    <div class={["bg-white rounded-lg shadow-sm border border-slate-200", @class]}>
      <div class={[
        "px-6 py-5 border-b border-slate-200 bg-slate-50 rounded-t-lg overflow-hidden",
        if(@actions != [], do: "flex justify-between items-center", else: "")
      ]}>
        <div>
          <h2 class="text-lg font-medium text-slate-900">
            {render_slot(@header)}
          </h2>
          <p :if={@description != []} class="mt-1 text-sm text-slate-600">
            {render_slot(@description)}
          </p>
        </div>
        <div :if={@actions != []} class="flex-none">
          {render_slot(@actions)}
        </div>
      </div>
      <div class="divide-y divide-slate-200 rounded-b-lg overflow-hidden">
        {render_slot(@body)}
      </div>
    </div>
    """
  end

  @doc """
  Renders a collapsible content container with proper rounded corner handling.

  This component handles the styling for collapsed vs expanded states correctly,
  ensuring rounded corners are applied appropriately.

  ## Examples

      <.collapsible_content id="my-content" title="Content" expanded={false}>
        <div class="p-4">Collapsible content goes here</div>
      </.collapsible_content>

  """
  attr :id, :string, required: true, doc: "Unique ID for the collapsible content"
  attr :title, :string, required: true, doc: "Title for the header"
  attr :expanded, :boolean, default: false, doc: "Whether the content is initially expanded"
  attr :class, :string, default: "", doc: "Additional CSS classes for the container"

  slot :inner_block, required: true, doc: "The collapsible content"

  def collapsible_content(assigns) do
    assigns = assign_new(assigns, :content_id, fn -> "#{assigns.id}-content" end)

    ~H"""
    <style>
      .collapsible-container .collapsible-header {
        border-radius: 0.5rem;
      }
      .collapsible-container .collapsible-header.expanded {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        border-bottom: 1px solid #e2e8f0; /* slate-200 */
      }
      .collapsible-container .collapsible-header .chevron-icon {
        transform: rotate(0deg);
        transition: transform 0.2s ease-in-out;
      }
      .collapsible-container .collapsible-header.expanded .chevron-icon {
        transform: rotate(-180deg);
      }
    </style>
    <div
      class={["bg-white rounded-lg shadow-sm border border-slate-200 collapsible-container", @class]}
      id={@id}
    >
      <div class={["collapsible-header px-6 py-5 bg-slate-50", @expanded && "expanded"]}>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-slate-900">{@title}</h2>
          <button
            type="button"
            phx-click={
              JS.toggle(to: "##{@content_id}")
              |> JS.toggle_class("expanded", to: "##{@id} .collapsible-header")
            }
            class="text-slate-500 hover:text-slate-700"
          >
            <.icon name="hero-chevron-down" class="h-5 w-5 chevron-icon" />
          </button>
        </div>
      </div>
      <div id={@content_id} class={["rounded-b-lg overflow-hidden", !@expanded && "hidden"]}>
        {render_slot(@inner_block)}
      </div>
    </div>
    """
  end
end
