defmodule RepobotWeb.UserMenuComponent do
  use RepobotWeb, :html

  attr :current_user, :map, required: true
  attr :current_organization, :map, required: true

  def user_menu(assigns) do
    ~H"""
    <details class="relative" style="z-index: 50">
      <summary class="flex items-center gap-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300 cursor-pointer list-none">
        <%= if @current_organization do %>
          <img
            src={@current_organization.settings.avatar_url}
            alt={@current_organization.name}
            class="h-8 w-8 rounded-full"
          />
        <% else %>
          <div class="h-8 w-8 rounded-full bg-slate-600 flex items-center justify-center">
            <svg
              class="h-5 w-5 text-slate-200"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M4 4a2 2 0 012-2h8a2 2 0 012 2v2h2a2 2 0 012 2v8a2 2 0 01-2 2H6a2 2 0 01-2-2v-2H2a2 2 0 01-2-2V6a2 2 0 012-2h2zm10 2H6v2h8V6zM4 8v8h12V8H4z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        <% end %>
        <div class="hidden md:block text-left">
          <div class="text-sm font-medium text-slate-200">
            <%= if @current_organization do %>
              {@current_organization.name}
            <% else %>
              Select Organization
            <% end %>
          </div>
          <%= if @current_organization && @current_organization.id != @current_user.default_organization_id do %>
            <div class="text-xs text-slate-300">
              {@current_user.login}
            </div>
          <% end %>
        </div>
        <svg
          class="hidden md:block h-5 w-5 text-slate-300"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </summary>
      <div class="absolute right-0 mt-2 w-56 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5">
        <div class="px-4 py-2">
          <div class="text-xs font-medium text-slate-500 uppercase tracking-wider">
            Organizations
          </div>
        </div>
        <%= for org <- (Repobot.Accounts.get_user_organizations_from_db(@current_user) |> elem(1) |> Enum.sort_by(& {if(@current_organization && &1.id == @current_organization.id, do: 0, else: 1), &1.name})) do %>
          <.link
            href={~p"/switch-organization/#{org.id}"}
            class={"flex items-center gap-2 px-4 py-2 text-sm text-slate-700 hover:bg-slate-100 #{if @current_organization && @current_organization.id == org.id, do: "bg-slate-50"}"}
          >
            <img
              :if={org.settings.avatar_url}
              src={org.settings.avatar_url}
              alt={org.name}
              class="h-5 w-5 rounded-full"
            />
            {org.name}
          </.link>
        <% end %>
        <div class="border-t border-slate-200 my-1"></div>
        <.link
          navigate={~p"/settings"}
          class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
        >
          Settings
        </.link>
        <.link
          href={@current_user.info.html_url}
          target="_blank"
          class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
        >
          GitHub Profile
        </.link>
        <div class="border-t border-slate-200"></div>
        <.link
          href={~p"/auth/logout"}
          method="delete"
          class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
        >
          Log out
        </.link>
      </div>
    </details>
    """
  end
end
