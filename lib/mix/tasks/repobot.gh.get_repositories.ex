defmodule Mix.Tasks.Repobot.Gh.GetRepositories do
  @moduledoc """
  Fetches repositories from a GitHub user and stores them as JSON fixtures.

  ## Usage

      mix repobot.gh.get_repositories username

  This will fetch all repositories for the given username and store them in
  test/fixtures/username_repos.json
  """

  use Mix.Task
  require Logger

  alias Repobot.{Repo, Accounts, Repositories}

  @impl Mix.Task
  def run([username]) do
    # Start required applications
    [:postgrex, :ecto, :repobot]
    |> Enum.each(&Application.ensure_all_started/1)

    # Get the user from the database
    case Repo.get_by(Accounts.User, login: username) do
      nil ->
        Mix.raise("User #{username} not found in the database")

      user ->
        # Fetch repositories and store them as JSON
        case Repositories.user_repositories(user, :refresh, user.default_organization_id) do
          repositories when is_list(repositories) ->
            # Create fixtures directory if it doesn't exist
            File.mkdir_p!("test/fixtures")

            # Convert repositories to JSON-friendly format
            json_data =
              repositories
              |> Enum.map(fn repo ->
                %{
                  id: repo.id,
                  name: repo.name,
                  owner: repo.owner,
                  full_name: repo.full_name,
                  language: repo.language,
                  fork: repo.fork,
                  data: repo.data,
                  settings: repo.settings
                }
              end)
              |> Jason.encode!(pretty: true)

            # Write to file
            filename = "test/fixtures/#{username}_repos.json"
            File.write!(filename, json_data)

            Mix.shell().info(
              "Successfully stored #{length(repositories)} repositories in #{filename}"
            )

          error ->
            Mix.raise("Failed to fetch repositories: #{inspect(error)}")
        end
    end
  end

  def run(_) do
    Mix.raise("Expected exactly one argument: username")
  end
end
