defmodule Repobot.RepositorySourceFile do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "repository_source_files" do
    belongs_to :repository, Repobot.Repository, type: :binary_id
    belongs_to :source_file, Repobot.SourceFile, type: :binary_id

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(repository_source_file, attrs) do
    repository_source_file
    |> cast(attrs, [:repository_id, :source_file_id])
    |> validate_required([:repository_id, :source_file_id])
    |> unique_constraint([:repository_id, :source_file_id])
  end
end
