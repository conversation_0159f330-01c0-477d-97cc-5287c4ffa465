defmodule Repobot.RepositoryFolder do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "repository_folders" do
    belongs_to :repository, Repobot.Repository, type: :binary_id
    belongs_to :folder, Repobot.Folder, type: :binary_id

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(repository_folder, attrs) do
    repository_folder
    |> cast(attrs, [:repository_id, :folder_id])
    |> validate_required([:repository_id, :folder_id])
    |> unique_constraint([:repository_id, :folder_id])
  end
end
