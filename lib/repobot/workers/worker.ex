defmodule Repobot.Workers.Worker do
  require Logger

  @moduledoc """
  Common Worker module with structured logging helpers for Oban workers.

  This module provides a set of utilities and helpers to streamline
  structured logging and error handling in Oban workers.

  ## Usage

      defmodule MyApp.Workers.MyWorker do
        use Repobot.Workers.Worker, queue: :default, max_attempts: 3

        @impl Oban.Worker
        def perform(%Oban.Job{args: args} = job) do
          log_job_start(job)

          case do_work(args) do
            {:ok, result} ->
              log_job_success(job, %{result: result})
              :ok

            {:error, reason} ->
              log_job_error(job, reason)
              {:error, reason}
          end
        end

        defp do_work(args) do
          # Your worker logic here
          {:ok, "work completed"}
        end
      end
  """

  defmacro __using__(opts) do
    quote do
      use Oban.Worker, unquote(opts)

      require Logger
      import Repobot.Workers.Worker

      alias Repobot.{Repo}
    end
  end

  @doc """
  Logs the start of a job with structured metadata.
  """
  def log_job_start(%Oban.Job{} = job) do
    Logger.info("Job started",
      event: "job_started",
      job_id: job.id,
      worker: job.worker,
      queue: job.queue,
      attempt: job.attempt,
      max_attempts: job.max_attempts,
      args: sanitize_args(job.args)
    )
  end

  @doc """
  Logs successful job completion with structured metadata.
  """
  def log_job_success(%Oban.Job{} = job, metadata \\ %{}) do
    Logger.info(
      "Job completed successfully",
      [
        event: "job_success",
        job_id: job.id,
        worker: job.worker,
        queue: job.queue,
        attempt: job.attempt,
        result: "success"
      ] ++ Map.to_list(metadata)
    )
  end

  @doc """
  Logs job errors with structured metadata.
  """
  def log_job_error(%Oban.Job{} = job, reason, metadata \\ %{}) do
    Logger.error(
      "Job failed",
      [
        event: "job_error",
        job_id: job.id,
        worker: job.worker,
        queue: job.queue,
        attempt: job.attempt,
        max_attempts: job.max_attempts,
        reason: format_error_reason(reason),
        result: "error"
      ] ++ Map.to_list(metadata)
    )
  end

  @doc """
  Logs job progress with structured metadata.
  """
  def log_job_progress(%Oban.Job{} = job, progress, metadata \\ %{}) do
    Logger.info(
      "Job progress update",
      [
        event: "job_progress",
        job_id: job.id,
        worker: job.worker,
        queue: job.queue,
        progress: progress
      ] ++ Map.to_list(metadata)
    )
  end

  @doc """
  Logs job retry with structured metadata.
  """
  def log_job_retry(%Oban.Job{} = job, reason, metadata \\ %{}) do
    Logger.warning(
      "Job will be retried",
      [
        event: "job_retry",
        job_id: job.id,
        worker: job.worker,
        queue: job.queue,
        attempt: job.attempt,
        max_attempts: job.max_attempts,
        reason: format_error_reason(reason),
        result: "retry"
      ] ++ Map.to_list(metadata)
    )
  end

  @doc """
  Safely sends a message to a receiver, handling both PID and encoded PID formats.
  """
  def safe_send(receiver_pid, message) when is_binary(receiver_pid) do
    try do
      pid = :erlang.binary_to_term(Base.decode64!(receiver_pid))
      send(pid, message)
    rescue
      e ->
        Logger.error("Failed to send message to receiver",
          event: "send_error",
          reason: Exception.message(e),
          receiver: receiver_pid
        )

        :error
    end
  end

  def safe_send(receiver_pid, message) when is_pid(receiver_pid) do
    send(receiver_pid, message)
  end

  def safe_send({receiver_pid, component}, message)
      when is_binary(receiver_pid) and is_binary(component) do
    try do
      pid = :erlang.binary_to_term(Base.decode64!(receiver_pid))
      component_atom = String.to_existing_atom(component)
      send(pid, {component_atom, message})
    rescue
      e ->
        Logger.error("Failed to send message to receiver with component",
          event: "send_error",
          reason: Exception.message(e),
          receiver: receiver_pid,
          component: component
        )

        :error
    end
  end

  def safe_send({receiver_pid, component}, message)
      when is_pid(receiver_pid) and is_atom(component) do
    send(receiver_pid, {component, message})
  end

  @doc """
  Encodes a PID for safe serialization in job arguments.
  """
  def encode_pid(pid) when is_pid(pid) do
    pid |> :erlang.term_to_binary() |> Base.encode64()
  end

  # Private helper functions

  defp sanitize_args(args) when is_map(args) do
    args
    |> Enum.map(fn {k, v} -> {k, sanitize_value(v)} end)
    |> Map.new()
  end

  defp sanitize_args(args), do: args

  defp sanitize_value(value) when is_binary(value) and byte_size(value) > 1000 do
    "[TRUNCATED: #{byte_size(value)} bytes]"
  end

  defp sanitize_value(value), do: value

  defp format_error_reason(reason) when is_binary(reason), do: reason
  defp format_error_reason(reason) when is_atom(reason), do: Atom.to_string(reason)
  defp format_error_reason(%{message: message}), do: message
  defp format_error_reason(reason), do: inspect(reason)
end
