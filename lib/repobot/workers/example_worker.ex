defmodule Repobot.Workers.ExampleWorker do
  @moduledoc """
  Example worker demonstrating the usage of the common Worker module.
  
  This worker shows how to use structured logging and error handling
  with the Repobot.Workers.Worker module.
  """
  
  use Repobot.Workers.Worker, queue: :default, max_attempts: 3
  
  @impl Oban.Worker
  def perform(%Oban.Job{args: %{"action" => "greet", "name" => name}} = job) do
    log_job_start(job)
    
    case greet(name) do
      {:ok, message} ->
        log_job_success(job, %{message: message})
        :ok
        
      {:error, reason} ->
        log_job_error(job, reason)
        {:error, reason}
    end
  end
  
  def perform(%Oban.Job{args: %{"action" => "fail"}} = job) do
    log_job_start(job)
    
    reason = "Intentional failure for testing"
    log_job_error(job, reason)
    {:error, reason}
  end
  
  def perform(%Oban.Job{args: %{"action" => "progress"}} = job) do
    log_job_start(job)
    
    # Simulate work with progress updates
    for i <- 1..5 do
      Process.sleep(1000)
      progress = i * 20
      log_job_progress(job, progress, %{step: i, total_steps: 5})
    end
    
    log_job_success(job, %{completed_steps: 5})
    :ok
  end
  
  def perform(%Oban.Job{} = job) do
    log_job_start(job)
    
    reason = "Unknown action in job arguments"
    log_job_error(job, reason, %{received_args: job.args})
    {:error, reason}
  end
  
  defp greet(name) when is_binary(name) and name != "" do
    {:ok, "Hello, #{name}!"}
  end
  
  defp greet(_name) do
    {:error, "Name must be a non-empty string"}
  end
end
