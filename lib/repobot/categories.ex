defmodule Repobot.Categories do
  @moduledoc """
  The Categories context.
  """

  import Ecto.Query

  alias Repobot.Repo
  alias Repobot.Category

  @doc """
  Returns the list of categories for a given organization.
  """
  def list_categories(organization) do
    Category
    |> where(organization_id: ^organization.id)
    |> order_by(asc: :name)
    |> Repo.all()
  end

  @doc """
  Gets a single category.
  Raises `Ecto.NoResultsError` if the Category does not exist.
  """
  def get_category!(id), do: Repo.get!(Category, id)

  @doc """
  Creates a category.
  """
  def create_category(attrs \\ %{}) do
    %Category{}
    |> Category.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a category.
  """
  def update_category(%Category{} = category, attrs) do
    category
    |> Category.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a category.
  """
  def delete_category(%Category{} = category) do
    Repo.delete(category)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking category changes.
  """
  def change_category(%Category{} = category, attrs \\ %{}) do
    Category.changeset(category, attrs)
  end
end
