defmodule Repobot.Tag do
  use Ecto.Schema
  import Ecto.Changeset
  alias Repobot.Tags

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "tags" do
    field :name, :string
    field :color, :string

    belongs_to :user, Repobot.Accounts.User
    belongs_to :organization, Repobot.Accounts.Organization

    many_to_many :source_files, Repobot.SourceFile,
      join_through: "source_file_tags",
      on_replace: :delete

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(tag, attrs \\ %{}) do
    tag
    |> cast(attrs, [:name, :user_id, :organization_id])
    |> validate_required([:name, :user_id, :organization_id])
    |> unique_constraint([:user_id, :name])
    |> put_color()
  end

  defp put_color(%{valid?: true, changes: %{user_id: user_id}} = changeset) do
    put_change(changeset, :color, Tags.next_available_color(user_id))
  end

  defp put_color(changeset), do: changeset
end
