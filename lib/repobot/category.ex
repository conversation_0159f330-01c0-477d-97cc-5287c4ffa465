defmodule Repobot.Category do
  use Ecto.Schema
  import Ecto.Changeset

  @derive {Jason.Encoder, only: [:id, :name]}

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "categories" do
    field :name, :string

    belongs_to :organization, Repobot.Accounts.Organization
    has_many :source_files, Repobot.SourceFile

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(category, attrs \\ %{}) do
    category
    |> cast(attrs, [:name, :organization_id])
    |> validate_required([:name, :organization_id])
    |> unique_constraint([:organization_id, :name], name: :categories_organization_id_name_index)
  end
end
