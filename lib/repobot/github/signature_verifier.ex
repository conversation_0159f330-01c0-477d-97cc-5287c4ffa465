defmodule Repobot.GitHub.SignatureVerifier do
  @moduledoc """
  Behaviour for verifying GitHub webhook signatures.
  """

  @callback verify_signature(Plug.Conn.t()) :: :ok | {:error, String.t()}
end

defmodule Repobot.GitHub.SignatureVerifier.Default do
  @moduledoc """
  Default implementation of GitHub webhook signature verification.
  """

  @behaviour Repobot.GitHub.SignatureVerifier

  alias Plug.Conn

  @github_signature_header "x-hub-signature"

  @impl true
  def verify_signature(conn) do
    case Conn.get_req_header(conn, @github_signature_header) do
      [signature_header] ->
        [method, their_digest] = String.split(signature_header, "=")
        secret = webhook_secret()
        raw_body = (conn.assigns[:raw_body] || []) |> Enum.reverse() |> Enum.join()
        hash_method = if method == "sha1", do: :sha, else: String.to_atom(method)

        our_digest =
          :crypto.mac(
            :hmac,
            hash_method,
            secret,
            raw_body
          )
          |> Base.encode16(case: :lower)

        if Plug.Crypto.secure_compare(their_digest, our_digest) do
          :ok
        else
          {:error, "Invalid signature"}
        end

      _ ->
        {:error, "Missing signature"}
    end
  end

  defp webhook_secret do
    Application.get_env(:repobot, :github_app)[:webhook_secret] ||
      raise "GitHub webhook secret not configured"
  end
end
