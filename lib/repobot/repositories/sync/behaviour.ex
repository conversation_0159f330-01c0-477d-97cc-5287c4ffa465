defmodule Repobot.Repositories.Sync.Behaviour do
  @moduledoc """
  Behaviour for synchronizing files between repositories.
  """

  alias Repobot.{Repository, SourceFile}

  @doc """
  Synchronizes a source file to a target repository, either through a direct update or pull request
  based on the template repository's sync_mode setting.

  Returns:
  - `{:ok, url}` where url is the PR URL if sync_mode is :pr
  - `{:ok, "File updated successfully"}` if sync_mode is :direct
  - `{:ok, "No changes needed - content is identical"}` if content is the same
  - `{:error, reason}` if synchronization fails
  """
  @callback sync_file(
              source_file :: SourceFile.t(),
              template_repo :: Repository.t(),
              target_repo :: Repository.t(),
              github_client :: term()
            ) :: {:ok, String.t()} | {:error, String.t()}

  @callback sync_file(
              source_file :: SourceFile.t(),
              template_repo :: Repository.t(),
              target_repo :: Repository.t(),
              github_client :: term(),
              opts :: keyword()
            ) :: {:ok, String.t()} | {:error, String.t()}

  @doc """
  Synchronizes multiple source files to a target repository in a batch, maintaining commit structure.
  Either creates a single PR with all changes or pushes all changes directly.

  Returns:
  - `{:ok, url}` where url is the PR URL if sync_mode is :pr
  - `{:ok, "Files updated successfully"}` if sync_mode is :direct
  - `{:error, reason}` if synchronization fails
  """
  @callback sync_changes(
              source_files :: [SourceFile.t()],
              template_repo :: Repository.t(),
              target_repo :: Repository.t(),
              github_client :: term()
            ) :: {:ok, String.t()} | {:error, String.t()}

  @callback sync_changes(
              source_files :: [SourceFile.t()],
              template_repo :: Repository.t(),
              target_repo :: Repository.t(),
              github_client :: term(),
              opts :: keyword()
            ) :: {:ok, String.t()} | {:error, String.t()}

  @optional_callbacks sync_file: 5, sync_changes: 5
end
