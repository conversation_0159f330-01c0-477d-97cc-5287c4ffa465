defmodule Repobot.Waitlist do
  @moduledoc """
  The Waitlist context.
  """

  import Ecto.Query, warn: false
  require <PERSON><PERSON>
  alias <PERSON><PERSON>ot.Repo
  alias Repobot.Waitlist.Entry

  def list_entries(opts \\ []) do
    order_by = Keyword.get(opts, :order_by, desc: :inserted_at)
    Entry |> order_by(^order_by) |> Repo.all()
  end

  def create_entry(attrs \\ %{}) do
    Logger.debug("Creating waitlist entry with attrs: #{inspect(attrs)}")

    %Entry{}
    |> Entry.changeset(attrs)
    |> Repo.insert()
  end

  def get_entry!(id), do: Repo.get!(Entry, id)

  def get_entry_by_invitation_code(code) when is_binary(code) do
    Logger.debug("Looking up waitlist entry by invitation code: #{code}")

    case Repo.get_by(Entry, invitation_code: code) do
      nil ->
        Logger.error("No waitlist entry found for invitation code: #{code}")
        nil

      entry ->
        Logger.debug("Found waitlist entry: #{inspect(entry)}")
        entry
    end
  end

  def generate_invitation_code(%Entry{} = entry) do
    Logger.debug("Generating invitation code for entry: #{inspect(entry)}")
    code = :crypto.strong_rand_bytes(16) |> Base.url_encode64(padding: false)
    Logger.debug("Generated code: #{code}")

    case entry
         |> Entry.invitation_code_changeset(%{invitation_code: code})
         |> Repo.update() do
      {:ok, updated_entry} = result ->
        Logger.debug("Successfully updated entry with invitation code: #{inspect(updated_entry)}")
        result

      {:error, changeset} = error ->
        Logger.error("Failed to update entry with invitation code: #{inspect(changeset.errors)}")
        error
    end
  end

  def get_invite_url(%Entry{invitation_code: code}) when is_binary(code) do
    case Application.get_env(:repobot, :url_host) do
      nil -> nil
      url_prefix -> "#{url_prefix}/auth/github?invitation_code=#{code}"
    end
  end

  def delete_entry(%Entry{} = entry) do
    Repo.delete(entry)
  end
end
