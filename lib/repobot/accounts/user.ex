defmodule Repobot.Accounts.User do
  use Ecto.Schema

  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  # Embedded schema for user info
  defmodule Info do
    use Ecto.Schema

    @primary_key false
    embedded_schema do
      field :name, :string
      field :email, :string
      field :nickname, :string
      field :description, :string
      field :avatar_url, :string
      field :html_url, :string
      field :location, :string
    end
  end

  # Embedded schema for user settings with encrypted fields
  defmodule Settings do
    use Ecto.Schema
    import Ecto.Changeset

    @primary_key false
    embedded_schema do
      field :anthropic_api_key, Repobot.Encrypted.String
      field :openai_api_key, Repobot.Encrypted.String
      field :onboarding_completed, :boolean, default: false
    end

    def changeset(settings, attrs) do
      settings
      |> cast(attrs, [:anthropic_api_key, :openai_api_key, :onboarding_completed])
    end
  end

  schema "users" do
    field :login, :string
    field :email, :string
    field :token, :string
    field :refresh_token, :string
    field :expires_at, :integer
    field :refresh_token_expires_at, :integer
    embeds_one :info, Info, on_replace: :update
    embeds_one :settings, Settings, on_replace: :update

    belongs_to :default_organization, Repobot.Accounts.Organization

    many_to_many :organizations, Repobot.Accounts.Organization,
      join_through: Repobot.Accounts.UserOrganization,
      on_replace: :delete

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(user, attrs \\ %{}) do
    user
    |> cast(attrs, [
      :login,
      :email,
      :token,
      :refresh_token,
      :expires_at,
      :refresh_token_expires_at,
      :default_organization_id
    ])
    |> cast_embed(:info, with: &info_changeset/2)
    |> cast_embed(:settings, with: &Settings.changeset/2)
    |> validate_required([:login, :token, :refresh_token, :expires_at, :refresh_token_expires_at])
    |> unique_constraint(:login)
  end

  def info_changeset(info, attrs) do
    info
    |> cast(attrs, [:name, :email, :nickname, :description, :avatar_url, :html_url, :location])
  end
end
