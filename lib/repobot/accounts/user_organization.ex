defmodule Repobot.Accounts.UserOrganization do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "users_organizations" do
    field :role, :string, default: "member"

    belongs_to :user, Repobot.Accounts.User
    belongs_to :organization, Repobot.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(user_organization, attrs) do
    user_organization
    |> cast(attrs, [:user_id, :organization_id, :role])
    |> validate_required([:user_id, :organization_id, :role])
    |> unique_constraint([:user_id, :organization_id],
      name: :user_id_organization_id_unique_index
    )
  end
end
