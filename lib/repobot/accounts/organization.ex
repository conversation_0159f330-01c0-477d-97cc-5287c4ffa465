defmodule Repobot.Accounts.Organization do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "organizations" do
    field :name, :string
    field :installation_id, :integer
    field :private_repos, :boolean, default: false

    has_one :settings, Repobot.Accounts.OrganizationSettings, on_delete: :delete_all

    has_many :default_users, Repobot.Accounts.User,
      foreign_key: :default_organization_id,
      on_delete: :nilify_all

    many_to_many :users, Repobot.Accounts.User,
      join_through: Repobot.Accounts.UserOrganization,
      on_replace: :delete,
      on_delete: :delete_all

    has_many :source_files, Repobot.SourceFile, on_delete: :delete_all
    has_many :repositories, Repobot.Repository, on_delete: :delete_all
    has_many :folders, Repobot.Folder, on_delete: :delete_all
    has_many :tags, Repobot.Tag, on_delete: :delete_all

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(organization, attrs \\ %{}) do
    organization
    |> cast(attrs, [:name, :installation_id, :private_repos])
    |> validate_required([:name])
    |> unique_constraint(:name)
  end
end
