defmodule Repobot.Accounts.OrganizationSettings do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "organization_settings" do
    field :anthropic_api_key, Repobot.Encrypted.String
    field :openai_api_key, Repobot.Encrypted.String
    field :avatar_url, :string
    field :html_url, :string

    belongs_to :organization, Repobot.Accounts.Organization

    timestamps(type: :utc_datetime)
  end

  def changeset(settings, attrs) do
    settings
    |> cast(attrs, [:anthropic_api_key, :openai_api_key, :avatar_url, :html_url, :organization_id])
    |> validate_required([:organization_id])
    |> unique_constraint(:organization_id)
  end
end
