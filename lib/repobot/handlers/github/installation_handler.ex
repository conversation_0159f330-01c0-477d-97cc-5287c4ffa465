defmodule Repobot.Handlers.GitHub.InstallationHandler do
  @moduledoc """
  Handles GitHub App installation events.
  """

  require Logger

  alias Repobot.{Accounts, Repo}
  alias Repobot.Accounts.Organization

  @doc """
  Handles GitHub App installation events.
  """
  def handle(%{"action" => "created", "installation" => installation} = _payload) do
    handle_installation_created(installation)
  end

  def handle(%{"action" => "deleted", "installation" => installation} = _payload) do
    handle_installation_deleted(installation)
  end

  def handle(_payload) do
    :ok
  end

  defp handle_installation_created(%{"id" => installation_id, "account" => account}) do
    case find_organization(account) do
      nil ->
        Logger.info("No organization found for account: #{account["login"]}")
        :ok

      org ->
        Logger.info("Updating installation_id for organization: #{org.name}")

        case Repo.update(Organization.changeset(org, %{installation_id: installation_id})) do
          {:ok, _org} ->
            Logger.info("Successfully updated installation_id for organization: #{org.name}")
            :ok

          {:error, reason} ->
            Logger.error("Failed to update installation_id: #{inspect(reason)}")
            {:error, "Failed to update installation_id"}
        end
    end
  end

  defp handle_installation_deleted(%{"id" => _installation_id, "account" => account}) do
    case find_organization(account) do
      nil ->
        Logger.info("No organization found for account: #{account["login"]}")
        :ok

      org ->
        Logger.info("Removing installation_id for organization: #{org.name}")

        case Repo.update(Organization.changeset(org, %{installation_id: nil})) do
          {:ok, _org} ->
            Logger.info("Successfully removed installation_id for organization: #{org.name}")
            :ok

          {:error, reason} ->
            Logger.error("Failed to remove installation_id: #{inspect(reason)}")
            {:error, "Failed to remove installation_id"}
        end
    end
  end

  defp find_organization(%{"login" => login}) do
    case Accounts.get_organization_by_name(login) do
      nil ->
        Logger.info("Organization not found: #{login}")
        nil

      org ->
        org
    end
  end
end
