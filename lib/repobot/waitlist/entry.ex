defmodule Repobot.Waitlist.Entry do
  use Ecto.Schema
  import Ecto.Changeset

  schema "waitlist_entries" do
    field :email, :string
    field :github_username, :string
    field :invitation_code, :string
    field :used_at, :utc_datetime_usec

    timestamps()
  end

  @email_regex ~r/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  @github_username_regex ~r/^[a-zA-Z0-9](?:[a-zA-Z0-9]|-(?=[a-zA-Z0-9])){0,38}$/

  @doc false
  def changeset(entry, attrs) do
    entry
    |> cast(attrs, [:email, :github_username])
    |> validate_required([:email, :github_username])
    |> validate_length(:email, max: 255)
    |> validate_length(:github_username, min: 1, max: 39)
    |> validate_format(:email, @email_regex, message: "must be a valid email address")
    |> validate_format(:github_username, @github_username_regex,
      message: "must be a valid GitHub username"
    )
    |> validate_no_disposable_email(:email)
    |> downcase_email()
    |> unique_constraint(:email)
  end

  @doc """
  Changeset for updating the invitation code.
  """
  def invitation_code_changeset(entry, attrs) do
    entry
    |> cast(attrs, [:invitation_code])
    |> validate_required([:invitation_code])
    |> unique_constraint(:invitation_code)
  end

  @doc """
  Changeset for marking the invitation as used.
  """
  def mark_as_used_changeset(entry) do
    change(entry, %{used_at: DateTime.utc_now()})
  end

  defp downcase_email(changeset) do
    case get_change(changeset, :email) do
      nil -> changeset
      email -> put_change(changeset, :email, String.downcase(email))
    end
  end

  # Basic check for common disposable email domains
  @disposable_domains [
    "tempmail.com",
    "throwawaymail.com",
    "mailinator.com",
    "guerrillamail.com",
    "10minutemail.com",
    "yopmail.com",
    "temp-mail.org",
    "tempmail.net"
  ]

  defp validate_no_disposable_email(changeset, field) do
    validate_change(changeset, field, fn _, email ->
      domain = email |> String.split("@") |> List.last() |> String.downcase()

      if Enum.member?(@disposable_domains, domain) do
        [{field, "disposable email addresses are not allowed"}]
      else
        []
      end
    end)
  end
end
