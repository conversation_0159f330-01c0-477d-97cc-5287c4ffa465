defmodule Number.Bytes do
  @moduledoc """
  Formats byte sizes into human-readable strings.
  """

  @doc """
  Converts a number of bytes to a human-readable string.

  ## Examples

      iex> Number.Bytes.to_string(1234)
      "1.21 KB"

      iex> Number.Bytes.to_string(1234567)
      "1.18 MB"

      iex> Number.Bytes.to_string(1234567890)
      "1.15 GB"
  """
  def to_string(bytes) when is_integer(bytes) do
    cond do
      bytes >= 1_000_000_000 -> "#{Float.round(bytes / 1_000_000_000, 2)} GB"
      bytes >= 1_000_000 -> "#{Float.round(bytes / 1_000_000, 2)} MB"
      bytes >= 1_000 -> "#{Float.round(bytes / 1_000, 2)} KB"
      true -> "#{bytes} B"
    end
  end
end
