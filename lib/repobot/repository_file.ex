defmodule Repobot.RepositoryFile do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "repository_files" do
    field :path, :string
    field :name, :string
    field :type, :string
    field :size, :integer
    field :sha, :string
    field :content, Repobot.Base64Content
    field :content_updated_at, :utc_datetime

    belongs_to :repository, Repobot.Repository

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(repository_file, attrs \\ %{}) do
    repository_file
    |> cast(attrs, [
      :path,
      :name,
      :type,
      :size,
      :sha,
      :content,
      :content_updated_at,
      :repository_id
    ])
    |> validate_required([:path, :name, :type, :repository_id])
    |> foreign_key_constraint(:repository_id)
    |> unique_constraint([:repository_id, :path],
      name: :repository_files_repository_id_path_index
    )
  end
end
