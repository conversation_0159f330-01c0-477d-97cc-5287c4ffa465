defmodule Repobot.Files.RepoTree do
  @moduledoc """
  Handles loading and tracking state of repository file trees.
  """

  require Logger

  alias <PERSON>ob<PERSON>.{Repositories, Repo}

  @type loading_state :: :not_started | :loading | :loaded | {:error, String.t()}
  @type repo_id :: binary()
  @type loading_states :: %{repo_id() => loading_state()}
  @type progress :: integer()

  @doc """
  Initializes loading states for the given repositories.
  Returns {loading_states, repositories_needing_refresh}.
  """
  @spec init_loading([Repobot.Repository.t()]) :: {loading_states(), [Repobot.Repository.t()]}
  def init_loading(repositories) do
    repositories
    |> Enum.reduce({%{}, []}, fn repo, {states, refresh_list} ->
      repo = Repo.preload(repo, :files)

      if Enum.empty?(repo.files) do
        {Map.put(states, repo.id, :loading), [repo | refresh_list]}
      else
        {Map.put(states, repo.id, :loaded), refresh_list}
      end
    end)
  end

  @doc """
  Starts loading trees for repositories that need refresh.
  Takes a list of repositories and a receiver to send state updates to.

  The receiver can be either:
  - A PID: Messages will be sent directly to this process
  - A tuple {pid, component_name}: Messages will be sent to the PID with component_name included
    to allow routing to the appropriate component

  ## Messages sent to receiver:
  - {:tree_loaded, repo_id} - When a repository's tree is loaded
  - {:tree_load_failed, repo_id, reason} - When loading fails

  When using {pid, component_name} format, messages will be:
  - {:repo_tree, component_name, {:tree_loaded, repo_id}}
  - {:repo_tree, component_name, {:tree_load_failed, repo_id, reason}}
  """
  @spec load_trees([Repobot.Repository.t()], pid() | {pid(), atom()}, Repobot.Accounts.User.t()) ::
          :ok
  def load_trees(repositories, receiver, user) do
    Enum.each(repositories, fn repo ->
      Task.start_link(fn ->
        case Repositories.refresh_repository_files!(repo.id, user) do
          {:ok, _} ->
            send_to_receiver(receiver, {:tree_loaded, repo.id})

          {:error, reason} ->
            Logger.error(
              "Failed to sync repository files for #{repo.full_name}: #{inspect(reason)}"
            )

            send_to_receiver(receiver, {:tree_load_failed, repo.id, reason})
        end
      end)
    end)

    :ok
  end

  @doc """
  Updates loading states based on received message.
  Returns updated loading states map and whether all trees are loaded.
  """
  @spec handle_tree_message(loading_states(), term()) ::
          {loading_states(), boolean()}
  def handle_tree_message(loading_states, message) do
    case message do
      {:tree_loaded, repo_id} ->
        new_states = Map.put(loading_states, repo_id, :loaded)
        {new_states, all_trees_loaded?(new_states)}

      {:tree_load_failed, repo_id, reason} ->
        new_states = Map.put(loading_states, repo_id, {:error, reason})
        {new_states, false}

      _ ->
        {loading_states, false}
    end
  end

  @doc """
  Checks if all trees are loaded in the given loading states.
  """
  @spec all_trees_loaded?(loading_states()) :: boolean()
  def all_trees_loaded?(loading_states) do
    Enum.all?(loading_states, fn {_repo_id, state} -> state == :loaded end)
  end

  @doc """
  Gets the loading state for a specific repository.
  """
  @spec get_loading_state(loading_states(), repo_id()) :: loading_state()
  def get_loading_state(loading_states, repo_id) do
    Map.get(loading_states, repo_id, :not_started)
  end

  @doc """
  Checks if any trees are still loading.
  """
  @spec loading?(loading_states()) :: boolean()
  def loading?(loading_states) do
    Enum.any?(loading_states, fn {_repo_id, state} -> state == :loading end)
  end

  @doc """
  Gets list of repository IDs that failed to load.
  """
  @spec failed_repos(loading_states()) :: [{repo_id(), String.t()}]
  def failed_repos(loading_states) do
    loading_states
    |> Enum.filter(fn {_repo_id, state} -> match?({:error, _}, state) end)
    |> Enum.map(fn {repo_id, {:error, reason}} -> {repo_id, reason} end)
  end

  @doc """
  Refreshes content for all files in the given repositories.
  Takes a list of repositories and a receiver to send progress updates to.

  The receiver can be either:
  - A PID: Messages will be sent directly to this process
  - A tuple {pid, component_name}: Messages will be sent to the PID with component_name included
    to allow routing to the appropriate component

  ## Messages sent to receiver:
  - {:content_refresh_progress, progress, status} - Progress from 0 to 100 with status message
  - {:content_refresh_complete, refreshed_repos} - When all content is refreshed
  - {:content_refresh_error, reason} - If refresh fails

  When using {pid, component_name} format, messages will be:
  - {:repo_tree, component_name, {:content_refresh_progress, progress, status}}
  - {:repo_tree, component_name, {:content_refresh_complete, refreshed_repos}}
  - {:repo_tree, component_name, {:content_refresh_error, reason}}
  """
  @spec refresh_file_content(
          [Repobot.Repository.t()],
          pid() | {pid(), atom()},
          Repobot.Accounts.User.t()
        ) :: :ok
  def refresh_file_content(repositories, receiver, user) do
    # Get all files that need content refresh
    # Ensure repositories have files preloaded to avoid NotLoaded errors
    files_to_refresh =
      repositories
      |> Enum.flat_map(fn repo ->
        case repo.files do
          %Ecto.Association.NotLoaded{} ->
            Logger.error(
              "Repository #{repo.full_name} files association not loaded in refresh_file_content"
            )

            []

          files when is_list(files) ->
            files
        end
      end)
      |> Enum.filter(&(&1.type == "file"))

    total_files = length(files_to_refresh)

    if total_files > 0 do
      Task.start_link(fn ->
        github_api = Application.get_env(:repobot, :github_api, Repobot.GitHub)
        github_client = github_api.client(user)

        try do
          files_to_refresh
          |> Task.async_stream(
            fn file ->
              repo = Enum.find(repositories, &(&1.id == file.repository_id))

              case github_api.get_file_content(github_client, repo.owner, repo.name, file.path) do
                {:ok, content, _response} ->
                  {:ok, _} =
                    Repobot.RepositoryFiles.update_repository_file(file, %{
                      content: content,
                      content_updated_at: DateTime.utc_now() |> DateTime.truncate(:second)
                    })

                  :ok

                {:error, reason} ->
                  {:error, reason}
              end
            end,
            ordered: false,
            max_concurrency: 5,
            timeout: 30_000
          )
          |> Stream.with_index(1)
          |> Enum.reduce_while(:ok, fn {result, index}, _acc ->
            # Calculate and send progress
            progress = floor(index * 100 / total_files)
            status = "Refreshing file content (#{index}/#{total_files})"
            send_to_receiver(receiver, {:content_refresh_progress, progress, status})

            case result do
              {:ok, :ok} ->
                if index == total_files do
                  # Reload repositories with fresh content
                  refreshed_repos =
                    Enum.map(repositories, &Repobot.Repo.preload(&1, :files, force: true))

                  send_to_receiver(receiver, {:content_refresh_complete, refreshed_repos})
                  {:halt, :ok}
                else
                  {:cont, :ok}
                end

              {:ok, {:error, reason}} ->
                send_to_receiver(receiver, {:content_refresh_error, reason})
                {:halt, {:error, reason}}

              {:exit, reason} ->
                send_to_receiver(receiver, {:content_refresh_error, reason})
                {:halt, {:error, reason}}
            end
          end)
        rescue
          e ->
            Logger.error(
              "Error refreshing file content: #{Exception.format(:error, e, __STACKTRACE__)}"
            )

            send_to_receiver(receiver, {:content_refresh_error, Exception.message(e)})
        end
      end)
    else
      # Reload repositories with fresh content
      refreshed_repos = Enum.map(repositories, &Repobot.Repo.preload(&1, :files, force: true))
      send_to_receiver(receiver, {:content_refresh_complete, refreshed_repos})
    end

    :ok
  end

  defp send_to_receiver(receiver_pid, message) when is_pid(receiver_pid) do
    send(receiver_pid, message)
  end

  defp send_to_receiver({pid, component_name}, message)
       when is_pid(pid) and is_atom(component_name) do
    send(pid, {:repo_tree, component_name, message})
  end
end
