defmodule Repobot.Folder do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "folders" do
    field :name, :string
    field :starred, :boolean, default: false
    field :settings, :map, default: %{}

    belongs_to :organization, Repobot.Accounts.Organization
    has_many :repositories, Repobot.Repository

    # Source files associated with this folder
    many_to_many :source_files, Repobot.SourceFile,
      join_through: Repobot.SourceFileFolder,
      on_replace: :delete

    # Template repositories that can belong to multiple folders
    many_to_many :template_repositories, Repobot.Repository,
      join_through: Repobot.RepositoryFolder,
      where: [template: true],
      on_replace: :delete

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(folder, attrs \\ %{}) do
    folder
    |> cast(attrs, [:name, :starred, :settings, :organization_id])
    |> validate_required([:name, :organization_id])
    |> unique_constraint([:name, :organization_id])
  end
end
