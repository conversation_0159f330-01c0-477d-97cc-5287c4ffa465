defmodule Repobot.SourceFileFolder do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "source_file_folders" do
    belongs_to :source_file, Repobot.SourceFile, type: :binary_id
    belongs_to :folder, Repobot.Folder, type: :binary_id

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(source_file_folder, attrs) do
    source_file_folder
    |> cast(attrs, [:source_file_id, :folder_id])
    |> validate_required([:source_file_id, :folder_id])
    |> unique_constraint([:source_file_id, :folder_id])
  end
end
