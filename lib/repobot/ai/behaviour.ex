defmodule Repobot.AI.Behaviour do
  @moduledoc """
  Behaviour for AI operations in Repobot.
  Defines callbacks for AI-powered features like tag inference.
  """

  @doc """
  Infers tags for a source file based on its properties.
  Should return a list of tag names.
  """
  @callback infer_tags(
              source_file :: Repobot.SourceFile.t(),
              organization :: Repobot.Accounts.Organization.t()
            ) ::
              {:ok, [String.t()]} | {:error, String.t()}

  @doc """
  Infers a category for a source file based on its properties.
  Should return a category name.
  """
  @callback infer_categories(
              source_files :: [Repobot.SourceFile.t()],
              organization :: Repobot.Accounts.Organization.t()
            ) ::
              {:ok, %{String.t() => String.t()}} | {:error, String.t()}

  @doc """
  Generates a Liquid template from two similar files.
  Takes into account available variables that can be used in the template.
  Returns {:ok, template} on success or {:error, reason} on failure.
  """
  @callback generate_template(
              file1 :: String.t(),
              file2 :: String.t(),
              file_path :: String.t(),
              available_variables :: %{String.t() => String.t()},
              organization :: Repobot.Accounts.Organization.t()
            ) :: {:ok, String.t()} | {:error, String.t()}
end
