defmodule Repobot.AI.NoOp do
  @moduledoc """
  No-op implementation of the Repobot.AI.Behaviour.
  Returns empty/default responses for all AI operations.
  Used when no API keys are available.
  """

  @behaviour Repobot.AI.Behaviour

  require Logger

  @impl true
  def infer_tags(_source_file, _organization) do
    Logger.debug("No AI backend available, returning empty tags")
    {:ok, []}
  end

  @impl true
  def infer_categories(_source_files, _organization) do
    Logger.debug("No AI backend available, returning empty categories")
    {:ok, %{}}
  end

  @impl true
  def generate_template(_file1, _file2, _file_path, _available_variables, _organization) do
    Logger.debug("No AI backend available, cannot generate template")
    {:error, "No AI backend available"}
  end
end
