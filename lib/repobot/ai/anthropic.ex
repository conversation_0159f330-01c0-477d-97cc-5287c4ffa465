defmodule Repobot.AI.Anthropic do
  @moduledoc """
  Anthropic implementation of the Repobot.AI.Behaviour.
  Uses Anthropic's Claude API to provide AI features.
  """

  @behaviour Repobot.AI.Behaviour

  require Logger

  @impl true
  def infer_tags(source_file, organization) do
    with {:ok, api_key} <- get_api_key(organization),
         {:ok, prompt} <- build_prompt(source_file),
         {:ok, response} <- call_anthropic(prompt, api_key) do
      parse_tags(response, source_file)
    end
  end

  @impl true
  def infer_categories(source_files, organization) do
    with {:ok, api_key} <- get_api_key(organization),
         {:ok, prompt} <- build_categories_prompt(source_files),
         {:ok, response} <- call_anthropic(prompt, api_key) do
      parse_categories(response)
    end
  end

  @impl true
  def generate_template(file1, file2, file_path, available_variables, organization) do
    with {:ok, api_key} <- get_api_key(organization),
         prompt <- build_template_prompt(file1, file2, file_path, available_variables),
         {:ok, response} <- call_anthropic(prompt, api_key) do
      # Extract the template from the response
      template =
        case Regex.run(~r/```(?:yaml|liquid)?\s*\n(.*?)\n```\s*$/s, response) do
          [_, extracted] -> String.trim(extracted)
          nil -> String.trim(response)
        end

      Logger.debug("Generated template:\n#{template}")
      {:ok, template}
    end
  end

  defp get_api_key(organization) do
    case organization do
      %{settings: %{anthropic_api_key: key}} when is_binary(key) and key != "" ->
        {:ok, key}

      _ ->
        {:error, "No Anthropic API key configured"}
    end
  end

  defp build_categories_prompt(source_files) do
    files_info =
      Enum.map(source_files, fn file ->
        """
        ID: #{file.id}
        Name: #{file.name}
        Path: #{file.target_path}
        Tags: #{Enum.map_join(file.tags, ", ", & &1.name)}
        """
      end)
      |> Enum.join("\n\n")

    prompt = """
    Please analyze these source files and suggest appropriate categories for them.
    Each file should be assigned to exactly one category.

    Files to categorize:
    #{files_info}

    Rules for categories:
    1. Use clear, descriptive names
    2. Keep names concise (1-3 words)
    3. Use Title Case
    4. Common categories: "Documentation", "Configuration", "CI/CD", "Scripts", "Templates", etc.
    5. Be consistent across similar files

    For each file, respond with one line in this exact format:
    file_id:Category Name

    Example response format:
    abc123:Documentation
    def456:CI/CD
    ghi789:Configuration

    Only include the ID:Category pairs, one per line, nothing else.
    """

    {:ok, prompt}
  end

  defp parse_categories(response) do
    response
    |> String.split("\n")
    |> Enum.map(&String.trim/1)
    |> Enum.reject(&(&1 == ""))
    |> Enum.reduce({:ok, %{}}, fn line, {:ok, acc} ->
      case String.split(line, ":", parts: 2) do
        [id, category] -> {:ok, Map.put(acc, id, String.trim(category))}
        _ -> {:error, "Invalid response format"}
      end
    end)
  end

  defp build_prompt(source_file) do
    # Get repository languages
    languages =
      source_file.repositories
      |> Enum.map(& &1.language)
      |> Enum.uniq()
      |> Enum.reject(&is_nil(&1))
      |> Enum.join(", ")

    # Get folder names to exclude from tags
    folder_names =
      source_file.repositories
      |> Enum.map(fn repo ->
        case repo.folder do
          nil -> nil
          folder -> folder.name
        end
      end)
      |> Enum.reject(&is_nil(&1))
      |> Enum.uniq()
      |> Enum.join(", ")

    # Get all existing tags for the organization
    existing_tags =
      Repobot.Tags.list_organization_tag_names(source_file.organization_id)
      |> Enum.join(", ")

    prompt = """
    Please analyze this source file and suggest appropriate category tags.
    Consider the following information:

    File name: #{source_file.name}
    Target path: #{source_file.target_path}
    Repository languages: #{languages}

    Existing tags in the system: #{existing_tags}

    IMPORTANT RULES:
    1. STRONGLY PREFER using existing tags from the list above
    2. Only suggest new tags if none of the existing tags are suitable
    3. DO NOT suggest tags that match these folder names: #{folder_names}
    4. Return EXACTLY 3 most relevant tags, no more, no less

    Each tag MUST:
    - Be lowercase
    - Use hyphens to separate words (e.g. github-actions, continuous-integration)
    - Contain no special characters except hyphens
    - Be either a single word or hyphenated compound words

    Return only the tags, one per line, no explanation.
    """

    {:ok, prompt}
  end

  defp call_anthropic(prompt, api_key) do
    url = "https://api.anthropic.com/v1/messages"
    Logger.info("Calling Anthropic API")

    headers = [
      {"x-api-key", api_key},
      {"anthropic-version", "2023-06-01"},
      {"content-type", "application/json"}
    ]

    body = %{
      model: "claude-3-7-sonnet-20250219",
      max_tokens: 1024,
      messages: [
        %{
          role: "user",
          content: prompt
        }
      ]
    }

    case Req.post(url, json: body, headers: headers) do
      {:ok, %{status: 200, body: %{"content" => [%{"text" => text} | _]}}} ->
        Logger.info("Successfully received response from Anthropic")
        Logger.debug("Response content:\n#{text}")
        {:ok, text}

      {:ok, %{status: status, body: %{"error" => %{"message" => message}}}} ->
        Logger.error("Anthropic API error: #{status} - #{message}")
        {:error, "Anthropic API error: #{message}"}

      {:ok, %{status: status, body: body}} ->
        Logger.error("Anthropic API error: #{status} - #{inspect(body)}")
        {:error, "Anthropic API error: #{status}"}

      {:error, error} ->
        Logger.error("Anthropic API request failed: #{inspect(error)}")
        {:error, "Failed to call Anthropic API"}
    end
  end

  defp parse_tags(response, source_file) do
    # Get folder names for filtering
    folder_names =
      source_file.repositories
      |> Enum.map(fn repo ->
        case repo.folder do
          nil -> nil
          folder -> String.downcase(folder.name)
        end
      end)
      |> Enum.reject(&is_nil(&1))
      |> MapSet.new()

    # Get tags from response and filter out any that match folder names
    tags =
      response
      |> String.split("\n")
      |> Enum.map(&String.trim/1)
      |> Enum.reject(fn tag ->
        # Reject empty lines, folder names, and lines that don't match tag format
        # Only accept tags that are lowercase and contain only letters, numbers, and hyphens
        tag == "" ||
          MapSet.member?(folder_names, String.downcase(tag)) ||
          !Regex.match?(~r/^[a-z0-9-]+$/, tag)
      end)
      |> Enum.take(5)

    {:ok, tags}
  end

  defp build_template_prompt(file1, file2, file_path, available_variables) do
    vars_info =
      for {var, val} <- available_variables do
        "#{var}: #{val}"
      end
      |> Enum.join("\n")

    # Determine if this is a GitHub Actions workflow file
    is_workflow = String.contains?(file_path, ".github/workflows/")
    Logger.info("Template type: #{if is_workflow, do: "GitHub Actions", else: "Generic"}")
    Logger.info("File path: #{file_path}")

    prompt =
      if is_workflow do
        Logger.info("Using GitHub Actions workflow prompt")
        build_github_actions_prompt(file1, file2, vars_info)
      else
        Logger.info("Using generic prompt")
        build_generic_prompt(file1, file2, vars_info)
      end

    Logger.debug("Generated prompt:\n#{prompt}")
    # Return just the prompt text
    prompt
  end

  defp build_github_actions_prompt(file1, file2, vars_info) do
    """
    You are a template generator specializing in GitHub Actions workflows.
    Your task is to create a Liquid template that can generate both of these workflow files.

    File 1:
    ```yaml
    #{file1}
    ```

    File 2:
    ```yaml
    #{file2}
    ```

    Available variables:
    #{vars_info}

    STRICT REQUIREMENTS FOR GITHUB ACTIONS WORKFLOWS:
    1. For matrix configurations in strategy:
       - ALWAYS replace version arrays with settings variables using loops
       - For Elixir versions: elixir_versions
       - For OTP versions: otp_versions
       - KEEP the YAML list structure with proper indentation
       - DO NOT add any conditional logic to matrix values
       - DO NOT modify the matrix structure

    Example - REQUIRED transformation:
    Input:
        strategy:
          matrix:
            elixir:
              - "1.14.5"
              - "1.15.7"
              - "1.16.3"
            otp:
              - "26.2"
              - "26.1"
              - "25.3"

    Output:
        strategy:
          matrix:
            elixir:
              {% for version in elixir_versions -%}
              - "{{ version }}"
              {% endfor %}
            otp:
              {% for version in otp_versions -%}
              - "{{ version }}"
              {% endfor %}

    FORBIDDEN PATTERNS:
    1. DO NOT use inline array syntax
    2. DO NOT add conditionals to matrix values
    3. DO NOT modify any other parts of the workflow unless they differ between files
    4. DO NOT change job names, step names, or other structural elements
    5. DO NOT add comments or explanations in the template

    Return only the template content with no explanation or additional text.
    """
  end

  defp build_generic_prompt(file1, file2, vars_info) do
    """
    You are a template generator specializing in generic templates.
    Your task is to create a Liquid template that can generate both of these files.

    File 1:
    ```yaml
    #{file1}
    ```

    File 2:
    ```yaml
    #{file2}
    ```

    Available variables:
    #{vars_info}

    Return only the template content with no explanation or additional text.
    """
  end
end
