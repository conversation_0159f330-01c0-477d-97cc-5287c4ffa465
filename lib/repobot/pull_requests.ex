defmodule Repobot.PullRequests do
  @moduledoc """
  The PullRequests context.
  """

  import Ecto.Query, warn: false
  alias <PERSON>obot.Repo

  alias Repobot.PullRequest

  @doc """
  Returns a pull request matching the given options.
  """
  def get_pull_request_by(opts) do
    Repo.get_by(PullRequest, opts)
  end

  @doc """
  Updates a pull request with the given attributes.
  """
  def update_pull_request(%PullRequest{} = pull_request, attrs) do
    pull_request
    |> PullRequest.changeset(attrs)
    |> Repo.update()
  end
end
