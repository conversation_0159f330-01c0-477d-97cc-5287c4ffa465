defmodule Repobot.Accounts do
  alias <PERSON>obot.Repo
  alias Repobot.Accounts.{User, Organization, OrganizationSettings, UserOrganization}
  import Ecto.Query
  require Logger

  # Six months in seconds
  @default_refresh_token_expiry 15_897_600

  @doc """
  Gets a user by ID. Raises if the user is not found.
  """
  def get_user!(id), do: Repo.get!(User, id)

  @doc """
  Gets a user by login (nickname). Returns the user or nil if not found.
  """
  def get_user_by_login(login) when is_binary(login) do
    Repo.get_by(User, login: login)
  end

  @doc """
  Updates a user's settings.
  Returns {:ok, user} on success or {:error, changeset} on failure.
  """
  def update_user_settings(%User{} = user, settings_params) do
    user
    |> User.changeset(%{settings: settings_params})
    |> Repo.update()
  end

  @doc """
  Gets all organizations for a user from the database.
  This is a lightweight version that doesn't hit the GitHub API.
  """
  def get_user_organizations_from_db(%User{} = user) do
    # Get all organizations from the database that the user has access to
    organizations =
      from(o in Organization,
        join: uo in UserOrganization,
        on: uo.organization_id == o.id,
        where: uo.user_id == ^user.id,
        preload: [:settings]
      )
      |> Repo.all()

    {:ok, organizations}
  end

  @doc """
  Updates an organization's settings.
  Returns {:ok, organization} on success or {:error, changeset} on failure.
  """
  def update_organization_settings(%Organization{} = organization, settings_params) do
    settings = organization.settings || %OrganizationSettings{organization_id: organization.id}

    settings
    |> OrganizationSettings.changeset(settings_params)
    |> Repo.insert_or_update()
  end

  defp create_or_update_organizations(_client, installations, user) do
    user_login = user.login

    Logger.debug(
      "User #{user_login} - Processing #{length(installations)} installations: #{inspect(installations)}"
    )

    # First, check if any installation matches the user's default organization (personal account)
    default_org_installation =
      Enum.find(installations, fn installation ->
        account = get_in(installation, ["account"])
        account_login = get_in(account, ["login"])

        # The default organization has the same name as the user's login
        account_login == user_login
      end)

    # If we found a matching installation for the default organization, update it
    if default_org_installation do
      installation_id = get_in(default_org_installation, ["id"])
      account = get_in(default_org_installation, ["account"])

      Logger.debug(
        "User #{user_login} - Found installation for default organization with ID: #{installation_id}"
      )

      # Find the default organization - try multiple approaches
      default_org = find_or_create_default_organization(user)

      if default_org do
        Logger.debug(
          "User #{user_login} - Updating installation_id for default organization: #{default_org.name}"
        )

        # Update the default organization with the installation ID
        case Organization.changeset(default_org, %{installation_id: installation_id})
             |> Repo.update() do
          {:ok, updated_org} ->
            Logger.debug(
              "User #{user_login} - Successfully updated installation_id for default organization"
            )

            # If the user doesn't have a default_organization_id set, update it now
            if is_nil(user.default_organization_id) do
              Logger.debug(
                "User #{user_login} - Setting default_organization_id to #{updated_org.id}"
              )

              {:ok, _updated_user} =
                User.changeset(user, %{default_organization_id: updated_org.id})
                |> Repo.update()
            end

            # Update settings if needed
            settings = Repo.preload(updated_org, :settings).settings

            if settings do
              settings_attrs = %{
                avatar_url: get_in(account, ["avatar_url"]),
                html_url: get_in(account, ["html_url"])
              }

              {:ok, _} =
                settings
                |> OrganizationSettings.changeset(
                  Map.merge(settings_attrs, %{
                    organization_id: updated_org.id,
                    anthropic_api_key: settings.anthropic_api_key,
                    openai_api_key: settings.openai_api_key
                  })
                )
                |> Repo.insert_or_update()
            end

          {:error, changeset} ->
            Logger.error(
              "User #{user_login} - Failed to update installation_id for default organization: #{inspect(changeset.errors)}"
            )
        end
      else
        Logger.error("User #{user_login} - Could not find or create default organization")
      end
    end

    # Process all installations as before
    Enum.each(installations, fn installation ->
      account = get_in(installation, ["account"])
      account_type = get_in(account, ["type"])
      account_login = get_in(account, ["login"])

      Logger.debug(
        "User #{user_login} - Checking installation for account: #{account_login} (Type: #{account_type})"
      )

      # Check account type *before* attempting membership check
      if account_type == "Organization" do
        # Alias for clarity
        org_login = account_login

        # Since the user has installed the app in these organizations,
        # we can safely assume they have access. Skip membership check.
        Logger.debug(
          "User #{user_login} - Processing organization with GitHub App installation: #{org_login}"
        )

        # The user needs to be either admin or member, default to member for safety
        # GitHub App installation proves they have access to the org
        role = "member"

        # Prepare data for org creation/update
        org_attrs = %{
          name: org_login,
          installation_id: get_in(installation, ["id"])
        }

        settings_attrs = %{
          avatar_url: get_in(installation, ["account", "avatar_url"]),
          html_url: get_in(installation, ["account", "html_url"])
        }

        # Create or update the org with the user
        create_or_update_org_with_user(user, org_attrs, settings_attrs, role)
      else
        # For User accounts (personal accounts), we've already handled the default organization above
        # so we don't need to do anything special here
        Logger.debug(
          "User #{user_login} - Account type '#{account_type}' for #{account_login} is not Organization. Already processed if it's the default organization."
        )
      end
    end)
  end

  # Helper to find or create a default organization for a user
  defp find_or_create_default_organization(user) do
    user_login = user.login

    # Try to find the default organization using the user's default_organization_id
    default_org =
      if user.default_organization_id do
        Logger.debug(
          "User #{user_login} - Looking up default organization by ID: #{user.default_organization_id}"
        )

        Repo.get(Organization, user.default_organization_id)
      end

    # If not found by ID, try to find by name (which should match the user's login)
    default_org =
      if is_nil(default_org) do
        Logger.debug(
          "User #{user_login} - Looking up default organization by name: #{user_login}"
        )

        Repo.get_by(Organization, name: user_login)
      else
        default_org
      end

    # If still not found, create a new default organization
    if is_nil(default_org) do
      Logger.debug("User #{user_login} - Creating new default organization")

      case %Organization{}
           |> Organization.changeset(%{name: user_login})
           |> Repo.insert() do
        {:ok, new_org} ->
          # Create settings for the new organization
          {:ok, _settings} =
            %OrganizationSettings{}
            |> OrganizationSettings.changeset(%{
              organization_id: new_org.id
            })
            |> Repo.insert()

          # Create user-organization association with admin role
          {:ok, _} =
            %UserOrganization{}
            |> UserOrganization.changeset(%{
              user_id: user.id,
              organization_id: new_org.id,
              role: "admin"
            })
            |> Repo.insert()

          new_org

        {:error, changeset} ->
          Logger.error(
            "User #{user_login} - Failed to create default organization: #{inspect(changeset.errors)}"
          )

          nil
      end
    else
      default_org
    end
  end

  # Helper to create or update organization and associate user
  defp create_or_update_org_with_user(user, org_attrs, settings_attrs, role) do
    user_login = user.login
    org_login = org_attrs.name

    # Org creation/update logic
    case Repo.get_by(Organization, name: org_attrs.name) |> Repo.preload(:settings) do
      nil ->
        # Create organization first
        Logger.debug("User #{user_login} - Creating new organization: #{org_login}")

        case %Organization{}
             |> Organization.changeset(org_attrs)
             |> Repo.insert() do
          {:ok, org} ->
            # Create settings for the new organization
            Logger.debug(
              "User #{user_login} - Creating settings for new organization: #{org_login}"
            )

            {:ok, _settings} =
              %OrganizationSettings{}
              |> OrganizationSettings.changeset(Map.put(settings_attrs, :organization_id, org.id))
              |> Repo.insert()

            # Create user-organization association with the correct role
            Logger.debug(
              "User #{user_login} - Creating UserOrganization link for #{org_login} with role #{role}"
            )

            {:ok, _} =
              %UserOrganization{}
              |> UserOrganization.changeset(%{
                user_id: user.id,
                organization_id: org.id,
                role: role
              })
              |> Repo.insert()

          {:error, changeset} ->
            Logger.error(
              "User #{user_login} - Failed to create organization #{org_login}: #{inspect(changeset.errors)}"
            )
        end

      org ->
        # Update organization
        Logger.debug("User #{user_login} - Updating existing organization: #{org_login}")

        case org
             |> Organization.changeset(org_attrs)
             |> Repo.update() do
          {:ok, updated_org} ->
            # Ensure settings exist before updating
            settings =
              Repo.preload(updated_org, :settings).settings ||
                %OrganizationSettings{organization_id: updated_org.id}

            Logger.debug("User #{user_login} - Updating settings for organization: #{org_login}")

            {:ok, _settings} =
              settings
              |> OrganizationSettings.changeset(
                Map.merge(settings_attrs, %{
                  organization_id: updated_org.id,
                  anthropic_api_key: settings.anthropic_api_key,
                  openai_api_key: settings.openai_api_key
                })
              )
              |> Repo.insert_or_update()

            # Create or update user-organization association with the correct role
            Logger.debug(
              "User #{user_login} - Checking/Updating UserOrganization link for #{org_login} with role #{role}"
            )

            case Repo.get_by(UserOrganization,
                   user_id: user.id,
                   organization_id: updated_org.id
                 ) do
              nil ->
                Logger.debug(
                  "User #{user_login} - Creating new UserOrganization link for updated org #{org_login}"
                )

                {:ok, _} =
                  %UserOrganization{}
                  |> UserOrganization.changeset(%{
                    user_id: user.id,
                    organization_id: updated_org.id,
                    role: role
                  })
                  |> Repo.insert()

              existing ->
                # Update role if it has changed
                if existing.role != role do
                  Logger.debug(
                    "User #{user_login} - Updating role in UserOrganization link for #{org_login} from #{existing.role} to #{role}"
                  )

                  {:ok, _} =
                    existing
                    |> UserOrganization.changeset(%{role: role})
                    |> Repo.update()
                else
                  Logger.debug(
                    "User #{user_login} - Role unchanged for UserOrganization link #{org_login}"
                  )
                end
            end

          {:error, changeset} ->
            Logger.error(
              "User #{user_login} - Failed to update organization #{org_login}: #{inspect(changeset.errors)}"
            )
        end
    end
  end

  def user_from_auth(auth) do
    now = System.system_time(:second)
    refresh_token_expires_at = now + @default_refresh_token_expiry
    github_login = auth.info.nickname

    Logger.info("Processing user_from_auth for GitHub user: #{github_login}")

    case Repo.get_by(User, login: github_login) do
      nil ->
        # Create default organization for the user
        Logger.info("Creating new user account for: #{github_login}")

        case %Organization{}
             |> Organization.changeset(%{name: github_login})
             |> Repo.insert() do
          {:ok, organization} ->
            # Create settings for the organization
            {:ok, _settings} =
              %OrganizationSettings{}
              |> OrganizationSettings.changeset(%{
                organization_id: organization.id,
                avatar_url: auth.info.image,
                html_url: auth.info.urls["html_url"]
              })
              |> Repo.insert()

            # Reload organization with settings
            organization = Repo.preload(organization, :settings)

            user_result =
              %User{
                login: github_login,
                token: auth.credentials.token,
                refresh_token: auth.credentials.refresh_token,
                expires_at: auth.credentials.expires_at,
                refresh_token_expires_at: refresh_token_expires_at,
                default_organization_id: organization.id,
                info: %{
                  name: auth.info.name,
                  email: auth.info.email,
                  nickname: github_login,
                  description: auth.info.description,
                  avatar_url: auth.info.image,
                  html_url: auth.info.urls["html_url"],
                  location: auth.info.location
                }
              }
              |> User.changeset()
              |> Repo.insert()

            case user_result do
              {:ok, user} ->
                # Create user-organization association with admin role for default org
                {:ok, _} =
                  %UserOrganization{}
                  |> UserOrganization.changeset(%{
                    user_id: user.id,
                    organization_id: organization.id,
                    role: "admin"
                  })
                  |> Repo.insert()

                # Fetch and create organizations where the GitHub App is installed
                Logger.info("Creating GitHub client for new user: #{github_login}")
                client = github_api().client(user)

                Logger.info("Fetching GitHub App installations for: #{github_login}")

                case github_api().list_user_installations(client) do
                  {:ok, installations} ->
                    Logger.info(
                      "Found #{length(installations)} GitHub App installations for user: #{github_login}"
                    )

                    create_or_update_organizations(client, installations, user)

                  {:error, reason} ->
                    Logger.error(
                      "Failed to fetch GitHub App installations for #{github_login}: #{reason}"
                    )
                end

                {:ok, user}

              {:error, changeset} ->
                Logger.error(
                  "Failed to create user #{github_login}: #{inspect(changeset.errors)}"
                )

                {:error, "Failed to create user account"}
            end

          {:error, changeset} ->
            Logger.error(
              "Failed to create organization for #{github_login}: #{inspect(changeset.errors)}"
            )

            {:error, "Failed to create organization"}
        end

      user ->
        Logger.info("Updating existing user: #{github_login}")

        case User.changeset(user, %{
               token: auth.credentials.token,
               refresh_token: auth.credentials.refresh_token,
               expires_at: auth.credentials.expires_at,
               refresh_token_expires_at: refresh_token_expires_at,
               info: %{
                 name: auth.info.name,
                 email: auth.info.email,
                 nickname: github_login,
                 description: auth.info.description,
                 avatar_url: auth.info.image,
                 html_url: auth.info.urls["html_url"],
                 location: auth.info.location
               }
             })
             |> Repo.update() do
          {:ok, updated_user} ->
            # Fetch and create organizations where the GitHub App is installed
            Logger.info("Creating GitHub client for existing user: #{github_login}")
            client = github_api().client(updated_user)

            Logger.info("Fetching GitHub App installations for existing user: #{github_login}")

            case github_api().list_user_installations(client) do
              {:ok, installations} ->
                Logger.info(
                  "Found #{length(installations)} GitHub App installations for existing user: #{github_login}"
                )

                create_or_update_organizations(client, installations, updated_user)

              {:error, reason} ->
                Logger.error(
                  "Failed to fetch GitHub App installations for existing user #{github_login}: #{reason}"
                )
            end

            {:ok, updated_user}

          {:error, changeset} ->
            Logger.error("Failed to update user #{github_login}: #{inspect(changeset.errors)}")
            {:error, "Failed to update user account"}
        end
    end
  end

  @doc """
  Refreshes a user's GitHub OAuth token using their refresh token.
  Returns {:ok, updated_user} on success or {:error, reason} on failure.
  """
  def refresh_user_token(%User{refresh_token: refresh_token} = user)
      when not is_nil(refresh_token) do
    Logger.debug("Refreshing GitHub OAuth token for user #{user.login}")

    client_id = Application.get_env(:ueberauth, Ueberauth.Strategy.Github.OAuth)[:client_id]

    client_secret =
      Application.get_env(:ueberauth, Ueberauth.Strategy.Github.OAuth)[:client_secret]

    req_options =
      [
        base_url: "https://github.com",
        url: "/login/oauth/access_token",
        headers: [{"accept", "application/json"}],
        json: %{
          client_id: client_id,
          client_secret: client_secret,
          grant_type: "refresh_token",
          refresh_token: refresh_token
        }
      ]
      |> Keyword.merge(Application.get_env(:repobot, :github_oauth_req_options, []))

    case Req.post(req_options) do
      {:ok,
       %{
         status: 200,
         body: %{
           "access_token" => new_token,
           "refresh_token" => new_refresh_token,
           "expires_in" => expires_in,
           "refresh_token_expires_in" => refresh_token_expires_in,
           "token_type" => "bearer"
         }
       }} ->
        now = System.system_time(:second)

        new_expires_at = now + expires_in
        new_refresh_token_expires_at = now + refresh_token_expires_in

        user
        |> User.changeset(%{
          token: new_token,
          refresh_token: new_refresh_token,
          expires_at: new_expires_at,
          refresh_token_expires_at: new_refresh_token_expires_at
        })
        |> Repo.update()

      {:ok, %{status: status, body: body}} ->
        Logger.error("Failed to refresh GitHub OAuth token: #{status} - #{inspect(body)}")
        {:error, "Failed to refresh token: unexpected response structure"}

      {:error, reason} ->
        Logger.error("HTTP error refreshing GitHub OAuth token: #{inspect(reason)}")
        {:error, "Failed to refresh token"}
    end
  end

  def refresh_user_token(%User{} = user) do
    Logger.error("Cannot refresh token for user #{user.login}: no refresh token available")
    {:error, "No refresh token available"}
  end

  defp github_api do
    github_api_module = Application.get_env(:repobot, :github_api)
    Logger.debug("Using GitHub API module: #{inspect(github_api_module)}")
    github_api_module
  end

  @doc """
  Returns a changeset for tracking organization changes.
  """
  def change_organization(%Organization{} = organization, attrs \\ %{}) do
    Organization.changeset(organization, attrs)
  end

  @doc """
  Updates an organization.
  """
  def update_organization(%Organization{} = organization, attrs) do
    organization
    |> Organization.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Returns a changeset for tracking user changes.
  """
  def change_user(%User{} = user, attrs \\ %{}) do
    User.changeset(user, attrs)
  end

  @doc """
  Updates a user.
  """
  def update_user(%User{} = user, attrs) do
    user
    |> User.changeset(attrs)
    |> Repo.update()
  end

  def list_users do
    Repo.all(User)
  end

  def list_organizations do
    Organization
    |> preload([:settings])
    |> Repo.all()
  end

  @doc """
  Adds a user to an organization with the specified role.
  Returns {:ok, user_organization} on success or {:error, changeset} on failure.
  """
  def add_user_to_organization(%User{} = user, %Organization{} = organization, role) do
    %UserOrganization{}
    |> UserOrganization.changeset(%{
      user_id: user.id,
      organization_id: organization.id,
      role: role
    })
    |> Repo.insert()
  end

  @doc """
  Removes a user from an organization by deleting the UserOrganization record.
  Returns {:ok, user_organization} on success or {:error, changeset} on failure.
  """
  def remove_user_from_organization(%User{} = user, %Organization{} = organization) do
    case Repo.get_by(UserOrganization, user_id: user.id, organization_id: organization.id) do
      nil ->
        {:error, :not_found}

      user_organization ->
        Repo.delete(user_organization)
    end
  end

  @doc """
  Gets an organization by its name.
  """
  def get_organization_by_name(name) do
    Repo.get_by(Organization, name: name)
  end
end
