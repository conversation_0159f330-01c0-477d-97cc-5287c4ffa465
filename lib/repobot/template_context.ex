defmodule Repobot.TemplateContext do
  @moduledoc """
  Provides context data for rendering templates with repository information.
  """

  @doc """
  Formats repository data for use in templates.
  Returns a map with flattened keys for easy access in templates.

  ## Example:

      iex> format_repository_data(%{"name" => "repo", "owner" => %{"login" => "user"}})
      %{
        "name" => "repo",
        "owner" => "user",
        "full_name" => "user/repo",
        # ... other fields
      }
  """
  def format_repository_data(repo) do
    %{
      # Basic info
      "name" => repo["name"],
      "full_name" => repo["full_name"],
      "description" => repo["description"],
      "homepage" => repo["homepage"],
      "language" => repo["language"],
      "default_branch" => repo["default_branch"],
      "topics" => repo["topics"] || [],
      "license" => get_in(repo, ["license", "name"]),
      "license_key" => get_in(repo, ["license", "key"]),

      # Repository stats
      "stars" => repo["stargazers_count"],
      "watchers" => repo["watchers_count"],
      "forks" => repo["forks_count"],
      "open_issues" => repo["open_issues_count"],

      # Dates
      "created_at" => repo["created_at"],
      "updated_at" => repo["updated_at"],
      "pushed_at" => repo["pushed_at"],

      # URLs
      "html_url" => repo["html_url"],
      "clone_url" => repo["clone_url"],
      "git_url" => repo["git_url"],
      "ssh_url" => repo["ssh_url"],

      # Owner info
      "owner" => repo["owner"]["login"],
      "owner_name" => repo["owner"]["login"],
      "owner_type" => repo["owner"]["type"],
      "owner_url" => repo["owner"]["html_url"],
      "owner_avatar" => repo["owner"]["avatar_url"],

      # Repository flags
      "private" => repo["private"],
      "fork" => repo["fork"],
      "archived" => repo["archived"],
      "disabled" => repo["disabled"],
      "has_wiki" => repo["has_wiki"],
      "has_pages" => repo["has_pages"],
      "has_issues" => repo["has_issues"],
      "has_projects" => repo["has_projects"],
      "has_downloads" => repo["has_downloads"],
      "has_discussions" => repo["has_discussions"]
    }
  end

  @doc """
  Returns a list of available template variables with their descriptions.
  """
  def available_variables do
    [
      {"name", "Repository name"},
      {"full_name", "Full repository name (owner/name)"},
      {"description", "Repository description"},
      {"homepage", "Homepage URL"},
      {"language", "Primary programming language"},
      {"default_branch", "Default branch name"},
      {"topics", "List of repository topics"},
      {"license", "License name"},
      {"license_key", "License identifier"},
      {"stars", "Number of stargazers"},
      {"watchers", "Number of watchers"},
      {"forks", "Number of forks"},
      {"open_issues", "Number of open issues"},
      {"created_at", "Repository creation date"},
      {"updated_at", "Last update date"},
      {"pushed_at", "Last push date"},
      {"html_url", "GitHub repository URL"},
      {"clone_url", "HTTPS clone URL"},
      {"git_url", "Git protocol URL"},
      {"ssh_url", "SSH clone URL"},
      {"owner", "Repository owner username"},
      {"owner_name", "Repository owner username (alias)"},
      {"owner_type", "Owner type (User/Organization)"},
      {"owner_url", "Owner's GitHub profile URL"},
      {"owner_avatar", "Owner's avatar URL"},
      {"private", "Whether the repository is private"},
      {"fork", "Whether the repository is a fork"},
      {"archived", "Whether the repository is archived"},
      {"disabled", "Whether the repository is disabled"},
      {"has_wiki", "Whether the repository has a wiki"},
      {"has_pages", "Whether the repository has GitHub Pages"},
      {"has_issues", "Whether the repository has issues enabled"},
      {"has_projects", "Whether the repository has projects enabled"},
      {"has_downloads", "Whether the repository has downloads enabled"},
      {"has_discussions", "Whether the repository has discussions enabled"}
    ]
  end

  @doc """
  Returns a list of commonly used template variables with their descriptions.
  These are the most frequently needed variables for basic templates.
  """
  def common_variables do
    [
      {"owner", "Repository owner username"},
      {"name", "Repository name"},
      {"full_name", "Full repository name (owner/name)"},
      {"description", "Repository description"},
      {"html_url", "GitHub repository URL"},
      {"clone_url", "HTTPS clone URL"},
      {"ssh_url", "SSH clone URL"},
      {"language", "Primary programming language"},
      {"license", "License name"},
      {"default_branch", "Default branch name"}
    ]
  end
end
