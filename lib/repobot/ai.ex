defmodule Repobot.AI do
  @moduledoc """
  Main module for AI operations in Repobot.
  """

  require Logger

  @doc """
  Returns the configured AI implementation based on available API keys.
  Checks for API key availability in the following order:
  1. Organization-specific API keys (Anthropic, then OpenAI)
  2. Falls back to no-op backend when no API keys are available

  In test environment, always uses the configured backend (mock).
  """
  def backend(organization \\ nil) do
    # In test environment, always use the configured backend (mock)
    if Application.get_env(:repobot, :env) == :test do
      Application.get_env(:repobot, :ai_backend)
    else
      case select_backend_by_api_key_availability(organization) do
        nil ->
          # Fall back to no-op backend when no API keys are available
          Logger.debug("No API keys available, using no-op backend")
          Repobot.AI.NoOp

        backend ->
          Logger.debug("Selected AI backend based on API key availability: #{backend}")
          backend
      end
    end
  end

  @doc """
  Returns the configured AI implementation.
  Defaults to OpenAI implementation.

  @deprecated Use backend/1 instead for dynamic backend selection
  """
  def static_backend do
    Application.get_env(:repobot, :ai_backend)
  end

  defp select_backend_by_api_key_availability(organization) do
    cond do
      # Check organization-specific Anthropic API key
      has_organization_anthropic_key?(organization) ->
        Repobot.AI.Anthropic

      # Check organization-specific OpenAI API key
      has_organization_openai_key?(organization) ->
        Repobot.AI.OpenAI

      # No API keys available
      true ->
        nil
    end
  end

  defp has_organization_anthropic_key?(nil), do: false

  defp has_organization_anthropic_key?(organization) do
    case organization do
      %{settings: %{anthropic_api_key: key}} when is_binary(key) and key != "" -> true
      _ -> false
    end
  end

  defp has_organization_openai_key?(nil), do: false

  defp has_organization_openai_key?(organization) do
    case organization do
      %{settings: %{openai_api_key: key}} when is_binary(key) and key != "" -> true
      _ -> false
    end
  end

  # Public functions for testing the selection logic
  if Application.compile_env(:repobot, :env) == :test do
    def test_has_organization_anthropic_key?(organization) do
      has_organization_anthropic_key?(organization)
    end

    def test_has_organization_openai_key?(organization) do
      has_organization_openai_key?(organization)
    end

    def test_select_backend_by_api_key_availability(organization) do
      select_backend_by_api_key_availability(organization)
    end
  end
end
