name: CI
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  tests:
    name: Run tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - uses: actions/checkout@v4

      - name: Start db service
        run: docker compose up -d postgres

      - name: Restore deps and _build cache
        uses: actions/cache@v3
        with:
          # we exclude tls_certificate_check because its dir has no x bit and tar fails to access
          # it when creating a cache tarbal
          path: |
            _build
            deps/*
            !deps/tls_certificate_check
          key: ${{ runner.os }}-mix-${{ hashFiles('mix.lock') }}
          restore-keys: ${{ runner.os }}-mix-

      - name: Set up .env files
        run: cp .env.dev.example .env.dev

      - name: Get deps
        run: docker compose run --rm test mix deps.get

      - name: Compile app
        run: docker compose run --rm test mix compile

      - name: Run tests with coverage
        run: docker compose run --rm test mix test
