import Config

# Configure your database
config :repobot, Repobot.Repo,
  username: "postg<PERSON>",
  password: "postgres",
  hostname: System.get_env("DATABASE_HOST") || "localhost",
  database: "repobot_dev",
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: 10

# For development, we disable any cache and enable
# debugging and code reloading.
#
# The watchers configuration can be used to run external
# watchers to your application. For example, we can use it
# to bundle .js and .css sources.
config :repobot, RepobotWeb.Endpoint,
  # Binding to loopback ipv4 address prevents access from other machines.
  # Change to `ip: {0, 0, 0, 0}` to allow access from other machines.
  http: [ip: {127, 0, 0, 1}, port: 4000],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base: "DCVDtMSmkIYuXg7Ma6f8gHLG2gj8fD/qcq5TmlHiO/AfmOTJFTRfubJQj0dq3DoA",
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:repobot, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:repobot, ~w(--watch)]}
  ]

# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# Mix task:
#
#     mix phx.gen.cert
#
# Run `mix help phx.gen.cert` for more information.
#
# The `http:` config above can be replaced with:
#
#     https: [
#       port: 4001,
#       cipher_suite: :strong,
#       keyfile: "priv/cert/selfsigned_key.pem",
#       certfile: "priv/cert/selfsigned.pem"
#     ],
#
# If desired, both `http:` and `https:` keys can be
# configured to run both http and https servers on
# different ports.

# Watch static and templates for browser reloading.
config :repobot, RepobotWeb.Endpoint,
  live_reload: [
    patterns: [
      ~r"priv/static/(?!uploads/).*(js|css|png|jpeg|jpg|gif|svg)$",
      ~r"priv/gettext/.*(po)$",
      ~r"lib/repobot_web/(controllers|live|components)/.*(ex|heex)$"
    ]
  ]

# Enable dev routes for dashboard and mailbox
config :repobot, dev_routes: true

# Configure Oban Web for development
config :repobot, Oban,
  plugins: [
    Oban.Plugins.Pruner,
    {Oban.Plugins.Cron, crontab: []},
    Oban.Plugins.Lifeline
  ]

config :repobot, :github_api, Repobot.GitHub

config :repobot, :url_host, "http://localhost:4000"

config :repobot, :github_app_install_url, "https://github.com/apps/repobot-app-dev"

# Configure development logging
config :logger, level: :debug

# Configure LoggerJSON for development
# config :logger, :default_handler, :console

# Set a higher stacktrace during development. Avoid configuring such
# in production as building large stacktraces may be expensive.
config :phoenix, :stacktrace_depth, 20

# Initialize plugs at runtime for faster development compilation
config :phoenix, :plug_init_mode, :runtime

config :phoenix_live_view,
  # Include HEEx debug annotations as HTML comments in rendered markup
  debug_heex_annotations: true,
  # Enable helpful, but potentially expensive runtime checks
  enable_expensive_runtime_checks: true

# Disable swoosh api client as it is only required for production adapters.
config :swoosh, :api_client, false
