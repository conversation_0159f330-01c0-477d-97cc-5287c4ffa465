# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

config :repobot,
  ecto_repos: [Repobot.Repo],
  generators: [timestamp_type: :utc_datetime],
  ai_backend: Repobot.AI.Anthropic,
  sync_backend: Repobot.Repositories.Sync,
  env: Mix.env()

# Configure Oban
config :repobot, Oban,
  repo: Repobot.Repo,
  notifier: {Oban.Notifiers.Phoenix, pubsub: Repobot.PubSub},
  plugins: [
    Oban.Plugins.Pruner,
    {Oban.Plugins.Cron, crontab: []}
  ],
  queues: [
    default: 10,
    files: 5,
    sync: 3,
    notifications: 2
  ]

# Configures the endpoint
config :repobot, RepobotWeb.Endpoint,
  url: [host: "localhost"],
  adapter: Bandit.PhoenixAdapter,
  render_errors: [
    formats: [html: RepobotWeb.ErrorHTML, json: RepobotWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: Repobot.PubSub,
  live_view: [signing_salt: "RBlK/qvA"]

# Configures the mailer
#
# By default it uses the "Local" adapter which stores the emails
# locally. You can see the emails in your browser, at "/dev/mailbox".
#
# For production it's recommended to configure a different adapter
# at the `config/runtime.exs`.
config :repobot, Repobot.Mailer, adapter: Swoosh.Adapters.Local

# Configure esbuild (the version is required)
config :esbuild,
  version: "0.17.11",
  repobot: [
    args:
      ~w(js/app.js --bundle --target=es2022 --outdir=../priv/static/assets/js --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

# Configure tailwind (the version is required)
config :tailwind,
  version: "4.0.9",
  repobot: [
    args: ~w(
      --input=assets/css/app.css
      --output=priv/static/assets/css/app.css
    ),
    cd: Path.expand("..", __DIR__)
  ]

# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

config :ueberauth, Ueberauth,
  providers: [
    github:
      {Ueberauth.Strategy.Github,
       [
         default_scope: "user,repo,workflow,write:repo_hook",
         send_redirect_uri: true
       ]}
  ]

config :ueberauth, Ueberauth.Strategy.Github.OAuth,
  client_id: System.get_env("GITHUB_CLIENT_ID"),
  client_secret: System.get_env("GITHUB_CLIENT_SECRET"),
  default_scope: "user:email,read:org"

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"
