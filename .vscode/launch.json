{"version": "0.2.0", "configurations": [{"type": "mix_task", "name": "mix (De<PERSON>ult task)", "request": "launch", "projectDir": "${workspaceRoot}"}, {"type": "mix_task", "name": "mix test current line", "request": "launch", "task": "test", "taskArgs": ["--trace", "${file}:${lineNumber}"], "startApps": true, "projectDir": "${workspaceRoot}", "requireFiles": ["test/**/test_helper.exs", "test/**/*_test.exs"]}]}